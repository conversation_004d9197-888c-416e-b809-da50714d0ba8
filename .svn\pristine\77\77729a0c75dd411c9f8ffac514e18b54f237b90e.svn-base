# Integration Guide

This guide shows how to integrate RepoSense AI with existing Docker setups and external services.

## Docker Integration

### Integration with Existing Docker Compose

If you have existing Docker services (Ollama, LLM Proxy, Open WebUI, etc.):

#### 1. Network Integration

**Option A: Use Existing Network**
```yaml
# In your reposense-ai docker-compose.yml
networks:
  default:
    external: true
    name: ollama-network  # Your existing network name
```

**Option B: Create Shared Network**
```yaml
# Create a shared network
networks:
  shared-network:
    driver: bridge

# Reference in both compose files
services:
  reposense-ai:
    networks:
      - shared-network
```

#### 2. Service Dependencies

```yaml
services:
  reposense-ai:
    depends_on:
      - ollama-server-local
    external_links:
      - ollama-server-local:ollama
    environment:
      - OLLAMA_BASE_URL=http://ollama-server-local:11434
```

#### 3. Port Management

Avoid port conflicts by adjusting ports:

```yaml
services:
  reposense-ai:
    ports:
      - "5002:5001"  # Use different external port
```

### Example Integration Setup

Your existing `docker-compose.yml`:
```yaml
services:
  ollama-server-local:
    image: ollama/ollama
    ports:
      - "11434:11434"
    networks:
      - ollama-network

  llm-proxy-server-local:
    image: your-llm-proxy
    ports:
      - "11440:11440"
    networks:
      - ollama-network

networks:
  ollama-network:
    driver: bridge
```

RepoSense AI integration:
```yaml
services:
  reposense-ai:
    build: ./reposense_ai
    ports:
      - "5001:5000"
    environment:
      - OLLAMA_HOST=ollama-server-local
      - OLLAMA_PORT=11434
    networks:
      - ollama-network
    depends_on:
      - ollama-server-local

networks:
  ollama-network:
    external: true
```

## SVN Server Integration

### VisualSVN Server

RepoSense AI works seamlessly with VisualSVN Server:

#### Configuration
```json
{
  "repositories": [
    {
      "id": "project-repo",
      "url": "http://your-svn-server:port/svn/repository-name",
      "username": "svn-user",
      "password": "svn-password",
      "enabled": true
    }
  ],
  "server_type": "visualsvn",
  "auto_detect_server_type": true
}
```

#### Authentication
- **Basic Auth**: Username/password in configuration
- **Windows Auth**: Use domain credentials
- **Certificate Auth**: Mount certificates in Docker container

### Apache SVN Server

For Apache-based SVN servers:

```json
{
  "server_type": "apache",
  "repositories": [
    {
      "url": "http://apache-svn-server/svn/repo",
      "username": "user",
      "password": "pass"
    }
  ]
}
```

### SVN+SSH Integration

For SVN over SSH:

```json
{
  "repositories": [
    {
      "url": "svn+ssh://user@server/path/to/repo",
      "ssh_key_path": "/app/keys/id_rsa"
    }
  ]
}
```

Mount SSH keys:
```yaml
volumes:
  - ./ssh-keys:/app/keys:ro
```

## AI Service Integration

### Ollama Integration

RepoSense AI integrates with Ollama for AI-powered documentation:

#### Local Ollama
```json
{
  "ollama": {
    "host": "localhost",
    "port": 11434,
    "model": "qwen3"
  }
}
```

#### Remote Ollama
```json
{
  "ollama": {
    "host": "ollama-server.company.com",
    "port": 11434,
    "model": "codellama",
    "timeout": 120
  }
}
```

#### Ollama with Authentication
```json
{
  "ollama": {
    "host": "secure-ollama.company.com",
    "port": 443,
    "ssl": true,
    "api_key": "your-api-key"
  }
}
```

### Alternative AI Services

RepoSense AI's plugin architecture allows integration with other AI services:

#### OpenAI Integration (Future)
```json
{
  "ai_backend": "openai",
  "openai": {
    "api_key": "sk-...",
    "model": "gpt-4",
    "organization": "org-..."
  }
}
```

## Email Integration

### SMTP Configuration

```json
{
  "email": {
    "smtp_server": "smtp.company.com",
    "smtp_port": 587,
    "username": "<EMAIL>",
    "password": "email-password",
    "use_tls": true,
    "from_address": "RepoSense AI <<EMAIL>>"
  }
}
```

### Email Templates

Custom email templates can be mounted:

```yaml
volumes:
  - ./email-templates:/app/templates/email:ro
```

## Authentication Integration

### LDAP Integration

RepoSense AI provides comprehensive LDAP integration for enterprise directory synchronization. See the [LDAP Integration Guide](ldap-integration.md) for detailed configuration instructions.

**Production Configuration Example:**

```json
{
  "ldap_sync_enabled": true,
  "ldap_server": "ldap.company.com",
  "ldap_port": 389,
  "ldap_use_ssl": false,
  "ldap_bind_dn": "CN=service-account,OU=Services,DC=company,DC=com",
  "ldap_bind_password": "service-password",
  "ldap_user_base_dn": "OU=Users,DC=company,DC=com",
  "ldap_user_filter": "(objectClass=person)",
  "ldap_sync_interval": 3600,
  "ldap_username_attr": "sAMAccountName",
  "ldap_email_attr": "mail",
  "ldap_fullname_attr": "displayName",
  "ldap_phone_attr": "telephoneNumber",
  "ldap_groups_attr": "memberOf",
  "ldap_group_role_mapping": {
    "CN=RepoSense-Admins,OU=Groups,DC=company,DC=com": "ADMIN",
    "CN=RepoSense-Managers,OU=Groups,DC=company,DC=com": "MANAGER",
    "CN=Developers,OU=Groups,DC=company,DC=com": "DEVELOPER",
    "CN=RepoSense-Viewers,OU=Groups,DC=company,DC=com": "VIEWER"
  },
  "ldap_default_role": "VIEWER"
}
```

**Enterprise Features:**
- **Automatic User Provisioning**: Periodic synchronization from LDAP directory
- **Role-Based Access Control**: Automatic role assignment based on group membership
- **Attribute Synchronization**: Username, email, full name, phone, and group memberships
- **Web Management Interface**: Complete LDAP management at `/ldap-sync`
- **Connection Testing**: Real-time LDAP connectivity validation
- **Manual Sync Triggers**: On-demand synchronization with detailed statistics
- **Group Mapping Management**: Visual interface for group-to-role associations
- **Development Support**: Included test LDAP server for development and testing

### OAuth Integration (Future)

```json
{
  "authentication": {
    "type": "oauth",
    "oauth": {
      "provider": "github",
      "client_id": "your-client-id",
      "client_secret": "your-client-secret"
    }
  }
}
```

## Monitoring and Logging

### External Logging

Forward logs to external systems:

```yaml
services:
  reposense-ai:
    logging:
      driver: "syslog"
      options:
        syslog-address: "tcp://log-server:514"
        tag: "reposense-ai"
```

### Prometheus Metrics (Future)

```yaml
services:
  reposense-ai:
    ports:
      - "9090:9090"  # Metrics endpoint
    environment:
      - ENABLE_METRICS=true
```

## Backup Integration

### Configuration Backup

```bash
# Automated backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
docker cp reposense-ai:/app/data/config.json ./backups/config_$DATE.json
```

### Database Backup (Future)

When database support is added:

```yaml
services:
  backup:
    image: postgres:13
    command: pg_dump -h db -U user dbname
    volumes:
      - ./backups:/backups
```

## Security Considerations

### Network Security
- Use internal networks for service communication
- Expose only necessary ports
- Implement proper firewall rules

### Secrets Management
- Use Docker secrets for sensitive data
- Mount secrets as files, not environment variables
- Rotate credentials regularly

### SSL/TLS
- Use HTTPS for web interface
- Implement certificate management
- Enable SSL for database connections

## Troubleshooting Integration

### Network Issues
```bash
# Test network connectivity
docker exec reposense-ai ping ollama-server-local

# Check network configuration
docker network inspect ollama-network
```

### Service Discovery
```bash
# Check service resolution
docker exec reposense-ai nslookup ollama-server-local

# Test service connectivity
docker exec reposense-ai curl http://ollama-server-local:11434/api/version
```

### Log Analysis
```bash
# View integration logs
docker-compose logs reposense-ai | grep -i "ollama\|integration\|connection"
```
