{"server": {"id": "6109d0be-f51c-4260-bab8-ad4e3806c52a", "name": "default", "description": "", "base_url": "", "default_username": null, "default_password": null, "enabled": true}, "users": [], "repositories": [], "ollama_host": "http://localhost:11434", "ollama_model": "qwen3", "ollama_model_documentation": null, "ollama_model_code_review": null, "ollama_model_risk_assessment": null, "ollama_timeout_base": 300, "ollama_timeout_connection": 30, "ollama_timeout_embeddings": 60, "use_enhanced_prompts": true, "enhanced_prompts_fallback": true, "check_interval": 300, "skip_initial_scan": false, "cleanup_orphaned_documents": false, "svn_server_url": null, "svn_server_username": null, "svn_server_password": null, "svn_server_type": "auto", "smtp_host": "mailhog", "smtp_port": 1025, "smtp_username": null, "smtp_password": null, "email_from": "reposense-ai@localhost", "email_recipients": [], "output_dir": "/app/data/output", "generate_docs": true, "send_emails": true, "web_enabled": true, "web_port": 5000, "web_host": "0.0.0.0", "web_secret_key": "f64682ac0b45c44d94bd09b701654f2cfc99d5d396a5f9b67fb6b183c06d0c99", "web_log_entries": 300, "log_cleanup_max_size_mb": 50, "log_cleanup_lines_to_keep": 1000, "log_rotation_max_size_mb": 10, "log_rotation_backup_count": 5}