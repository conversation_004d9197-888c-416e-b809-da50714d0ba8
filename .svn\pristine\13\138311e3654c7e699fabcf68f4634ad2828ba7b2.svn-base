# LDAP Integration Guide

RepoSense AI supports LDAP integration for automatic user synchronization from your organization's directory service. This guide covers configuration, setup, and troubleshooting.

## Overview

The LDAP integration provides:
- **Periodic user synchronization** from LDAP directory to local database
- **Automatic role mapping** based on LDAP group membership
- **User attribute synchronization** (username, email, full name, phone)
- **Web-based management interface** for monitoring and manual sync

## Configuration

### Basic LDAP Settings

Add the following configuration to your `config.json`:

```json
{
  "ldap_sync_enabled": true,
  "ldap_server": "ldap.company.com",
  "ldap_port": 389,
  "ldap_use_ssl": false,
  "ldap_bind_dn": "CN=service-account,OU=Service Accounts,DC=company,DC=com",
  "ldap_bind_password": "service-password",
  "ldap_user_base_dn": "OU=Users,DC=company,DC=com",
  "ldap_user_filter": "(objectClass=person)",
  "ldap_sync_interval": 3600
}
```

### Attribute Mapping

Configure how LDAP attributes map to RepoSense user fields:

```json
{
  "ldap_username_attr": "sAMAccountName",
  "ldap_email_attr": "mail",
  "ldap_fullname_attr": "displayName",
  "ldap_phone_attr": "telephoneNumber",
  "ldap_groups_attr": "memberOf"
}
```

### Group to Role Mapping

Map LDAP groups to RepoSense roles:

```json
{
  "ldap_group_role_mapping": {
    "CN=RepoSense-Admins,OU=Groups,DC=company,DC=com": "ADMIN",
    "CN=RepoSense-Managers,OU=Groups,DC=company,DC=com": "MANAGER",
    "CN=Developers,OU=Groups,DC=company,DC=com": "DEVELOPER",
    "CN=RepoSense-Viewers,OU=Groups,DC=company,DC=com": "VIEWER"
  },
  "ldap_default_role": "VIEWER"
}
```

## Configuration Examples

### Active Directory (Windows)

```json
{
  "ldap_sync_enabled": true,
  "ldap_server": "ad.company.com",
  "ldap_port": 389,
  "ldap_use_ssl": false,
  "ldap_bind_dn": "CN=RepoSense Service,OU=Service Accounts,DC=company,DC=com",
  "ldap_bind_password": "SecurePassword123",
  "ldap_user_base_dn": "OU=Employees,DC=company,DC=com",
  "ldap_user_filter": "(&(objectClass=user)(objectCategory=person))",
  "ldap_username_attr": "sAMAccountName",
  "ldap_email_attr": "mail",
  "ldap_fullname_attr": "displayName",
  "ldap_phone_attr": "telephoneNumber",
  "ldap_groups_attr": "memberOf"
}
```

### OpenLDAP (Unix/Linux)

```json
{
  "ldap_sync_enabled": true,
  "ldap_server": "ldap.company.com",
  "ldap_port": 389,
  "ldap_use_ssl": false,
  "ldap_bind_dn": "cn=reposense,ou=services,dc=company,dc=com",
  "ldap_bind_password": "service-password",
  "ldap_user_base_dn": "ou=people,dc=company,dc=com",
  "ldap_user_filter": "(objectClass=inetOrgPerson)",
  "ldap_username_attr": "uid",
  "ldap_email_attr": "mail",
  "ldap_fullname_attr": "cn",
  "ldap_phone_attr": "telephoneNumber",
  "ldap_groups_attr": "memberOf"
}
```

### LDAPS (SSL/TLS)

```json
{
  "ldap_sync_enabled": true,
  "ldap_server": "ldaps.company.com",
  "ldap_port": 636,
  "ldap_use_ssl": true,
  "ldap_bind_dn": "CN=service-account,OU=Services,DC=company,DC=com",
  "ldap_bind_password": "secure-password"
}
```

## Configuration Parameters

| Parameter | Description | Default | Required |
|-----------|-------------|---------|----------|
| `ldap_sync_enabled` | Enable/disable LDAP sync | `false` | Yes |
| `ldap_server` | LDAP server hostname | - | Yes |
| `ldap_port` | LDAP server port | `389` | No |
| `ldap_use_ssl` | Use SSL/TLS connection | `false` | No |
| `ldap_bind_dn` | Service account DN | - | Yes |
| `ldap_bind_password` | Service account password | - | Yes |
| `ldap_user_base_dn` | Base DN for user search | - | Yes |
| `ldap_user_filter` | LDAP filter for users | `"(objectClass=person)"` | No |
| `ldap_sync_interval` | Sync interval in seconds | `3600` | No |
| `ldap_username_attr` | Username attribute | `"sAMAccountName"` | No |
| `ldap_email_attr` | Email attribute | `"mail"` | No |
| `ldap_fullname_attr` | Full name attribute | `"displayName"` | No |
| `ldap_phone_attr` | Phone attribute | `"telephoneNumber"` | No |
| `ldap_groups_attr` | Groups attribute | `"memberOf"` | No |
| `ldap_default_role` | Default role for users | `"VIEWER"` | No |

## User Roles

RepoSense AI supports four user roles:

- **ADMIN**: Full system administration access
- **MANAGER**: Repository and user management
- **DEVELOPER**: Full repository access and notifications
- **VIEWER**: Read-only access to documents

## Sync Behavior

### User Creation
- New LDAP users are automatically created in RepoSense
- User attributes are populated from LDAP
- Role is assigned based on group membership

### User Updates
- Existing users are updated with current LDAP data
- Email, full name, phone, and role are synchronized
- Repository associations and notification preferences are preserved

### User Deletion
- Users are NOT automatically deleted from RepoSense
- Manual cleanup may be required for deactivated LDAP users

## Web Interface

Access the LDAP management interface at `/ldap-sync` to:
- View sync status and statistics
- Test LDAP connection
- Manually trigger synchronization
- Monitor sync activity logs
- Review group-to-role mappings

## Troubleshooting

### Connection Issues

**Problem**: Cannot connect to LDAP server
```
LDAP connection failed: [Errno 111] Connection refused
```

**Solutions**:
- Verify `ldap_server` and `ldap_port` settings
- Check network connectivity and firewall rules
- Test with LDAP client tools (ldapsearch, Apache Directory Studio)

### Authentication Issues

**Problem**: Authentication failed
```
LDAP bind failed: Invalid credentials
```

**Solutions**:
- Verify `ldap_bind_dn` and `ldap_bind_password`
- Ensure service account has read permissions
- Test credentials with LDAP client tools

### Search Issues

**Problem**: No users found
```
LDAP sync completed: 0 created, 0 updated, 0 errors, 0 skipped from 0 LDAP users
```

**Solutions**:
- Verify `ldap_user_base_dn` is correct
- Check `ldap_user_filter` matches your LDAP schema
- Ensure service account has search permissions on user base DN

### Attribute Issues

**Problem**: Missing user data (email, phone, etc.)
```
User created but missing email address
```

**Solutions**:
- Verify attribute names match your LDAP schema
- Check if attributes are populated in LDAP
- Adjust attribute mapping configuration

## Security Considerations

- Use a dedicated service account with minimal required permissions
- Consider using LDAPS (SSL/TLS) for encrypted connections
- Regularly rotate service account passwords
- Monitor sync logs for suspicious activity
- Limit LDAP search scope to necessary organizational units

## Performance Tuning

- Adjust `ldap_sync_interval` based on your organization's needs
- Use specific `ldap_user_filter` to limit search scope
- Consider running sync during off-peak hours
- Monitor sync duration and adjust accordingly

## Monitoring

The LDAP sync service provides:
- Detailed logging of sync operations
- Web interface for real-time status monitoring
- Notification events for sync failures
- Statistics on user creation and updates

Check the RepoSense AI logs for detailed sync information:
```bash
docker logs reposense-ai | grep "LDAP"
```
