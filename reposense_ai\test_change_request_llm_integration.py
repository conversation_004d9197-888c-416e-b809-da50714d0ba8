#!/usr/bin/env python3
"""
Integration test for Change Request LLM Integration Analysis

This test validates the complete flow:
1. Change request data retrieval from SQL database
2. Commit-to-change-request correlation
3. LLM analysis with change request context
4. Document generation with business context
"""

import sys
import os
import tempfile
import sqlite3
from datetime import datetime, timed<PERSON>ta
from typing import List, Dict, Any

# Add the reposense_ai directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from models import CommitInfo, ChangeRequestInfo, RepositoryConfig, SqlConfig
from change_request_integration import ChangeRequestIntegration
from ollama_client import OllamaClient
from config_manager import ConfigManager

class ChangeRequestLLMIntegrationTest:
    """Integration test for change request LLM analysis"""
    
    def __init__(self):
        self.test_db_path = None
        self.integration = None
        self.ollama_client = None
        
    def setup_test_database(self) -> str:
        """Create a temporary test database with sample change request data"""
        # Create temporary database
        fd, self.test_db_path = tempfile.mkstemp(suffix='.db')
        os.close(fd)
        
        conn = sqlite3.connect(self.test_db_path)
        cursor = conn.cursor()
        
        # Create change requests table
        cursor.execute("""
            CREATE TABLE change_requests (
                id INTEGER PRIMARY KEY,
                number VARCHAR(50) UNIQUE,
                title TEXT,
                description TEXT,
                status VARCHAR(20),
                priority VARCHAR(20),
                category VARCHAR(50),
                assigned_to VARCHAR(100),
                created_date DATETIME,
                updated_date DATETIME,
                risk_level VARCHAR(20),
                business_impact TEXT,
                technical_notes TEXT
            )
        """)
        
        # Insert test change requests
        test_change_requests = [
            {
                'number': 'CR-2024-001',
                'title': 'Implement Camera Control Interval Feature',
                'description': 'Add interval setting to camera control to allow automatic picture taking at specified intervals. This will improve automation capabilities for surveillance applications.',
                'status': 'APPROVED',
                'priority': 'HIGH',
                'category': 'FEATURE',
                'assigned_to': 'fvaneijk',
                'created_date': '2016-06-15 10:00:00',
                'updated_date': '2016-06-20 15:30:00',
                'risk_level': 'MEDIUM',
                'business_impact': 'Improves automation and reduces manual intervention in surveillance systems',
                'technical_notes': 'Requires UI changes to CameraControl and MainForm. Add timer functionality.'
            },
            {
                'number': 'CR-2024-002', 
                'title': 'Database Performance Optimization',
                'description': 'Optimize database queries for better performance in high-load scenarios. Focus on indexing and query optimization.',
                'status': 'IN_PROGRESS',
                'priority': 'MEDIUM',
                'category': 'PERFORMANCE',
                'assigned_to': '<EMAIL>',
                'created_date': '2016-06-10 09:00:00',
                'updated_date': '2016-06-21 11:00:00',
                'risk_level': 'LOW',
                'business_impact': 'Reduces system response time and improves user experience',
                'technical_notes': 'Review all database queries, add appropriate indexes, consider query caching.'
            },
            {
                'number': 'CR-2024-003',
                'title': 'Security Enhancement - Input Validation',
                'description': 'Implement comprehensive input validation across all user interfaces to prevent security vulnerabilities.',
                'status': 'PENDING',
                'priority': 'CRITICAL',
                'category': 'SECURITY',
                'assigned_to': '<EMAIL>',
                'created_date': '2016-06-18 14:00:00',
                'updated_date': '2016-06-18 14:00:00',
                'risk_level': 'HIGH',
                'business_impact': 'Critical for preventing security breaches and protecting user data',
                'technical_notes': 'Audit all input fields, implement validation libraries, add sanitization.'
            }
        ]
        
        for cr in test_change_requests:
            cursor.execute("""
                INSERT INTO change_requests 
                (number, title, description, status, priority, category, assigned_to, 
                 created_date, updated_date, risk_level, business_impact, technical_notes)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                cr['number'], cr['title'], cr['description'], cr['status'], 
                cr['priority'], cr['category'], cr['assigned_to'], cr['created_date'],
                cr['updated_date'], cr['risk_level'], cr['business_impact'], cr['technical_notes']
            ))
        
        conn.commit()
        conn.close()
        
        print(f"✅ Test database created: {self.test_db_path}")
        return self.test_db_path
        
    def setup_integration(self):
        """Setup change request integration with test database"""
        sql_config = SqlConfig(
            enabled=True,
            connection_string=f"sqlite:///{self.test_db_path}",
            query_template="""
                SELECT number, title, description, status, priority, category, 
                       assigned_to, created_date, updated_date, risk_level,
                       business_impact, technical_notes
                FROM change_requests 
                WHERE assigned_to = ? OR assigned_to LIKE '%' || ? || '%'
                ORDER BY updated_date DESC
            """,
            parameter_mappings={
                "author_email": "author",
                "author_name": "author"
            }
        )
        
        self.integration = ChangeRequestIntegration(sql_config)
        print("✅ Change request integration configured")
        
    def setup_ollama_client(self):
        """Setup Ollama client for LLM testing"""
        try:
            config_manager = ConfigManager()
            config = config_manager.load_config()
            self.ollama_client = OllamaClient(config)
            
            if self.ollama_client.test_connection():
                print("✅ Ollama client connected successfully")
                return True
            else:
                print("⚠️  Ollama client connection failed - will test without LLM")
                return False
        except Exception as e:
            print(f"⚠️  Could not setup Ollama client: {e}")
            return False
    
    def create_test_commit(self) -> CommitInfo:
        """Create a test commit that should match change requests"""
        return CommitInfo(
            revision="7",
            author="fvaneijk",
            date="2016-06-21 01:44:41",
            message="Add camera interval control and start button functionality",
            changed_paths=[
                "/CaptureCam/AutoKams/Controls/CameraControl.Designer.cs",
                "/CaptureCam/AutoKams/MainForm.Designer.cs"
            ],
            diff="""@@ -15,6 +15,12 @@ namespace AutoKams.Controls
             this.btnStart = new System.Windows.Forms.Button();
             this.lblStatus = new System.Windows.Forms.Label();
             this.pnlCamera = new System.Windows.Forms.Panel();
+            this.lblInterval = new System.Windows.Forms.Label();
+            this.numInterval = new System.Windows.Forms.NumericUpDown();
+            ((System.ComponentModel.ISupportInitialize)(this.numInterval)).BeginInit();
             this.SuspendLayout();
             // 
+            // lblInterval
+            // 
+            this.lblInterval.AutoSize = true;
+            this.lblInterval.Location = new System.Drawing.Point(12, 50);
+            this.lblInterval.Name = "lblInterval";
+            this.lblInterval.Size = new System.Drawing.Size(45, 13);
+            this.lblInterval.TabIndex = 3;
+            this.lblInterval.Text = "Interval:";
+            // 
+            // numInterval
+            // 
+            this.numInterval.Location = new System.Drawing.Point(63, 48);
+            this.numInterval.Maximum = new decimal(new int[] {
+            3600,
+            0,
+            0,
+            0});
+            this.numInterval.Minimum = new decimal(new int[] {
+            1,
+            0,
+            0,
+            0});
+            this.numInterval.Name = "numInterval";
+            this.numInterval.Size = new System.Drawing.Size(120, 20);
+            this.numInterval.TabIndex = 4;
+            this.numInterval.Value = new decimal(new int[] {
+            5,
+            0,
+            0,
+            0});""",
            repository_id="visionApi",
            repository_name="visionApi"
        )
    
    def test_change_request_retrieval(self) -> bool:
        """Test change request retrieval from database"""
        print("\n🔍 Testing Change Request Retrieval")
        print("-" * 50)
        
        try:
            # Test retrieval by author
            change_requests = self.integration.get_change_requests_for_commit(
                author="fvaneijk",
                commit_message="Add camera interval control",
                changed_files=["/CaptureCam/AutoKams/Controls/CameraControl.Designer.cs"]
            )
            
            print(f"📊 Found {len(change_requests)} change requests for author 'fvaneijk'")
            
            for cr in change_requests:
                print(f"   - {cr.number}: {cr.title}")
                print(f"     Status: {cr.status}, Priority: {cr.priority}")
                print(f"     Category: {cr.category}, Risk: {cr.risk_level}")
            
            # Verify we found the expected change request
            expected_cr = next((cr for cr in change_requests if cr.number == 'CR-2024-001'), None)
            if expected_cr:
                print("✅ Successfully retrieved expected change request CR-2024-001")
                return True
            else:
                print("❌ Expected change request CR-2024-001 not found")
                return False
                
        except Exception as e:
            print(f"❌ Error retrieving change requests: {e}")
            return False
    
    def test_commit_correlation(self) -> bool:
        """Test commit-to-change-request correlation"""
        print("\n🔗 Testing Commit-to-Change-Request Correlation")
        print("-" * 50)
        
        try:
            commit = self.create_test_commit()
            
            # Get change requests for this commit
            change_requests = self.integration.get_change_requests_for_commit(
                author=commit.author,
                commit_message=commit.message,
                changed_files=commit.changed_paths
            )
            
            # Add change requests to commit
            commit.change_requests = change_requests
            
            print(f"📊 Commit: {commit.message}")
            print(f"📊 Author: {commit.author}")
            print(f"📊 Files: {len(commit.changed_paths)} files changed")
            print(f"📊 Correlated Change Requests: {len(commit.change_requests)}")
            
            for cr in commit.change_requests:
                print(f"   - {cr.number}: {cr.title}")
                print(f"     Business Impact: {cr.business_impact[:100]}...")
            
            if len(commit.change_requests) > 0:
                print("✅ Successfully correlated commit with change requests")
                return True
            else:
                print("❌ No change requests correlated with commit")
                return False
                
        except Exception as e:
            print(f"❌ Error correlating commit with change requests: {e}")
            return False
    
    def test_llm_analysis_with_change_requests(self) -> bool:
        """Test LLM analysis with change request context"""
        print("\n🤖 Testing LLM Analysis with Change Request Context")
        print("-" * 50)
        
        if not self.ollama_client:
            print("⚠️  Skipping LLM test - Ollama client not available")
            return True
        
        try:
            commit = self.create_test_commit()
            
            # Get change requests for this commit
            change_requests = self.integration.get_change_requests_for_commit(
                author=commit.author,
                commit_message=commit.message,
                changed_files=commit.changed_paths
            )
            
            commit.change_requests = change_requests
            
            print(f"📊 Generating documentation with {len(change_requests)} change requests...")
            
            # Generate documentation with change request context
            documentation = self.ollama_client.generate_documentation(commit)
            
            if documentation:
                print("✅ Documentation generated successfully")
                print(f"📄 Documentation length: {len(documentation)} characters")
                
                # Check for change request context in documentation
                context_indicators = [
                    "CR-2024-001",
                    "Change Request",
                    "business impact",
                    "camera interval",
                    "automation",
                    "surveillance"
                ]
                
                found_indicators = []
                for indicator in context_indicators:
                    if indicator.lower() in documentation.lower():
                        found_indicators.append(indicator)
                
                print(f"📊 Found {len(found_indicators)} context indicators in documentation:")
                for indicator in found_indicators:
                    print(f"   ✅ {indicator}")
                
                # Check that documentation doesn't contain placeholder text
                placeholders = [
                    "[Brief summary",
                    "[Analysis of how code changes align",
                    "[Detailed technical analysis]"
                ]
                
                found_placeholders = []
                for placeholder in placeholders:
                    if placeholder in documentation:
                        found_placeholders.append(placeholder)
                
                if found_placeholders:
                    print("❌ Found placeholder text in documentation:")
                    for placeholder in found_placeholders:
                        print(f"   ❌ {placeholder}")
                    return False
                else:
                    print("✅ No placeholder text found - documentation properly generated")
                
                # Show a preview of the documentation
                print("\n📄 Documentation Preview (first 500 characters):")
                print("-" * 50)
                print(documentation[:500] + "..." if len(documentation) > 500 else documentation)
                
                return len(found_indicators) >= 3  # Expect at least 3 context indicators
            else:
                print("❌ No documentation generated")
                return False
                
        except Exception as e:
            print(f"❌ Error in LLM analysis: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def cleanup(self):
        """Clean up test resources"""
        if self.test_db_path and os.path.exists(self.test_db_path):
            os.unlink(self.test_db_path)
            print(f"🧹 Cleaned up test database: {self.test_db_path}")
    
    def run_all_tests(self) -> bool:
        """Run all integration tests"""
        print("🚀 Change Request LLM Integration Test")
        print("=" * 60)
        
        try:
            # Setup
            self.setup_test_database()
            self.setup_integration()
            ollama_available = self.setup_ollama_client()
            
            # Run tests
            tests = [
                ("Change Request Retrieval", self.test_change_request_retrieval),
                ("Commit Correlation", self.test_commit_correlation),
                ("LLM Analysis with Change Requests", self.test_llm_analysis_with_change_requests)
            ]
            
            results = []
            for test_name, test_func in tests:
                try:
                    result = test_func()
                    results.append((test_name, result))
                except Exception as e:
                    print(f"❌ {test_name} failed with exception: {e}")
                    results.append((test_name, False))
            
            # Summary
            print("\n📊 Test Results Summary")
            print("=" * 60)
            
            passed = 0
            total = len(results)
            
            for test_name, result in results:
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"{status} {test_name}")
                if result:
                    passed += 1
            
            print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
            
            if passed == total:
                print("🎉 All tests passed! Change request LLM integration is working correctly.")
                return True
            else:
                print("⚠️  Some tests failed. Check the output above for details.")
                return False
                
        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            import traceback
            traceback.print_exc()
            return False
        finally:
            self.cleanup()

def main():
    """Run the integration test"""
    test = ChangeRequestLLMIntegrationTest()
    success = test.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
