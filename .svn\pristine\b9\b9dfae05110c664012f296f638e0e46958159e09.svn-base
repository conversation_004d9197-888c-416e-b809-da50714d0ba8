#!/usr/bin/env python3
"""
Historical Scanner Service
Orchestrates historical scanning of repository commits with progress tracking,
batch processing, and error handling.
"""

import logging
import threading
import time
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from pathlib import Path
from queue import Empty, Queue
from typing import Any, Callable, Dict, List, Optional, Tuple

from models import (
    CommitInfo,
    HistoricalScanConfig,
    HistoricalScanStatus,
    RepositoryConfig,
)
from notification_system import EventFactory, NotificationSeverity

# Removed: from document_processor import DocumentProcessor  # Now using UnifiedDocumentProcessor
# Removed: from document_database import DocumentDatabase, DocumentRecord  # UnifiedDocumentProcessor handles DB
from ollama_client import OllamaClient
from repository_backends import RepositoryBackendManager

# Removed: from metadata_extractor import MetadataExtractor  # UnifiedDocumentProcessor handles metadata


@dataclass
class ScanTask:
    """Represents a historical scanning task"""

    repository_id: str
    repository_name: str
    scan_config: HistoricalScanConfig
    revisions_to_scan: List[str] = field(default_factory=list)
    created_at: datetime = field(default_factory=datetime.now)
    priority: int = 1  # Lower number = higher priority


@dataclass
class ScanProgress:
    """Progress tracking for a historical scan"""

    repository_id: str
    total_revisions: int
    processed_revisions: int
    failed_revisions: int
    current_revision: Optional[str] = None
    started_at: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    error_message: Optional[str] = None
    status: HistoricalScanStatus = HistoricalScanStatus.NOT_STARTED


class HistoricalScanner:
    """Service for orchestrating historical repository scanning"""

    def __init__(
        self,
        backend_manager: RepositoryBackendManager,
        unified_processor,  # UnifiedDocumentProcessor (avoiding import for now)
        ollama_client: OllamaClient,
        max_concurrent_scans: int = 2,
        db_path: str = "/app/data/documents.db",
        config_manager=None,
        monitor_service=None,
        notification_manager=None,
    ):
        self.backend_manager = backend_manager
        self.unified_processor = (
            unified_processor  # Use unified processor instead of document processor
        )
        self.ollama_client = ollama_client
        self.max_concurrent_scans = max_concurrent_scans
        self.config_manager = config_manager
        self.monitor_service = monitor_service
        self.notification_manager = notification_manager

        # Remove direct database operations and metadata extraction - unified processor handles this
        # self.document_db = DocumentDatabase(db_path)
        # self.metadata_extractor = MetadataExtractor(ollama_client, config_manager)

        self.logger = logging.getLogger(__name__)

        # Task management
        self.scan_queue: Queue[ScanTask] = Queue()
        self.active_scans: Dict[str, ScanProgress] = {}
        self.completed_scans: Dict[str, ScanProgress] = {}

        # Threading
        self.worker_threads: List[threading.Thread] = []
        self.running = False
        self.shutdown_event = threading.Event()

        # Progress callbacks
        self.progress_callbacks: List[Callable[[str, ScanProgress], None]] = []

        # Statistics
        self.stats = {
            "total_scans_started": 0,
            "total_scans_completed": 0,
            "total_scans_failed": 0,
            "total_revisions_processed": 0,
            "total_documents_generated": 0,
        }

    def start(self):
        """Start the historical scanner service"""
        if self.running:
            return

        self.running = True
        self.shutdown_event.clear()

        # Start worker threads
        for i in range(self.max_concurrent_scans):
            thread = threading.Thread(
                target=self._worker_thread, name=f"HistoricalScanner-{i}", daemon=True
            )
            thread.start()
            self.worker_threads.append(thread)

        self.logger.info(
            f"Historical scanner started with {self.max_concurrent_scans} worker threads"
        )

    def stop(self):
        """Stop the historical scanner service"""
        if not self.running:
            return

        self.logger.info("Stopping historical scanner...")
        self.running = False
        self.shutdown_event.set()

        # Wait for worker threads to finish
        for thread in self.worker_threads:
            thread.join(timeout=30)

        self.worker_threads.clear()
        self.logger.info("Historical scanner stopped")

    def queue_scan(
        self,
        repo: RepositoryConfig,
        scan_config: HistoricalScanConfig,
        priority: int = 1,
    ) -> bool:
        """
        Queue a historical scan for a repository

        Args:
            repo: Repository configuration
            scan_config: Historical scan configuration
            priority: Scan priority (lower = higher priority)

        Returns:
            True if scan was queued successfully
        """
        try:
            # Check if scan is already active
            if repo.id in self.active_scans:
                self.logger.warning(f"Scan already active for repository {repo.name}")
                return False

            # Get backend for repository
            backend = self.backend_manager.get_backend_for_repository(repo, None)
            if not backend:
                self.logger.error(f"No backend available for repository {repo.name}")
                return False

            # Determine revisions to scan
            revisions_to_scan, error_message = self._determine_revisions_to_scan(
                repo, scan_config, backend
            )
            if not revisions_to_scan:
                self.logger.warning(
                    f"No revisions to scan for repository {repo.name}: {error_message}"
                )
                # Store the error message for the UI to display
                if repo.id not in self.completed_scans:
                    progress = ScanProgress(
                        repository_id=repo.id,
                        total_revisions=0,
                        processed_revisions=0,
                        failed_revisions=0,
                        status=HistoricalScanStatus.FAILED,
                        error_message=error_message,
                    )
                    self.completed_scans[repo.id] = progress
                return False

            # Check permissions early if documentation generation is enabled
            if scan_config.generate_documentation:
                permission_error = self._check_output_permissions(repo)
                if permission_error:
                    self.logger.error(
                        f"Permission check failed for repository {repo.name}: {permission_error}"
                    )
                    # Store the permission error for the UI to display
                    if repo.id not in self.completed_scans:
                        progress = ScanProgress(
                            repository_id=repo.id,
                            total_revisions=0,
                            processed_revisions=0,
                            failed_revisions=0,
                            status=HistoricalScanStatus.FAILED,
                            error_message=permission_error,
                        )
                        self.completed_scans[repo.id] = progress
                    return False

            # Create scan task
            task = ScanTask(
                repository_id=repo.id,
                repository_name=repo.name,
                scan_config=scan_config,
                revisions_to_scan=revisions_to_scan,
                priority=priority,
            )

            # Create initial progress tracking and set status to IN_PROGRESS immediately
            progress = ScanProgress(
                repository_id=repo.id,
                total_revisions=len(revisions_to_scan),
                processed_revisions=0,
                failed_revisions=0,
                started_at=datetime.now(),
                status=HistoricalScanStatus.IN_PROGRESS,
            )

            # Clear any existing progress for this repository before starting new scan
            if repo.id in self.active_scans:
                del self.active_scans[repo.id]
            if repo.id in self.completed_scans:
                del self.completed_scans[repo.id]

            # Add to active scans immediately so UI shows "in_progress" status
            self.active_scans[repo.id] = progress
            self._notify_progress_callbacks(repo.id, progress)

            # Queue the task
            self.scan_queue.put(task)
            self.stats["total_scans_started"] += 1

            self.logger.info(
                f"Queued historical scan for {repo.name} with {len(revisions_to_scan)} revisions (status: IN_PROGRESS)"
            )
            return True

        except Exception as e:
            self.logger.error(f"Error queuing scan for {repo.name}: {e}")
            return False

    def cancel_scan(self, repository_id: str) -> bool:
        """
        Cancel an active scan

        Args:
            repository_id: Repository ID to cancel

        Returns:
            True if scan was cancelled
        """
        if repository_id in self.active_scans:
            progress = self.active_scans[repository_id]
            progress.status = HistoricalScanStatus.CANCELLED
            progress.error_message = "Scan cancelled by user"

            self.logger.info(f"Cancelled scan for repository {repository_id}")
            return True

        return False

    def get_scan_progress(self, repository_id: str) -> Optional[ScanProgress]:
        """Get progress for a specific scan"""
        return self.active_scans.get(repository_id) or self.completed_scans.get(
            repository_id
        )

    def get_all_scan_progress(self) -> Dict[str, ScanProgress]:
        """Get progress for all scans (active and completed)"""
        all_progress = {}
        all_progress.update(self.active_scans)
        all_progress.update(self.completed_scans)
        return all_progress

    def get_statistics(self) -> Dict[str, Any]:
        """Get scanner statistics"""
        return {
            **self.stats,
            "active_scans": len(self.active_scans),
            "queued_scans": self.scan_queue.qsize(),
            "completed_scans": len(self.completed_scans),
        }

    def add_progress_callback(self, callback: Callable[[str, ScanProgress], None]):
        """Add a progress callback function"""
        self.progress_callbacks.append(callback)

    def remove_progress_callback(self, callback: Callable[[str, ScanProgress], None]):
        """Remove a progress callback function"""
        if callback in self.progress_callbacks:
            self.progress_callbacks.remove(callback)

    def _determine_revisions_to_scan(
        self, repo: RepositoryConfig, scan_config: HistoricalScanConfig, backend
    ) -> Tuple[List[str], Optional[str]]:
        """Determine which revisions need to be scanned

        Returns:
            Tuple of (revisions_list, error_message)
            If revisions_list is empty, error_message explains why
        """
        try:
            revisions = []

            if scan_config.scan_by_revision:
                # Scan by revision range
                revisions = backend.get_revision_range(
                    repo, scan_config.start_revision, scan_config.end_revision
                )
            elif (
                scan_config.scan_by_date
                and scan_config.start_date
                and scan_config.end_date
            ):
                # Scan by date range
                revisions = backend.get_revisions_by_date_range(
                    repo, scan_config.start_date, scan_config.end_date
                )

            if not revisions:
                if scan_config.scan_by_revision:
                    return (
                        [],
                        f"No revisions found in range {scan_config.start_revision} to {scan_config.end_revision}. Please check your revision range.",
                    )
                else:
                    return (
                        [],
                        f"No revisions found in date range {scan_config.start_date} to {scan_config.end_date}. Please check your date range.",
                    )

            # Store original count for better error messages
            original_count = len(revisions)

            # Check database for existing documents instead of relying on last_scanned_revision
            # This allows partial scanning of different revision ranges
            self.logger.info(
                f"Processing {len(revisions)} revisions for {repo.name}. Force rescan: {scan_config.force_rescan}"
            )

            if (
                not scan_config.force_rescan
                and hasattr(self, "unified_processor")
                and self.unified_processor
                and hasattr(self.unified_processor, "db")
            ):
                try:
                    # Filter out revisions that already have documents in the database
                    revisions_to_check = []
                    skipped_count = 0

                    self.logger.debug(
                        f"Checking database for existing documents for {len(revisions)} revisions"
                    )

                    for revision in revisions:
                        # Generate document ID to check if it exists (use same format as unified processor)
                        doc_id = f"{repo.id}_{revision}"
                        existing_doc = self.unified_processor.db.get_document_by_id(
                            doc_id
                        )

                        if not existing_doc:
                            # No document exists for this revision - needs scanning
                            revisions_to_check.append(revision)
                            self.logger.debug(
                                f"Revision {revision} needs scanning - no document found with ID: {doc_id}"
                            )
                        else:
                            skipped_count += 1
                            self.logger.debug(
                                f"Skipping revision {revision} - document already exists: {doc_id}"
                            )

                    revisions = revisions_to_check

                    if skipped_count > 0:
                        self.logger.info(
                            f"Skipped {skipped_count} revisions that already have documents. "
                            f"Scanning {len(revisions)} new revisions. "
                            f"Use 'Force Rescan' option to re-scan existing revisions."
                        )

                    if not revisions and original_count > 0:
                        return [], (
                            f"All {original_count} revisions in the specified range already have documents in the database. "
                            f"Enable 'Force Rescan' option to re-scan previously processed revisions, "
                            f"or use the 'Delete All' function to remove existing documents before re-scanning."
                        )
                except Exception as e:
                    self.logger.error(
                        f"Error checking existing documents, proceeding with all revisions: {e}"
                    )
                    import traceback

                    self.logger.error(
                        f"Database check traceback: {traceback.format_exc()}"
                    )
                    # If database check fails, proceed with all revisions (safer approach)
                    pass
            elif scan_config.force_rescan:
                self.logger.info(
                    f"Force rescan enabled - will process all {len(revisions)} revisions regardless of existing documents"
                )
            else:
                self.logger.warning(
                    f"Unified processor not available for database checking, proceeding with all {len(revisions)} revisions"
                )

            return revisions, None

        except Exception as e:
            error_msg = f"Error determining revisions to scan: {str(e)}"
            self.logger.error(f"{error_msg} for {repo.name}")
            return [], error_msg

    def _check_output_permissions(self, repo: RepositoryConfig) -> Optional[str]:
        """Check if we have write permissions to the output directory

        Returns:
            None if permissions are OK, error message string if there's a problem
        """
        try:
            import os
            import tempfile
            from pathlib import Path

            # Create the expected output directory path (repo.name now includes branch path)
            output_dir = Path("/app/data/output/repositories") / repo.name / "docs"

            # Try to create the directory structure
            try:
                output_dir.mkdir(parents=True, exist_ok=True)
            except PermissionError:
                return (
                    f"Cannot create documentation directory '{output_dir}'. "
                    f"This is a file permission issue. "
                    f"Please check that the application has write access to the data directory. "
                    f"You may need to restart the container or fix directory permissions."
                )

            # Try to write a test file
            test_file = output_dir / ".permission_test"
            try:
                with open(test_file, "w") as f:
                    f.write("test")
                # Clean up test file
                test_file.unlink()
                return None  # Permissions are OK
            except PermissionError:
                return (
                    f"Cannot write to documentation directory '{output_dir}'. "
                    f"This is a file permission issue. "
                    f"Please check that the application has write access to the data directory. "
                    f"You may need to restart the container or fix directory permissions."
                )
            except Exception as e:
                return f"Unexpected error checking permissions: {e}"

        except Exception as e:
            return f"Error checking output permissions: {e}"

    def _worker_thread(self):
        """Worker thread for processing scan tasks"""
        thread_name = threading.current_thread().name
        self.logger.info(f"Historical scanner worker {thread_name} started")

        while self.running and not self.shutdown_event.is_set():
            try:
                # Get next task from queue (with timeout)
                try:
                    task = self.scan_queue.get(timeout=1.0)
                except Empty:
                    continue

                # Process the scan task
                self._process_scan_task(task)

                # Mark task as done
                self.scan_queue.task_done()

            except Exception as e:
                self.logger.error(f"Error in worker thread {thread_name}: {e}")
                time.sleep(1)

        self.logger.info(f"Historical scanner worker {thread_name} stopped")

    def _process_scan_task(self, task: ScanTask):
        """Process a single scan task"""
        repo_id = task.repository_id
        repo_name = task.repository_name

        # Get existing progress tracker (created in queue_scan)
        progress = self.active_scans.get(repo_id)
        if not progress:
            # Fallback: create progress tracker if not found (shouldn't happen)
            progress = ScanProgress(
                repository_id=repo_id,
                total_revisions=len(task.revisions_to_scan),
                processed_revisions=0,
                failed_revisions=0,
                started_at=datetime.now(),
                status=HistoricalScanStatus.IN_PROGRESS,
            )
            self.active_scans[repo_id] = progress
            self._notify_progress_callbacks(repo_id, progress)

        try:
            self.logger.info(
                f"Starting historical scan for {repo_name} ({len(task.revisions_to_scan)} revisions)"
            )

            # Get repository configuration (we need the full config)
            repo_config = self._get_repository_config(repo_id)
            if not repo_config:
                raise Exception(f"Repository configuration not found for {repo_id}")

            # Get backend
            backend = self.backend_manager.get_backend_for_repository(repo_config, None)
            if not backend:
                raise Exception(f"No backend available for repository {repo_name}")

            # Process revisions in batches
            batch_size = task.scan_config.batch_size
            revisions = task.revisions_to_scan

            for i in range(0, len(revisions), batch_size):
                # Check if scan was cancelled
                if progress.status == HistoricalScanStatus.CANCELLED:
                    break

                batch = revisions[i : i + batch_size]
                processed_in_batch = self._process_revision_batch(
                    repo_config, backend, batch, task.scan_config, progress
                )

                # Progress is now updated individually within _process_revision_batch
                # No need to update here to avoid double-counting

                # Small delay between batches to avoid overwhelming the system
                time.sleep(0.1)

            # Mark scan as completed or failed based on results
            if progress.status != HistoricalScanStatus.CANCELLED:
                if progress.failed_revisions > 0:
                    # Scan had failures (including documentation generation failures)
                    progress.status = HistoricalScanStatus.FAILED
                    progress.error_message = f"Scan completed with {progress.failed_revisions} failed revisions. Check logs for details."
                    self.stats["total_scans_failed"] += 1
                    self.logger.warning(
                        f"Historical scan for {repo_name} completed with failures: "
                        f"{progress.processed_revisions} processed, {progress.failed_revisions} failed"
                    )

                    # Emit failed scan completion event
                    self._emit_scan_completion_event(
                        repo_id=repo_id,
                        repo_name=repo_name,
                        revisions_processed=progress.processed_revisions,
                        success=False,
                        error_message=progress.error_message,
                    )
                else:
                    # Scan was completely successful
                    progress.status = HistoricalScanStatus.COMPLETED
                    self.stats["total_scans_completed"] += 1
                    self.logger.info(
                        f"Historical scan for {repo_name} completed successfully: "
                        f"{progress.processed_revisions} processed, {progress.failed_revisions} failed"
                    )

                    # Emit successful scan completion event
                    self._emit_scan_completion_event(
                        repo_id=repo_id,
                        repo_name=repo_name,
                        revisions_processed=progress.processed_revisions,
                        success=True,
                    )

                # Find and update the actual repository configuration
                self.logger.info(
                    f"Attempting to save configuration for scan of {repo_name} (status: {progress.status.value})"
                )
                if self.monitor_service:
                    self.logger.info(
                        f"Monitor service is available, looking for repository {repo_id}"
                    )
                    try:
                        # Find the repository in the database
                        repo_config = self.monitor_service.repo_db.get_repository_by_id(
                            repo_id
                        )

                        if repo_config and repo_config.historical_scan:
                            # Update the actual repository scan config with the final status
                            repo_config.historical_scan.scan_status = progress.status
                            repo_config.historical_scan.scan_completed_at = (
                                datetime.now()
                            )
                            repo_config.historical_scan.processed_revisions = (
                                progress.processed_revisions
                            )
                            repo_config.historical_scan.failed_revisions = (
                                progress.failed_revisions
                            )
                            repo_config.historical_scan.error_message = (
                                progress.error_message
                            )

                            # Only update last_scanned_revision if scan was completely successful
                            if (
                                progress.status == HistoricalScanStatus.COMPLETED
                                and revisions
                            ):
                                try:
                                    repo_config.historical_scan.last_scanned_revision = int(
                                        revisions[-1]
                                    )
                                except (ValueError, TypeError):
                                    # Handle non-numeric revision systems
                                    pass

                            # Save the updated configuration to database
                            success = self.monitor_service.repo_db.update_repository(
                                repo_config
                            )
                            if success:
                                self.logger.info(
                                    f"Saved configuration after scan for {repo_name}: {progress.status.value}"
                                )
                            else:
                                self.logger.error(
                                    f"Failed to save configuration after scan for {repo_name}"
                                )
                        else:
                            self.logger.warning(
                                f"Could not find repository config for {repo_name} to update scan status"
                            )
                    except Exception as e:
                        self.logger.error(f"Error saving configuration after scan: {e}")

        except Exception as e:
            self.logger.error(f"Error processing scan for {repo_name}: {e}")
            progress.status = HistoricalScanStatus.FAILED
            progress.error_message = str(e)
            self.stats["total_scans_failed"] += 1

            # Find and update the actual repository configuration for failure
            if self.monitor_service:
                try:
                    # Find the repository in the database
                    repo_config = self.monitor_service.repo_db.get_repository_by_id(
                        repo_id
                    )

                    if repo_config and repo_config.historical_scan:
                        # Update scan config with failure status
                        repo_config.historical_scan.scan_status = (
                            HistoricalScanStatus.FAILED
                        )
                        repo_config.historical_scan.error_message = str(e)

                        # Save the updated configuration to database
                        success = self.monitor_service.repo_db.update_repository(
                            repo_config
                        )
                        if success:
                            self.logger.debug(
                                f"Saved configuration after scan failure for {repo_name}"
                            )
                        else:
                            self.logger.error(
                                f"Failed to save configuration after scan failure for {repo_name}"
                            )
                    else:
                        self.logger.warning(
                            f"Could not find repository config for {repo_name} to update failure status"
                        )
                except Exception as save_error:
                    self.logger.error(
                        f"Error saving configuration after scan failure: {save_error}"
                    )

        finally:
            # Move from active to completed
            if repo_id in self.active_scans:
                self.completed_scans[repo_id] = self.active_scans.pop(repo_id)

            self._notify_progress_callbacks(repo_id, progress)

    def _process_revision_batch(
        self,
        repo_config: RepositoryConfig,
        backend,
        revisions: List[str],
        scan_config: HistoricalScanConfig,
        progress: ScanProgress,
    ):
        """Process a batch of revisions"""
        processed_in_batch = 0
        repo_id = repo_config.id
        last_progress_update = time.time()

        self.logger.info(
            f"Processing batch of {len(revisions)} revisions for {repo_config.name}: {revisions}"
        )

        for commit_info in backend.get_commit_batch(repo_config, revisions):
            self.logger.info(
                f"Processing revision {commit_info.revision} for {repo_config.name}"
            )
            try:
                # Check if scan was cancelled
                if progress.status == HistoricalScanStatus.CANCELLED:
                    break

                progress.current_revision = commit_info.revision

                # Skip large commits if configured
                if (
                    scan_config.skip_large_commits
                    and len(commit_info.changed_paths)
                    > scan_config.max_files_per_commit
                ):
                    self.logger.debug(
                        f"Skipping large commit {commit_info.revision} "
                        f"({len(commit_info.changed_paths)} files)"
                    )
                    processed_in_batch += 1
                    progress.processed_revisions += 1

                    # Update progress every 3 revisions or every 2 seconds
                    current_time = time.time()
                    if (
                        processed_in_batch % 3 == 0
                        or current_time - last_progress_update >= 2.0
                    ):
                        self._update_estimated_completion(progress)
                        self._notify_progress_callbacks(repo_id, progress)
                        last_progress_update = current_time
                    continue

                # Generate documentation if enabled
                documentation_success = True
                if scan_config.generate_documentation:
                    documentation_success = self._generate_documentation(
                        commit_info, scan_config, repo_config
                    )
                    if not documentation_success:
                        self.logger.warning(
                            f"Documentation generation failed for revision {commit_info.revision}"
                        )
                        progress.failed_revisions += 1
                    else:
                        self.logger.debug(
                            f"Documentation generation succeeded for revision {commit_info.revision}"
                        )

                # Only count as successfully processed if documentation generation succeeded (when enabled)
                if documentation_success:
                    self.stats["total_revisions_processed"] += 1
                    processed_in_batch += 1
                    progress.processed_revisions += 1
                else:
                    # Still increment batch counter to maintain progress tracking
                    processed_in_batch += 1

                # Update progress every 3 revisions or every 2 seconds
                current_time = time.time()
                if (
                    processed_in_batch % 3 == 0
                    or current_time - last_progress_update >= 2.0
                ):
                    self._update_estimated_completion(progress)
                    self._notify_progress_callbacks(repo_id, progress)
                    last_progress_update = current_time

            except PermissionError as e:
                # Handle permission errors specifically - these are critical and should stop the scan
                self.logger.error(
                    f"Permission error processing revision {commit_info.revision}: {e}"
                )
                progress.status = HistoricalScanStatus.FAILED
                progress.error_message = str(e)
                # Don't continue processing if we have permission issues
                break
            except Exception as e:
                self.logger.error(
                    f"Error processing revision {commit_info.revision}: {e}"
                )
                progress.failed_revisions += 1
                processed_in_batch += 1
                progress.processed_revisions += 1

                # Update progress every 3 revisions or every 2 seconds
                current_time = time.time()
                if (
                    processed_in_batch % 3 == 0
                    or current_time - last_progress_update >= 2.0
                ):
                    self._update_estimated_completion(progress)
                    self._notify_progress_callbacks(repo_id, progress)
                    last_progress_update = current_time

        # Final progress update for this batch
        self._update_estimated_completion(progress)
        self._notify_progress_callbacks(repo_id, progress)

        return processed_in_batch

    def _generate_documentation(
        self,
        commit_info: CommitInfo,
        scan_config: HistoricalScanConfig,
        repo_config: "RepositoryConfig",
    ) -> bool:
        """Generate documentation for a commit

        Returns:
            True if documentation was successfully generated and saved, False otherwise
        """
        try:
            # Generate documentation using Ollama
            documentation = self.ollama_client.generate_documentation(commit_info)

            # Check if documentation generation was successful
            if documentation and not documentation.startswith("Error:"):
                # Save documentation using the integrated document system
                success = self._save_historical_documentation_integrated(
                    commit_info, documentation, repo_config, scan_config
                )

                if success:
                    self.stats["total_documents_generated"] += 1
                    self.logger.debug(
                        f"Successfully generated documentation for revision {commit_info.revision}"
                    )
                    return True
                else:
                    self.logger.error(
                        f"Failed to save documentation for revision {commit_info.revision}"
                    )
                    return False
            else:
                if documentation and documentation.startswith("Error:"):
                    self.logger.error(
                        f"Documentation generation failed for revision {commit_info.revision} in {repo_config.name}: {documentation}"
                    )

                    # Enhanced error analysis with more specific guidance
                    doc_lower = documentation.lower()
                    if (
                        "encoding issue" in doc_lower
                        or "utf-8" in doc_lower
                        or "binary files" in doc_lower
                    ):
                        self.logger.error(
                            f"🔧 ENCODING ERROR for r{commit_info.revision}: Repository contains binary files causing UTF-8 decode errors. Consider configuring SVN to handle binary files properly or exclude binary file types from analysis."
                        )
                    elif (
                        "connectivity issue" in doc_lower
                        or "ollama service" in doc_lower
                        or "connection" in doc_lower
                    ):
                        ollama_host = "unknown"
                        if self.config_manager:
                            try:
                                config = self.config_manager.load_config()
                                ollama_host = config.ollama_host
                            except Exception:
                                pass
                        self.logger.error(
                            f"🌐 CONNECTION ERROR for r{commit_info.revision}: Cannot reach Ollama service at {ollama_host}. Check service status: docker ps | grep ollama"
                        )
                    elif (
                        "context limit" in doc_lower
                        or "model/memory issue" in doc_lower
                        or "too large" in doc_lower
                    ):
                        file_count = (
                            len(commit_info.changed_paths)
                            if hasattr(commit_info, "changed_paths")
                            and commit_info.changed_paths
                            else 0
                        )
                        self.logger.error(
                            f"📊 CONTEXT LIMIT ERROR for r{commit_info.revision}: Diff too large for AI model ({file_count} files changed). Consider using a larger model or processing smaller commits."
                        )
                    elif "model" in doc_lower and (
                        "overload" in doc_lower or "unavailable" in doc_lower
                    ):
                        self.logger.error(
                            f"🤖 MODEL ERROR for r{commit_info.revision}: AI model overloaded or unavailable. Try again later or switch to a different model."
                        )
                    elif (
                        "truncated response" in doc_lower
                        or "appears to be markdown" in doc_lower
                    ):
                        self.logger.warning(
                            f"⚠️  PARTIAL RESPONSE for r{commit_info.revision}: AI response may be incomplete but valid. This is often recoverable."
                        )
                    else:
                        self.logger.error(
                            f"❓ UNKNOWN ERROR for r{commit_info.revision}: Check Ollama service logs for more details."
                        )
                elif documentation:
                    self.logger.error(
                        f"Documentation generation returned unexpected content for revision {commit_info.revision}: {documentation[:100]}..."
                    )
                else:
                    self.logger.error(
                        f"No documentation generated for revision {commit_info.revision} in {repo_config.name} - this may indicate Ollama service issues, model unavailability, or network connectivity problems"
                    )
                return False

        except PermissionError as e:
            # Handle permission errors specifically
            self.logger.error(
                f"Permission error generating documentation for revision {commit_info.revision}: {e}"
            )
            raise PermissionError(
                f"File permission error during documentation generation: {e}"
            )
        except Exception as e:
            self.logger.error(
                f"Error generating documentation for revision {commit_info.revision}: {e}"
            )
            raise

    def _save_historical_documentation_integrated(
        self,
        commit_info: CommitInfo,
        documentation: str,
        repo_config: "RepositoryConfig",
        scan_config: HistoricalScanConfig,
    ) -> bool:
        """Save historical documentation using the integrated document system

        Returns:
            True if documentation was successfully saved, False otherwise
        """
        try:
            from datetime import datetime
            from pathlib import Path

            # Create output directory structure (repository_name now includes branch path)
            output_dir = (
                Path("/app/data/output/repositories")
                / commit_info.repository_name
                / "docs"
            )

            # Log directory creation for debugging
            self.logger.debug(f"Creating documentation directory: {output_dir}")
            if self._is_uuid(commit_info.repository_name):
                self.logger.warning(
                    f"Repository name appears to be UUID: {commit_info.repository_name}. "
                    f"This may indicate a configuration issue."
                )
            else:
                self.logger.debug(
                    f"Using friendly repository name: {commit_info.repository_name}"
                )

            try:
                output_dir.mkdir(parents=True, exist_ok=True)
            except PermissionError as e:
                raise PermissionError(
                    f"Cannot create documentation directory '{output_dir}'. "
                    f"This is likely a file permission issue. "
                    f"Please check that the application has write access to the data directory. "
                    f"You may need to restart the container or fix directory permissions. "
                    f"Original error: {e}"
                )

            # Create filename
            filename = f"revision_{commit_info.revision}.md"
            filepath = output_dir / filename

            # Write documentation to file
            try:
                with open(filepath, "w", encoding="utf-8") as f:
                    f.write(documentation)

                # Verify the file was actually written
                if not (filepath.exists() and filepath.stat().st_size > 0):
                    self.logger.error(
                        f"Documentation file {filepath} was not created or is empty"
                    )
                    return False

                self.logger.debug(f"Successfully saved documentation to {filepath}")

            except PermissionError as e:
                raise PermissionError(
                    f"Cannot write documentation file '{filepath}'. "
                    f"This is a file permission issue. "
                    f"Please check that the application has write access to the data directory. "
                    f"You may need to restart the container or fix directory permissions. "
                    f"Original error: {e}"
                )

            # Simple ID generation for logging (unified processor will handle the complex logic)
            doc_id = f"{commit_info.repository_id}_{commit_info.revision}"

            # Use unified processor for all document processing (replaces duplicate logic)
            # For force_rescan, we need to check if document already exists and delete it first
            if scan_config.force_rescan:
                doc_id = f"{commit_info.repository_id}_{commit_info.revision}"
                existing_doc = self.unified_processor.db.get_document_by_id(doc_id)
                if existing_doc:
                    self.logger.info(
                        f"Force rescan enabled - deleting existing document: {doc_id}"
                    )
                    self.unified_processor.db.delete_document(doc_id)

            success = self.unified_processor.process_commit(
                commit_info=commit_info,
                repository_config=repo_config,
                documentation=documentation,
                priority=7,  # High priority for historical scans
            )

            if success:
                self.logger.debug(
                    f"Queued historical documentation for processing: {doc_id}"
                )

                # CRITICAL: Invalidate DocumentService cache to ensure web interface shows new documents
                try:
                    cache_signal_file = Path("/app/data/cache_invalidate_signal")
                    cache_signal_file.touch()
                    self.logger.debug(
                        f"Created cache invalidation signal for new document: {doc_id}"
                    )
                except Exception as e:
                    self.logger.warning(
                        f"Could not create cache invalidation signal: {e}"
                    )

                return True  # Successfully queued for processing
            else:
                self.logger.error(f"Failed to queue historical documentation: {doc_id}")
                return False  # Failed to queue

        except Exception as e:
            self.logger.error(
                f"Error saving historical documentation for revision {commit_info.revision}: {e}"
            )
            return False

    def _extract_document_metadata(self, documentation: str) -> Dict[str, Any]:
        """Extract metadata from generated documentation using hybrid heuristic + LLM approach"""
        metadata: Dict[str, Any] = {}

        try:
            # First try heuristic extraction (fast)
            code_review_rec = self._extract_code_review_recommendation(documentation)
            if code_review_rec is not None:
                metadata["code_review_recommended"] = code_review_rec

                # Extract priority if review is recommended
                if code_review_rec:
                    priority = self._extract_code_review_priority(documentation)
                    if priority:
                        metadata["code_review_priority"] = priority

            # Extract documentation impact using robust section-based approach
            doc_impact = self._extract_documentation_impact(documentation)
            if doc_impact is not None:
                metadata["documentation_impact"] = doc_impact

            # Extract risk level using robust section-based approach
            risk_level = self._extract_risk_level(documentation)
            if risk_level:
                metadata["risk_level"] = risk_level

            # Check if any critical fields are missing and use LLM fallback
            missing_fields = []
            if metadata.get("code_review_recommended") is None:
                missing_fields.append("code_review_recommended")
            if metadata.get("code_review_priority") is None and metadata.get(
                "code_review_recommended"
            ):
                missing_fields.append("code_review_priority")
            if metadata.get("risk_level") is None:
                missing_fields.append("risk_level")

            if missing_fields:
                self.logger.info(
                    f"Heuristic extraction incomplete for {missing_fields}, using LLM fallback"
                )
                llm_metadata = self._extract_metadata_with_llm(documentation)

                # Fill in missing values with LLM results
                for field in missing_fields:
                    if llm_metadata.get(field) is not None:
                        metadata[field] = llm_metadata[field]
                        self.logger.debug(
                            f"LLM provided {field}: {llm_metadata[field]}"
                        )

        except Exception as e:
            self.logger.error(f"Error extracting document metadata: {e}")

        return metadata

    def _extract_metadata_with_llm(self, documentation: str) -> Dict[str, Any]:
        """Extract metadata using LLM when heuristics fail"""
        try:
            system_prompt = """You are a metadata extraction assistant. Analyze the provided technical documentation and extract specific metadata fields.

Return ONLY a JSON object with these exact fields:
- code_review_recommended: true/false (whether code review is recommended)
- code_review_priority: "HIGH"/"MEDIUM"/"LOW" (priority level if review is recommended, null if not recommended)
- risk_level: "HIGH"/"MEDIUM"/"LOW" (risk level of the changes)
- documentation_impact: true/false (whether documentation needs updates)

Be precise and consistent. If you cannot determine a value, use null."""

            prompt = f"""Analyze this technical documentation and extract the metadata:

{documentation}

Return only the JSON object with the metadata fields."""

            # Use specialized model for code review and risk assessment
            specialized_model = None
            if hasattr(self.ollama_client, "config") and self.ollama_client.config:
                config = self.ollama_client.config
                # Prefer risk assessment model, fall back to code review model
                risk_model = getattr(config, "ollama_model_risk_assessment", None)
                code_review_model = getattr(config, "ollama_model_code_review", None)
                specialized_model = risk_model or code_review_model

                if specialized_model:
                    self.logger.info(
                        f"🎯 Historical scanner using specialized model: {specialized_model}"
                    )
                else:
                    self.logger.debug("Historical scanner using default model")

            response = self.ollama_client.call_ollama(
                prompt, system_prompt, model=specialized_model
            )

            if response:
                # Try to parse JSON response
                import json

                try:
                    # Extract JSON from response (LLM might wrap it in explanatory text)
                    json_str = response.strip()

                    # Look for JSON object in the response
                    start_idx = json_str.find("{")
                    end_idx = json_str.rfind("}")

                    if start_idx != -1 and end_idx != -1 and end_idx > start_idx:
                        json_str = json_str[start_idx : end_idx + 1]

                    metadata = json.loads(json_str)

                    # Validate and normalize the response
                    normalized = {}

                    # Code review recommendation
                    if "code_review_recommended" in metadata:
                        val = metadata["code_review_recommended"]
                        if isinstance(val, bool):
                            normalized["code_review_recommended"] = val
                        elif isinstance(val, str):
                            normalized["code_review_recommended"] = val.lower() in [
                                "true",
                                "yes",
                                "1",
                            ]

                    # Code review priority
                    if "code_review_priority" in metadata:
                        val = metadata["code_review_priority"]
                        if (
                            val
                            and isinstance(val, str)
                            and val.upper() in ["HIGH", "MEDIUM", "LOW"]
                        ):
                            normalized["code_review_priority"] = val.upper()

                    # Risk level
                    if "risk_level" in metadata:
                        val = metadata["risk_level"]
                        if (
                            val
                            and isinstance(val, str)
                            and val.upper() in ["HIGH", "MEDIUM", "LOW"]
                        ):
                            normalized["risk_level"] = val.upper()

                    # Documentation impact
                    if "documentation_impact" in metadata:
                        val = metadata["documentation_impact"]
                        if isinstance(val, bool):
                            normalized["documentation_impact"] = val
                        elif isinstance(val, str):
                            normalized["documentation_impact"] = val.lower() in [
                                "true",
                                "yes",
                                "1",
                            ]

                    self.logger.debug(f"LLM extracted metadata: {normalized}")
                    return normalized

                except json.JSONDecodeError as e:
                    self.logger.warning(f"Failed to parse LLM JSON response: {e}")
                    self.logger.debug(f"Raw LLM response: {response}")

            return {}

        except Exception as e:
            self.logger.error(f"Error extracting metadata with LLM: {e}")
            return {}

    def _extract_code_review_recommendation(self, content: str) -> Optional[bool]:
        """Extract code review recommendation using voting mechanism between multiple analysis methods"""
        try:
            votes = {}
            confidence_scores = {}

            # Method 1: Heuristic keyword analysis
            heuristic_result = self._extract_code_review_heuristic(content)
            if heuristic_result:
                recommendation, confidence = heuristic_result
                votes["heuristic"] = recommendation
                confidence_scores["heuristic"] = confidence
                self.logger.debug(
                    f"Code review heuristic vote: {recommendation} (confidence: {confidence:.2f})"
                )

            # Method 2: LLM analysis (if available)
            if self.ollama_client:
                llm_result = self._extract_code_review_llm(content)
                if llm_result:
                    recommendation, confidence = llm_result
                    votes["llm"] = recommendation
                    confidence_scores["llm"] = confidence
                    self.logger.debug(
                        f"Code review LLM vote: {recommendation} (confidence: {confidence:.2f})"
                    )

            # Method 3: File pattern analysis
            file_result = self._extract_code_review_file_analysis(content)
            if file_result:
                recommendation, confidence = file_result
                votes["file_analysis"] = recommendation
                confidence_scores["file_analysis"] = confidence
                self.logger.debug(
                    f"Code review file analysis vote: {recommendation} (confidence: {confidence:.2f})"
                )

            # Apply voting mechanism
            final_recommendation = self._vote_on_code_review(votes, confidence_scores)

            if final_recommendation is not None:
                self.logger.debug(
                    f"Final code review decision: {final_recommendation} (from {len(votes)} votes)"
                )

            return final_recommendation

        except Exception as e:
            self.logger.debug(f"Error extracting code review recommendation: {e}")
            return None

    def _extract_code_review_priority(self, content: str) -> Optional[str]:
        """Extract code review priority using voting mechanism between heuristic and LLM analysis"""
        try:
            # Collect votes from different methods
            votes = {}
            confidence_scores = {}

            # Method 1: Heuristic analysis
            heuristic_result = self._extract_priority_heuristic(content)
            if heuristic_result:
                priority, confidence = heuristic_result
                votes["heuristic"] = priority
                confidence_scores["heuristic"] = confidence
                self.logger.debug(
                    f"Heuristic vote: {priority} (confidence: {confidence:.2f})"
                )

            # Method 2: LLM analysis (if available)
            if self.ollama_client:
                llm_result = self._extract_priority_llm(content)
                if llm_result:
                    priority, confidence = llm_result
                    votes["llm"] = priority
                    confidence_scores["llm"] = confidence
                    self.logger.debug(
                        f"LLM vote: {priority} (confidence: {confidence:.2f})"
                    )

            # Method 3: File-based heuristics (analyze file types, changes)
            file_result = self._extract_priority_file_analysis(content)
            if file_result:
                priority, confidence = file_result
                votes["file_analysis"] = priority
                confidence_scores["file_analysis"] = confidence
                self.logger.debug(
                    f"File analysis vote: {priority} (confidence: {confidence:.2f})"
                )

            # Apply voting mechanism
            final_priority = self._vote_on_priority(votes, confidence_scores)

            if final_priority:
                self.logger.debug(
                    f"Final priority decision: {final_priority} (from {len(votes)} votes)"
                )

            return final_priority

        except Exception as e:
            self.logger.debug(f"Error extracting code review priority: {e}")
            return None

    def _extract_priority_heuristic(self, content: str) -> Optional[tuple[str, float]]:
        """Extract priority using heuristic analysis with confidence scoring"""
        try:
            review_section = self._extract_section(
                content, "Code Review Recommendation"
            )
            if not review_section:
                return None

            review_lower = review_section.lower()
            confidence = 0.0

            # Explicit priority mentions (high confidence)
            if (
                "high priority" in review_lower
                or "urgent" in review_lower
                or "critical" in review_lower
            ):
                confidence = 0.9 if "high priority" in review_lower else 0.8
                return ("HIGH", confidence)
            elif "medium priority" in review_lower or "moderate" in review_lower:
                confidence = 0.9 if "medium priority" in review_lower else 0.7
                return ("MEDIUM", confidence)
            elif "low priority" in review_lower or "optional" in review_lower:
                confidence = 0.9 if "low priority" in review_lower else 0.8
                return ("LOW", confidence)

            # Contextual inference (medium confidence)
            low_indicators = [
                "may not require",
                "simple",
                "straightforward",
                "minimal",
                "quick glance",
                "due to its simplicity",
            ]
            high_indicators = ["extensive review", "thorough", "careful", "detailed"]

            low_count = sum(
                1 for indicator in low_indicators if indicator in review_lower
            )
            high_count = sum(
                1 for indicator in high_indicators if indicator in review_lower
            )

            if low_count > 0:
                confidence = min(0.7, 0.4 + (low_count * 0.1))
                return ("LOW", confidence)
            elif high_count > 0:
                confidence = min(0.7, 0.4 + (high_count * 0.1))
                return ("HIGH", confidence)
            else:
                # Default to medium with low confidence
                return ("MEDIUM", 0.3)

        except Exception as e:
            self.logger.debug(f"Error in heuristic priority extraction: {e}")
            return None

    def _extract_priority_llm(self, content: str) -> Optional[tuple[str, float]]:
        """Extract priority using LLM analysis with confidence scoring"""
        try:
            if not self.ollama_client:
                return None

            # Create a focused prompt for priority determination
            priority_prompt = f"""
            Analyze the following code review content and determine the review priority level.

            Content: {content[:2000]}  # Limit content to avoid token limits

            Based on the content, determine:
            1. Priority level: HIGH, MEDIUM, or LOW
            2. Confidence level: 0.0 to 1.0 (how certain you are)

            Consider:
            - HIGH: Security issues, breaking changes, critical bugs, production hotfixes
            - MEDIUM: New features, moderate refactoring, configuration changes
            - LOW: Documentation, tests, style improvements, minor fixes

            Respond in JSON format:
            {{"priority": "HIGH|MEDIUM|LOW", "confidence": 0.0-1.0, "reasoning": "brief explanation"}}
            """

            response = self.ollama_client.call_ollama(priority_prompt)
            if not response:
                return None

            # Parse JSON response
            import json

            try:
                result = json.loads(response.strip())
                priority = result.get("priority", "").upper()
                confidence = float(result.get("confidence", 0.5))
                reasoning = result.get("reasoning", "")

                if priority in ["HIGH", "MEDIUM", "LOW"]:
                    self.logger.debug(f"LLM reasoning: {reasoning}")
                    return (priority, confidence)

            except (json.JSONDecodeError, ValueError) as e:
                # Fallback to text parsing if JSON fails
                response_lower = response.lower()
                if "high" in response_lower and (
                    "priority" in response_lower or "urgent" in response_lower
                ):
                    return ("HIGH", 0.6)
                elif "low" in response_lower and (
                    "priority" in response_lower or "minor" in response_lower
                ):
                    return ("LOW", 0.6)
                elif "medium" in response_lower or "moderate" in response_lower:
                    return ("MEDIUM", 0.6)

            return None

        except Exception as e:
            self.logger.debug(f"Error in LLM priority extraction: {e}")
            return None

    def _extract_priority_file_analysis(
        self, content: str
    ) -> Optional[tuple[str, float]]:
        """Extract priority based on file types and change patterns"""
        try:
            content_lower = content.lower()

            # High priority file patterns
            high_priority_patterns = [
                "security",
                "auth",
                "login",
                "password",
                "token",
                "crypto",
                "database",
                "migration",
                "schema",
                "production",
                "deploy",
                "config",
                "settings",
                "environment",
                "api",
                "endpoint",
                "critical",
                "hotfix",
                "urgent",
                "breaking",
            ]

            # Low priority file patterns
            low_priority_patterns = [
                "test",
                "spec",
                "readme",
                "doc",
                "comment",
                "style",
                "format",
                "lint",
                "typo",
                "whitespace",
                "import",
                "example",
                "sample",
                "demo",
            ]

            high_score = sum(
                1 for pattern in high_priority_patterns if pattern in content_lower
            )
            low_score = sum(
                1 for pattern in low_priority_patterns if pattern in content_lower
            )

            # Calculate confidence based on pattern matches
            total_patterns = high_score + low_score
            if total_patterns == 0:
                return ("MEDIUM", 0.2)  # Default with low confidence

            if high_score > low_score:
                confidence = min(0.8, 0.3 + (high_score * 0.1))
                return ("HIGH", confidence)
            elif low_score > high_score:
                confidence = min(0.8, 0.3 + (low_score * 0.1))
                return ("LOW", confidence)
            else:
                return ("MEDIUM", 0.4)

        except Exception as e:
            self.logger.debug(f"Error in file analysis priority extraction: {e}")
            return None

    def _vote_on_priority(self, votes: dict, confidence_scores: dict) -> Optional[str]:
        """Apply voting mechanism to determine final priority"""
        try:
            if not votes:
                return None

            # Weighted voting based on confidence scores
            priority_scores = {"HIGH": 0.0, "MEDIUM": 0.0, "LOW": 0.0}
            total_weight = 0.0

            for method, priority in votes.items():
                confidence = confidence_scores.get(method, 0.5)
                priority_scores[priority] += confidence
                total_weight += confidence

            # Normalize scores
            if total_weight > 0:
                for priority in priority_scores:
                    priority_scores[priority] /= total_weight

            # Find winning priority
            winning_priority = max(
                priority_scores.keys(), key=lambda k: priority_scores[k]
            )
            winning_score = priority_scores[winning_priority]

            # Require minimum confidence threshold
            if winning_score < 0.3:
                self.logger.debug(
                    f"Low confidence in priority decision: {winning_score:.2f}"
                )
                return "MEDIUM"  # Default fallback

            # Log voting details
            vote_details = ", ".join(
                [
                    f"{method}:{priority}({confidence_scores.get(method, 0.5):.2f})"
                    for method, priority in votes.items()
                ]
            )
            self.logger.debug(
                f"Priority voting: {vote_details} -> {winning_priority} ({winning_score:.2f})"
            )

            return winning_priority

        except Exception as e:
            self.logger.debug(f"Error in priority voting: {e}")
            return "MEDIUM"  # Safe fallback

    def _extract_documentation_impact(self, content: str) -> Optional[bool]:
        """Extract documentation impact using voting mechanism between multiple analysis methods"""
        try:
            votes = {}
            confidence_scores = {}

            # Method 1: Heuristic keyword analysis
            heuristic_result = self._extract_doc_impact_heuristic(content)
            if heuristic_result:
                impact, confidence = heuristic_result
                votes["heuristic"] = impact
                confidence_scores["heuristic"] = confidence
                self.logger.debug(
                    f"Doc impact heuristic vote: {impact} (confidence: {confidence:.2f})"
                )

            # Method 2: LLM analysis (if available)
            if self.ollama_client:
                llm_result = self._extract_doc_impact_llm(content)
                if llm_result:
                    impact, confidence = llm_result
                    votes["llm"] = impact
                    confidence_scores["llm"] = confidence
                    self.logger.debug(
                        f"Doc impact LLM vote: {impact} (confidence: {confidence:.2f})"
                    )

            # Method 3: File pattern analysis
            file_result = self._extract_doc_impact_file_analysis(content)
            if file_result:
                impact, confidence = file_result
                votes["file_analysis"] = impact
                confidence_scores["file_analysis"] = confidence
                self.logger.debug(
                    f"Doc impact file analysis vote: {impact} (confidence: {confidence:.2f})"
                )

            # Apply voting mechanism
            final_impact = self._vote_on_doc_impact(votes, confidence_scores)

            if final_impact is not None:
                self.logger.debug(
                    f"Final doc impact decision: {final_impact} (from {len(votes)} votes)"
                )

            return final_impact

        except Exception as e:
            self.logger.debug(f"Error extracting documentation impact: {e}")
            return None

    def _extract_risk_level(self, content: str) -> Optional[str]:
        """Extract risk level using voting mechanism between multiple analysis methods"""
        try:
            votes = {}
            confidence_scores = {}

            # Method 1: Heuristic keyword analysis
            heuristic_result = self._extract_risk_heuristic(content)
            if heuristic_result:
                risk, confidence = heuristic_result
                votes["heuristic"] = risk
                confidence_scores["heuristic"] = confidence
                self.logger.debug(
                    f"Risk heuristic vote: {risk} (confidence: {confidence:.2f})"
                )

            # Method 2: LLM analysis (if available)
            if self.ollama_client:
                llm_result = self._extract_risk_llm(content)
                if llm_result:
                    risk, confidence = llm_result
                    votes["llm"] = risk
                    confidence_scores["llm"] = confidence
                    self.logger.debug(
                        f"Risk LLM vote: {risk} (confidence: {confidence:.2f})"
                    )

            # Method 3: File pattern analysis
            file_result = self._extract_risk_file_analysis(content)
            if file_result:
                risk, confidence = file_result
                votes["file_analysis"] = risk
                confidence_scores["file_analysis"] = confidence
                self.logger.debug(
                    f"Risk file analysis vote: {risk} (confidence: {confidence:.2f})"
                )

            # Apply voting mechanism
            final_risk = self._vote_on_risk(votes, confidence_scores)

            if final_risk:
                self.logger.debug(
                    f"Final risk decision: {final_risk} (from {len(votes)} votes)"
                )

            return final_risk

        except Exception as e:
            self.logger.debug(f"Error extracting risk level: {e}")
            return None

    def _extract_risk_heuristic(self, content: str) -> Optional[tuple[str, float]]:
        """Extract risk level using heuristic analysis with confidence scoring"""
        try:
            sections = [
                "Code Review Recommendation",
                "Impact Assessment",
                "Summary",
                "Recommendations",
            ]
            confidence = 0.0

            for section_name in sections:
                section = self._extract_section(content, section_name)
                if section:
                    section_lower = section.lower()

                    # Explicit risk level mentions (high confidence)
                    if (
                        "high risk" in section_lower
                        or "risk level: high" in section_lower
                    ):
                        return ("HIGH", 0.9)
                    elif (
                        "medium risk" in section_lower
                        or "risk level: medium" in section_lower
                    ):
                        return ("MEDIUM", 0.9)
                    elif (
                        "low risk" in section_lower
                        or "risk level: low" in section_lower
                    ):
                        return ("LOW", 0.9)

                    # Contextual risk indicators (medium confidence)
                    high_risk_indicators = [
                        "security risks",
                        "complex technical dependencies",
                        "breaking changes",
                        "critical",
                        "production impact",
                    ]
                    low_risk_indicators = [
                        "minimal impact",
                        "straightforward",
                        "simple",
                        "no additional follow-up",
                        "safe",
                    ]

                    # Check for negation patterns first
                    if any(
                        neg in section_lower
                        for neg in [
                            "no security risks",
                            "does not introduce",
                            "no additional",
                        ]
                    ):
                        confidence = 0.7
                        return ("LOW", confidence)

                    high_count = sum(
                        1
                        for indicator in high_risk_indicators
                        if indicator in section_lower
                    )
                    low_count = sum(
                        1
                        for indicator in low_risk_indicators
                        if indicator in section_lower
                    )

                    if high_count > 0:
                        confidence = min(0.8, 0.5 + (high_count * 0.1))
                        return ("HIGH", confidence)
                    elif low_count > 0:
                        confidence = min(0.8, 0.5 + (low_count * 0.1))
                        return ("LOW", confidence)

            # Default to medium with low confidence if no clear indicators
            return ("MEDIUM", 0.3)

        except Exception as e:
            self.logger.debug(f"Error in heuristic risk extraction: {e}")
            return None

    def _extract_risk_llm(self, content: str) -> Optional[tuple[str, float]]:
        """Extract risk level using LLM analysis with confidence scoring"""
        try:
            if not self.ollama_client:
                return None

            # Create focused prompt for risk assessment
            risk_prompt = f"""
            Analyze the following code review content and determine the risk level.

            Content: {content[:2000]}  # Limit content to avoid token limits

            Based on the content, determine:
            1. Risk level: HIGH, MEDIUM, or LOW
            2. Confidence level: 0.0 to 1.0 (how certain you are)

            Consider:
            - HIGH: Security vulnerabilities, breaking changes, database migrations, production systems
            - MEDIUM: New features, moderate refactoring, configuration changes, API modifications
            - LOW: Documentation, tests, style improvements, minor bug fixes

            Respond in JSON format:
            {{"risk_level": "HIGH|MEDIUM|LOW", "confidence": 0.0-1.0, "reasoning": "brief explanation"}}
            """

            response = self.ollama_client.call_ollama(risk_prompt)
            if not response:
                return None

            # Parse JSON response
            import json

            try:
                result = json.loads(response.strip())
                risk_level = result.get("risk_level", "").upper()
                confidence = float(result.get("confidence", 0.6))
                reasoning = result.get("reasoning", "")

                if risk_level in ["HIGH", "MEDIUM", "LOW"]:
                    self.logger.debug(f"LLM risk reasoning: {reasoning}")
                    return (risk_level, confidence)

            except (json.JSONDecodeError, ValueError):
                # Fallback to text parsing if JSON fails
                response_lower = response.lower()
                if "high" in response_lower and (
                    "risk" in response_lower or "critical" in response_lower
                ):
                    return ("HIGH", 0.6)
                elif "low" in response_lower and (
                    "risk" in response_lower or "safe" in response_lower
                ):
                    return ("LOW", 0.6)
                elif "medium" in response_lower or "moderate" in response_lower:
                    return ("MEDIUM", 0.6)

            return None

        except Exception as e:
            self.logger.debug(f"Error in LLM risk extraction: {e}")
            return None

    def _extract_risk_file_analysis(self, content: str) -> Optional[tuple[str, float]]:
        """Extract risk level based on file types and change patterns"""
        try:
            content_lower = content.lower()

            # High risk file patterns
            high_risk_patterns = [
                "security",
                "auth",
                "login",
                "password",
                "token",
                "crypto",
                "ssl",
                "tls",
                "database",
                "migration",
                "schema",
                "sql",
                "production",
                "deploy",
                "config",
                "settings",
                "environment",
                "api",
                "endpoint",
                "server",
                "critical",
                "hotfix",
                "urgent",
                "breaking",
                "major",
            ]

            # Medium risk patterns
            medium_risk_patterns = [
                "feature",
                "refactor",
                "update",
                "modify",
                "change",
                "new",
                "interface",
                "class",
                "method",
                "function",
                "algorithm",
            ]

            # Low risk patterns
            low_risk_patterns = [
                "test",
                "spec",
                "readme",
                "doc",
                "comment",
                "style",
                "format",
                "lint",
                "typo",
                "whitespace",
                "import",
                "example",
                "sample",
                "demo",
                "minor",
                "fix",
            ]

            high_score = sum(
                1 for pattern in high_risk_patterns if pattern in content_lower
            )
            medium_score = sum(
                1 for pattern in medium_risk_patterns if pattern in content_lower
            )
            low_score = sum(
                1 for pattern in low_risk_patterns if pattern in content_lower
            )

            # Calculate confidence based on pattern matches
            total_patterns = high_score + medium_score + low_score
            if total_patterns == 0:
                return ("MEDIUM", 0.2)  # Default with low confidence

            if high_score > medium_score and high_score > low_score:
                confidence = min(0.8, 0.4 + (high_score * 0.1))
                return ("HIGH", confidence)
            elif low_score > high_score and low_score > medium_score:
                confidence = min(0.8, 0.4 + (low_score * 0.1))
                return ("LOW", confidence)
            else:
                confidence = min(0.6, 0.3 + (medium_score * 0.05))
                return ("MEDIUM", confidence)

        except Exception as e:
            self.logger.debug(f"Error in file analysis risk extraction: {e}")
            return None

    def _vote_on_risk(self, votes: dict, confidence_scores: dict) -> Optional[str]:
        """Apply voting mechanism to determine final risk level"""
        try:
            if not votes:
                return "MEDIUM"  # Safe default

            # Single vote - return if confidence is reasonable
            if len(votes) == 1:
                method, risk = next(iter(votes.items()))
                confidence = confidence_scores.get(method, 0.5)
                return risk if confidence >= 0.3 else "MEDIUM"

            # Multiple votes - weighted by confidence
            risk_scores = {"HIGH": 0.0, "MEDIUM": 0.0, "LOW": 0.0}
            total_weight = 0.0

            for method, risk in votes.items():
                confidence = confidence_scores.get(method, 0.5)
                risk_scores[risk] += confidence
                total_weight += confidence

            # Normalize scores
            if total_weight > 0:
                for risk in risk_scores:
                    risk_scores[risk] /= total_weight

            # Find winning risk level
            winning_risk = max(risk_scores.keys(), key=lambda k: risk_scores[k])
            winning_score = risk_scores[winning_risk]

            # Require minimum confidence threshold
            if winning_score < 0.4:
                return "MEDIUM"  # Safe fallback

            # Log voting details
            vote_details = ", ".join(
                [
                    f"{method}:{risk}({confidence_scores.get(method, 0.5):.2f})"
                    for method, risk in votes.items()
                ]
            )
            self.logger.debug(
                f"Risk voting: {vote_details} -> {winning_risk} ({winning_score:.2f})"
            )

            return winning_risk

        except Exception as e:
            self.logger.debug(f"Error in risk voting: {e}")
            return "MEDIUM"  # Safe fallback

    def _extract_doc_impact_heuristic(
        self, content: str
    ) -> Optional[tuple[bool, float]]:
        """Extract documentation impact using heuristic analysis with confidence scoring"""
        try:
            doc_section = self._extract_section(content, "Documentation Impact")
            if not doc_section:
                return None

            doc_lower = doc_section.lower()

            # Explicit documentation impact mentions (high confidence)
            if any(
                phrase in doc_lower
                for phrase in [
                    "not required",
                    "no updates",
                    "no impact",
                    "no documentation updates",
                ]
            ):
                return (False, 0.9)
            elif any(
                phrase in doc_lower
                for phrase in [
                    "required",
                    "should be updated",
                    "needs update",
                    "documentation needed",
                ]
            ):
                return (True, 0.9)

            # Contextual indicators (medium confidence)
            impact_indicators = [
                "api",
                "interface",
                "public",
                "user",
                "configuration",
                "setup",
                "install",
                "breaking",
            ]
            no_impact_indicators = [
                "internal",
                "private",
                "refactor",
                "cleanup",
                "test",
                "style",
            ]

            impact_count = sum(
                1 for indicator in impact_indicators if indicator in doc_lower
            )
            no_impact_count = sum(
                1 for indicator in no_impact_indicators if indicator in doc_lower
            )

            if impact_count > no_impact_count:
                confidence = min(0.7, 0.4 + (impact_count * 0.1))
                return (True, confidence)
            elif no_impact_count > impact_count:
                confidence = min(0.7, 0.4 + (no_impact_count * 0.1))
                return (False, confidence)
            else:
                return (False, 0.3)  # Default to no impact with low confidence

        except Exception as e:
            self.logger.debug(f"Error in heuristic doc impact extraction: {e}")
            return None

    def _extract_doc_impact_llm(self, content: str) -> Optional[tuple[bool, float]]:
        """Extract documentation impact using LLM analysis with confidence scoring"""
        try:
            if not self.ollama_client:
                return None

            # Create focused prompt for documentation impact assessment
            doc_prompt = f"""
            Analyze the following code review content and determine if documentation updates are needed.

            Content: {content[:2000]}  # Limit content to avoid token limits

            Based on the content, determine:
            1. Documentation impact: true (documentation needed) or false (no documentation needed)
            2. Confidence level: 0.0 to 1.0 (how certain you are)

            Consider documentation is needed for:
            - API changes, new endpoints, interface modifications
            - User-facing features, configuration changes
            - Breaking changes, major functionality updates
            - Installation, setup, or usage procedure changes

            Documentation is NOT needed for:
            - Internal refactoring, code cleanup
            - Test additions, style improvements
            - Bug fixes that don't change behavior
            - Private/internal implementation details

            Respond in JSON format:
            {{"documentation_impact": true/false, "confidence": 0.0-1.0, "reasoning": "brief explanation"}}
            """

            response = self.ollama_client.call_ollama(doc_prompt)
            if not response:
                return None

            # Parse JSON response
            import json

            try:
                result = json.loads(response.strip())
                doc_impact = result.get("documentation_impact", False)
                confidence = float(result.get("confidence", 0.6))
                reasoning = result.get("reasoning", "")

                if isinstance(doc_impact, bool):
                    self.logger.debug(f"LLM doc impact reasoning: {reasoning}")
                    return (doc_impact, confidence)

            except (json.JSONDecodeError, ValueError):
                # Fallback to text parsing if JSON fails
                response_lower = response.lower()
                if (
                    "true" in response_lower
                    or "needed" in response_lower
                    or "required" in response_lower
                ):
                    return (True, 0.6)
                elif (
                    "false" in response_lower
                    or "not needed" in response_lower
                    or "not required" in response_lower
                ):
                    return (False, 0.6)

            return None

        except Exception as e:
            self.logger.debug(f"Error in LLM doc impact extraction: {e}")
            return None

    def _extract_doc_impact_file_analysis(
        self, content: str
    ) -> Optional[tuple[bool, float]]:
        """Extract documentation impact based on file types and change patterns"""
        try:
            content_lower = content.lower()

            # High documentation impact patterns
            high_impact_patterns = [
                "api",
                "endpoint",
                "interface",
                "public",
                "user",
                "client",
                "configuration",
                "config",
                "setup",
                "install",
                "deploy",
                "breaking",
                "major",
                "feature",
                "new",
                "add",
            ]

            # Low documentation impact patterns
            low_impact_patterns = [
                "test",
                "spec",
                "internal",
                "private",
                "refactor",
                "cleanup",
                "style",
                "format",
                "lint",
                "fix",
                "bug",
                "minor",
            ]

            high_count = sum(
                1 for pattern in high_impact_patterns if pattern in content_lower
            )
            low_count = sum(
                1 for pattern in low_impact_patterns if pattern in content_lower
            )

            # Calculate confidence based on pattern matches
            total_patterns = high_count + low_count
            if total_patterns == 0:
                return (False, 0.2)  # Default to no impact with low confidence

            if high_count > low_count:
                confidence = min(0.8, 0.4 + (high_count * 0.1))
                return (True, confidence)
            elif low_count > high_count:
                confidence = min(0.8, 0.4 + (low_count * 0.1))
                return (False, confidence)
            else:
                return (False, 0.3)  # Default to no impact when tied

        except Exception as e:
            self.logger.debug(f"Error in file analysis doc impact extraction: {e}")
            return None

    def _vote_on_doc_impact(
        self, votes: dict, confidence_scores: dict
    ) -> Optional[bool]:
        """Apply voting mechanism to determine final documentation impact"""
        try:
            if not votes:
                return False  # Default to no impact

            # Single vote - return if confidence is reasonable
            if len(votes) == 1:
                method, impact = next(iter(votes.items()))
                confidence = confidence_scores.get(method, 0.5)
                return impact if confidence >= 0.3 else False

            # Multiple votes - weighted by confidence
            impact_scores = {"True": 0.0, "False": 0.0}
            total_weight = 0.0

            for method, impact in votes.items():
                confidence = confidence_scores.get(method, 0.5)
                impact_key = "True" if impact else "False"
                impact_scores[impact_key] += confidence
                total_weight += confidence

            # Normalize scores
            if total_weight > 0:
                for impact in impact_scores:
                    impact_scores[impact] /= total_weight

            # Find winning impact
            winning_impact = max(impact_scores.keys(), key=lambda k: impact_scores[k])
            winning_score = impact_scores[winning_impact]

            # Require minimum confidence threshold
            if winning_score < 0.4:
                return False  # Default to no impact

            # Log voting details
            vote_details = ", ".join(
                [
                    f"{method}:{impact}({confidence_scores.get(method, 0.5):.2f})"
                    for method, impact in votes.items()
                ]
            )
            self.logger.debug(
                f"Doc impact voting: {vote_details} -> {winning_impact} ({winning_score:.2f})"
            )

            return winning_impact == "True"

        except Exception as e:
            self.logger.debug(f"Error in doc impact voting: {e}")
            return False  # Safe fallback

    def _extract_code_review_heuristic(
        self, content: str
    ) -> Optional[tuple[bool, float]]:
        """Extract code review recommendation using heuristic analysis with confidence scoring"""
        try:
            review_section = self._extract_section(
                content, "Code Review Recommendation"
            )
            if not review_section:
                return None

            review_lower = review_section.lower()

            # Explicit negative indicators (high confidence)
            negative_phrases = [
                "not required",
                "no review",
                "skip review",
                "does not require",
                "should not require",
                "not be subject to",
                "not necessary",
            ]
            if any(phrase in review_lower for phrase in negative_phrases):
                return (False, 0.9)

            # Explicit positive indicators (high confidence)
            positive_phrases = [
                "recommended",
                "should be reviewed",
                "should be code reviewed",
                "requires review",
                "should be considered",
                "consider",
                "priority",
            ]
            if any(phrase in review_lower for phrase in positive_phrases):
                return (True, 0.9)

            # Contextual indicators (medium confidence)
            complexity_indicators = [
                "complex",
                "significant",
                "major",
                "breaking",
                "security",
            ]
            simple_indicators = [
                "simple",
                "minor",
                "trivial",
                "straightforward",
                "cleanup",
            ]

            complexity_count = sum(
                1 for indicator in complexity_indicators if indicator in review_lower
            )
            simple_count = sum(
                1 for indicator in simple_indicators if indicator in review_lower
            )

            if complexity_count > simple_count:
                confidence = min(0.7, 0.4 + (complexity_count * 0.1))
                return (True, confidence)
            elif simple_count > complexity_count:
                confidence = min(0.7, 0.4 + (simple_count * 0.1))
                return (False, confidence)
            else:
                return (True, 0.3)  # Default to review recommended with low confidence

        except Exception as e:
            self.logger.debug(f"Error in heuristic code review extraction: {e}")
            return None

    def _extract_code_review_llm(self, content: str) -> Optional[tuple[bool, float]]:
        """Extract code review recommendation using LLM analysis with confidence scoring"""
        try:
            if not self.ollama_client:
                return None

            # Create focused prompt for code review recommendation
            review_prompt = f"""
            Analyze the following code review content and determine if code review is recommended.

            Content: {content[:2000]}  # Limit content to avoid token limits

            Based on the content, determine:
            1. Code review recommended: true (review needed) or false (no review needed)
            2. Confidence level: 0.0 to 1.0 (how certain you are)

            Consider code review is recommended for:
            - Complex logic changes, algorithm modifications
            - Security-related changes, authentication, authorization
            - API changes, breaking changes, major refactoring
            - Database schema changes, production configurations
            - Bug fixes in critical systems

            Code review is NOT needed for:
            - Simple documentation updates, comment changes
            - Trivial formatting, style improvements
            - Test additions without logic changes
            - Minor typo fixes, whitespace cleanup

            Respond in JSON format:
            {{"code_review_recommended": true/false, "confidence": 0.0-1.0, "reasoning": "brief explanation"}}
            """

            response = self.ollama_client.call_ollama(review_prompt)
            if not response:
                return None

            # Parse JSON response
            import json

            try:
                result = json.loads(response.strip())
                review_recommended = result.get("code_review_recommended", True)
                confidence = float(result.get("confidence", 0.6))
                reasoning = result.get("reasoning", "")

                if isinstance(review_recommended, bool):
                    self.logger.debug(f"LLM code review reasoning: {reasoning}")
                    return (review_recommended, confidence)

            except (json.JSONDecodeError, ValueError):
                # Fallback to text parsing if JSON fails
                response_lower = response.lower()
                if (
                    "true" in response_lower
                    or "recommended" in response_lower
                    or "needed" in response_lower
                ):
                    return (True, 0.6)
                elif (
                    "false" in response_lower
                    or "not recommended" in response_lower
                    or "not needed" in response_lower
                ):
                    return (False, 0.6)

            return None

        except Exception as e:
            self.logger.debug(f"Error in LLM code review extraction: {e}")
            return None

    def _extract_code_review_file_analysis(
        self, content: str
    ) -> Optional[tuple[bool, float]]:
        """Extract code review recommendation based on file types and change patterns"""
        try:
            content_lower = content.lower()

            # High review need patterns
            high_review_patterns = [
                "security",
                "auth",
                "login",
                "password",
                "token",
                "crypto",
                "algorithm",
                "logic",
                "complex",
                "critical",
                "production",
                "api",
                "endpoint",
                "breaking",
                "major",
                "refactor",
                "database",
                "migration",
                "schema",
                "config",
            ]

            # Low review need patterns
            low_review_patterns = [
                "test",
                "spec",
                "readme",
                "doc",
                "comment",
                "style",
                "format",
                "lint",
                "typo",
                "whitespace",
                "import",
                "example",
                "sample",
                "demo",
                "minor",
                "trivial",
            ]

            high_count = sum(
                1 for pattern in high_review_patterns if pattern in content_lower
            )
            low_count = sum(
                1 for pattern in low_review_patterns if pattern in content_lower
            )

            # Calculate confidence based on pattern matches
            total_patterns = high_count + low_count
            if total_patterns == 0:
                return (True, 0.3)  # Default to review recommended with low confidence

            if high_count > low_count:
                confidence = min(0.8, 0.5 + (high_count * 0.1))
                return (True, confidence)
            elif low_count > high_count:
                confidence = min(0.8, 0.5 + (low_count * 0.1))
                return (False, confidence)
            else:
                return (True, 0.4)  # Default to review when tied

        except Exception as e:
            self.logger.debug(f"Error in file analysis code review extraction: {e}")
            return None

    def _vote_on_code_review(
        self, votes: dict, confidence_scores: dict
    ) -> Optional[bool]:
        """Apply voting mechanism to determine final code review recommendation"""
        try:
            if not votes:
                return True  # Default to review recommended

            # Single vote - return if confidence is reasonable
            if len(votes) == 1:
                method, recommendation = next(iter(votes.items()))
                confidence = confidence_scores.get(method, 0.5)
                return recommendation if confidence >= 0.3 else True

            # Multiple votes - weighted by confidence
            recommendation_scores = {"True": 0.0, "False": 0.0}
            total_weight = 0.0

            for method, recommendation in votes.items():
                confidence = confidence_scores.get(method, 0.5)
                rec_key = "True" if recommendation else "False"
                recommendation_scores[rec_key] += confidence
                total_weight += confidence

            # Normalize scores
            if total_weight > 0:
                for rec in recommendation_scores:
                    recommendation_scores[rec] /= total_weight

            # Find winning recommendation
            winning_rec = max(
                recommendation_scores.keys(), key=lambda k: recommendation_scores[k]
            )
            winning_score = recommendation_scores[winning_rec]

            # Require minimum confidence threshold
            if winning_score < 0.4:
                return True  # Default to review recommended

            # Log voting details
            vote_details = ", ".join(
                [
                    f"{method}:{rec}({confidence_scores.get(method, 0.5):.2f})"
                    for method, rec in votes.items()
                ]
            )
            self.logger.debug(
                f"Code review voting: {vote_details} -> {winning_rec} ({winning_score:.2f})"
            )

            return winning_rec == "True"

        except Exception as e:
            self.logger.debug(f"Error in code review voting: {e}")
            return True  # Safe fallback

    def _extract_section(self, content: str, section_name: str) -> Optional[str]:
        """Extract a specific section from the document content"""
        try:
            # Look for markdown section headers
            import re

            pattern = rf"##\s*{re.escape(section_name)}\s*\n(.*?)(?=\n##|\Z)"
            match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
            return match.group(1).strip() if match else None
        except Exception:
            return None

    def _extract_ai_summary_from_documentation(
        self, documentation: str
    ) -> Optional[str]:
        """Extract AI-generated summary from documentation content to use as commit message"""
        try:
            # Look for the Summary section in the documentation
            summary_content = self._extract_section(documentation, "Summary")

            if summary_content:
                # Clean up the summary - take first sentence or first line if it's concise
                lines = summary_content.split("\n")
                first_line = lines[0].strip()

                # If first line is a complete sentence and not too long, use it
                if (
                    first_line
                    and len(first_line) <= 100
                    and (first_line.endswith(".") or len(lines) == 1)
                ):
                    return first_line.rstrip(".")

                # Otherwise, try to find a concise summary sentence
                for line in lines:
                    line = line.strip()
                    if (
                        line
                        and len(line) <= 100
                        and not line.startswith("*")
                        and not line.startswith("-")
                    ):
                        return line.rstrip(".")

                # Fallback: use first 80 characters of summary
                if len(first_line) > 0:
                    return (
                        (first_line[:80] + "...")
                        if len(first_line) > 80
                        else first_line.rstrip(".")
                    )

            return None
        except Exception as e:
            self.logger.debug(f"Error extracting AI summary from documentation: {e}")
            return None

    def _get_repository_config(self, repo_id: str) -> Optional[RepositoryConfig]:
        """Get repository configuration by ID"""
        try:
            # Get repository from database via monitor service
            if (
                hasattr(self, "monitor_service")
                and self.monitor_service
                and hasattr(self.monitor_service, "repo_db")
            ):
                repo = self.monitor_service.repo_db.get_repository_by_id(repo_id)
                if repo:
                    return repo

            self.logger.error(f"Repository configuration not found for {repo_id}")
            return None

        except Exception as e:
            self.logger.error(f"Error getting repository config for {repo_id}: {e}")
            return None

    def _is_uuid(self, value: str) -> bool:
        """Check if a string is a valid UUID"""
        try:
            import uuid

            uuid.UUID(value)
            return True
        except (ValueError, AttributeError):
            return False

    def _update_estimated_completion(self, progress: ScanProgress):
        """Update estimated completion time based on current progress"""
        if progress.started_at and progress.processed_revisions > 0:
            elapsed = datetime.now() - progress.started_at
            rate = progress.processed_revisions / elapsed.total_seconds()

            if rate > 0:
                remaining_revisions = (
                    progress.total_revisions - progress.processed_revisions
                )
                remaining_seconds = remaining_revisions / rate
                progress.estimated_completion = datetime.now() + timedelta(
                    seconds=remaining_seconds
                )

    def _notify_progress_callbacks(self, repo_id: str, progress: ScanProgress):
        """Notify all registered progress callbacks"""
        for callback in self.progress_callbacks:
            try:
                callback(repo_id, progress)
            except Exception as e:
                self.logger.error(f"Error in progress callback: {e}")

    def _emit_scan_completion_event(
        self,
        repo_id: str,
        repo_name: str,
        revisions_processed: int,
        success: bool,
        error_message: Optional[str] = None,
    ) -> None:
        """Emit a historical scan completion notification event"""
        if not self.notification_manager:
            return

        try:
            if success:
                title = f"Historical Scan Complete: {repo_name}"
                message = f"Successfully completed historical scan of {repo_name}. Processed {revisions_processed} revisions."
                severity = NotificationSeverity.INFO
            else:
                title = f"Historical Scan Failed: {repo_name}"
                message = f"Historical scan of {repo_name} completed with errors. Processed {revisions_processed} revisions. {error_message or ''}"
                severity = NotificationSeverity.ERROR

            event = EventFactory.create_processing_event(
                title=title,
                message=message,
                repository_id=repo_id,
                success=success,
            )

            # Override severity for failed scans
            event.severity = severity

            # Add scan-specific metadata
            event.metadata.update(
                {
                    "source": "historical_scan",
                    "repository_name": repo_name,
                    "revisions_processed": revisions_processed,
                    "scan_type": "historical",
                }
            )

            if error_message:
                event.metadata["error_message"] = error_message

            self.notification_manager.emit_event(event)

        except Exception as e:
            self.logger.error(f"Error emitting scan completion event: {e}")
