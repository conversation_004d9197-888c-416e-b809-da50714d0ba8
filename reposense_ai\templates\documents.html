{% extends "base.html" %}

{% block title %}Documents - RepoSense AI{% endblock %}

{% block content %}
<style>
    /* Custom styles for different view modes */
    .card-view .card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .card-view .card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .repository-group .card-header {
        cursor: pointer;
    }

    .repository-group .card-header:hover {
        background-color: rgba(13, 202, 240, 0.9) !important;
    }

    .view-mode-indicator {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 10;
    }

    /* Responsive adjustments for cards */
    @media (max-width: 768px) {
        .card-view .col-md-6 {
            margin-bottom: 1rem;
        }
    }

    /* Repository groups styling */
    .repository-group .table th {
        border-top: none;
        font-weight: 600;
        font-size: 0.875rem;
    }

    .repository-group .table td {
        vertical-align: middle;
    }

    /* Filter section styling */
    .filter-section {
        padding: 1rem;
        background-color: #f8f9fa;
        border-radius: 0.375rem;
        border-left: 4px solid #dee2e6;
        margin-bottom: 1.5rem;
    }

    .filter-section:last-child {
        margin-bottom: 0;
    }

    .filter-section h6 {
        color: #495057;
        font-weight: 600;
        margin-bottom: 1rem;
    }

    .filter-section h6 i {
        color: #6c757d;
    }

    /* Responsive table improvements */
    .table-responsive {
        overflow-x: auto;
    }

    /* Ensure Actions column is always visible */
    .table th:last-child,
    .table td:last-child {
        position: sticky;
        right: 0;
        background-color: white;
        z-index: 10;
        border-left: 1px solid #dee2e6;
    }

    .table thead th:last-child {
        background-color: #f8f9fa;
    }

    /* Fix dropdown z-index issues - aggressive approach */
    .table .dropdown-menu {
        z-index: 9999 !important;
        position: absolute !important;
    }

    .table .btn-group {
        position: relative;
        z-index: 30;
    }

    .table .dropdown {
        position: relative !important;
        z-index: 40;
    }

    /* When dropdown is open, ensure it's above everything */
    .table .dropdown.show {
        z-index: 9998 !important;
        position: relative !important;
    }

    .table .dropdown.show .dropdown-menu {
        z-index: 9999 !important;
        position: absolute !important;
    }

    /* Ensure dropdowns appear above sticky elements and sibling buttons */
    .dropdown-menu {
        z-index: 9999 !important;
        position: absolute !important;
    }

    /* Specific fix for button groups with dropdowns */
    .btn-group .dropdown {
        z-index: 40;
        position: relative !important;
    }

    .btn-group .dropdown.show {
        z-index: 9998 !important;
        position: relative !important;
    }

    .btn-group .dropdown-menu {
        z-index: 9999 !important;
        position: absolute !important;
    }

    /* Override Bootstrap/Popper positioning in tables */
    .table .dropdown-menu[data-popper-placement] {
        z-index: 9999 !important;
    }

    /* Compact button groups */
    .btn-group-sm .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }

    /* Better responsive behavior */
    @media (max-width: 1200px) {
        .text-truncate {
            max-width: 180px;
        }
    }

    @media (max-width: 992px) {
        .text-truncate {
            max-width: 150px;
        }
    }

    @media (max-width: 768px) {
        .text-truncate {
            max-width: 120px;
        }

        .btn-group-sm .btn {
            padding: 0.2rem 0.4rem;
            font-size: 0.8rem;
        }
    }
</style>
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Generated Documents</h1>
            <p class="page-subtitle">View and manage AI-generated documentation for repository commits</p>
        </div>

        <!-- Processing Status Indicator -->
        <div id="documents-processing-status" class="d-flex align-items-center" style="display: none;">
            <div class="alert alert-info mb-0 me-3">
                <i class="fas fa-spinner fa-spin me-2"></i>
                <span id="processing-message">Processing documents...</span>
            </div>
        </div>
        <div>
            <button class="btn btn-outline-primary" onclick="refreshDocuments()"
                title="Refresh page to get latest data">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
            <button class="btn btn-outline-secondary ms-2" onclick="clearCacheAndRefresh()"
                title="Clear cache and refresh to force fresh data from database">
                <i class="fas fa-trash-alt"></i> Clear Cache & Refresh
            </button>
            <button class="btn btn-outline-warning ms-2" onclick="forceHardRefresh()"
                title="Force hard refresh - bypass all caches and reload from database">
                <i class="fas fa-exclamation-triangle"></i> Force Hard Refresh
            </button>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-primary" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ total_count }}</h5>
                <p class="card-text text-muted">Total Documents</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-success" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-success">{{ stats|length }}</h5>
                <p class="card-text text-muted">Repositories</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-warning" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ (total_size / 1024)|round(1) }} KB</h5>
                <p class="card-text text-muted">Total Size</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center shadow-sm border-info" style="border-width: 2px;">
            <div class="card-body">
                <h5 class="card-title text-info">{% if documents %}{{ documents[0].date.strftime('%Y-%m-%d') }}{% else
                    %}N/A{% endif %}</h5>
                <p class="card-text text-muted">Latest Document</p>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Filters and Controls -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-info" style="border-width: 2px;">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-filter me-2"></i> Filters & Organization</h6>
                <button class="btn btn-outline-light btn-sm" type="button" data-bs-toggle="collapse"
                    data-bs-target="#filtersCollapse" aria-expanded="false">
                    <i class="fas fa-chevron-down"></i> Toggle Filters
                </button>
            </div>
            <div class="collapse show" id="filtersCollapse">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('documents_page') }}" id="filtersForm">

                        <!-- Search & Basic Filters -->
                        <div class="filter-section">
                            <h6><i class="fas fa-search me-2"></i>Search & Basic Filters</h6>
                            <div class="row g-3">
                                <!-- Search -->
                                <div class="col-md-6">
                                    <label for="search" class="form-label">Search</label>
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" id="search" name="search"
                                            value="{{ search_query or '' }}"
                                            placeholder="Search commit messages, authors, repositories...">
                                    </div>
                                </div>

                                <!-- Repository Filter -->
                                <div class="col-md-3">
                                    <label for="repository" class="form-label">Repository</label>
                                    <select class="form-select" id="repository" name="repository">
                                        <option value="">All Repositories</option>
                                        {% for repo in available_repositories %}
                                        <option value="{{ repo.id }}" {% if repository_filter==repo.id %}selected{%
                                            endif %}>
                                            {{ repo.name }} ({{ repo.doc_count }} docs)
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <!-- Author Filter -->
                                <div class="col-md-3">
                                    <label for="author" class="form-label">Author</label>
                                    <select class="form-select" id="author" name="author">
                                        <option value="">All Authors</option>
                                        {% for author in available_authors %}
                                        <option value="{{ author }}" {% if author_filter==author %}selected{% endif %}>
                                            {{ author }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Time Range -->
                        <div class="filter-section">
                            <h6 class="mb-3"><i class="fas fa-calendar me-2"></i>Time Range</h6>

                            <div class="row g-3">
                                <!-- Commit Date Sub-Panel -->
                                <div class="col-md-6">
                                    <div class="card border-primary" style="border-width: 1px;">
                                        <div
                                            class="card-header bg-primary text-white py-2 d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0"><i class="fas fa-code-branch me-2"></i>Commit Date Range
                                            </h6>
                                            <button type="button" class="btn btn-outline-light btn-sm"
                                                id="clear-commit-dates" title="Clear commit date filters">
                                                <i class="fas fa-times"></i> Clear
                                            </button>
                                        </div>
                                        <div class="card-body p-3">
                                            <!-- Quick Selection Buttons -->
                                            <div class="mb-3">
                                                <label class="form-label text-muted small mb-2">Quick Selection</label>
                                                <div class="btn-group-sm d-flex flex-wrap gap-1" role="group">
                                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                                        id="commit-today" title="Today's commits only">
                                                        <i class="fas fa-calendar-day"></i> Today
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                                        id="commit-last-7-days" title="Last 7 days of commits">
                                                        <i class="fas fa-calendar-week"></i> 7 Days
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                                        id="commit-last-30-days" title="Last 30 days of commits">
                                                        <i class="fas fa-calendar-month"></i> 30 Days
                                                    </button>
                                                    <button type="button" class="btn btn-outline-primary btn-sm"
                                                        id="commit-last-week" title="This week (Monday-Friday)">
                                                        <i class="fas fa-calendar-alt"></i> This Week
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Manual Date Inputs -->
                                            <div class="row g-2">
                                                <div class="col-6">
                                                    <label for="date_from" class="form-label small">From</label>
                                                    <input type="date" class="form-control form-control-sm"
                                                        id="date_from" name="date_from" value="{{ date_from or '' }}"
                                                        title="Filter by commit date">
                                                </div>
                                                <div class="col-6">
                                                    <label for="date_to" class="form-label small">To</label>
                                                    <input type="date" class="form-control form-control-sm" id="date_to"
                                                        name="date_to" value="{{ date_to or '' }}"
                                                        title="Filter by commit date">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- Processing Date Sub-Panel -->
                                <div class="col-md-6">
                                    <div class="card border-success" style="border-width: 1px;">
                                        <div
                                            class="card-header bg-success text-white py-2 d-flex justify-content-between align-items-center">
                                            <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Processing Date Range</h6>
                                            <button type="button" class="btn btn-outline-light btn-sm"
                                                id="clear-processing-dates" title="Clear processing date filters">
                                                <i class="fas fa-times"></i> Clear
                                            </button>
                                        </div>
                                        <div class="card-body p-3">
                                            <!-- Quick Selection Buttons -->
                                            <div class="mb-3">
                                                <label class="form-label text-muted small mb-2">Quick Selection</label>
                                                <div class="btn-group-sm d-flex flex-wrap gap-1" role="group">
                                                    <button type="button" class="btn btn-outline-success btn-sm"
                                                        id="processed-today" title="Today's processing only">
                                                        <i class="fas fa-calendar-day"></i> Today
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success btn-sm"
                                                        id="processed-last-7-days" title="Last 7 days of processing">
                                                        <i class="fas fa-clock"></i> 7 Days
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success btn-sm"
                                                        id="processed-last-30-days" title="Last 30 days of processing">
                                                        <i class="fas fa-history"></i> 30 Days
                                                    </button>
                                                    <button type="button" class="btn btn-outline-success btn-sm"
                                                        id="processed-last-week"
                                                        title="This week of processing (Monday-Friday)">
                                                        <i class="fas fa-calendar-check"></i> This Week
                                                    </button>
                                                </div>
                                            </div>

                                            <!-- Manual Date Inputs -->
                                            <div class="row g-2">
                                                <div class="col-6">
                                                    <label for="processed_date_from"
                                                        class="form-label small">From</label>
                                                    <input type="date" class="form-control form-control-sm"
                                                        id="processed_date_from" name="processed_date_from"
                                                        value="{{ processed_date_from or '' }}"
                                                        title="Filter by processing date">
                                                </div>
                                                <div class="col-6">
                                                    <label for="processed_date_to" class="form-label small">To</label>
                                                    <input type="date" class="form-control form-control-sm"
                                                        id="processed_date_to" name="processed_date_to"
                                                        value="{{ processed_date_to or '' }}"
                                                        title="Filter by processing date">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- AI Analysis Filters -->
                        <div class="filter-section">
                            <h6><i class="fas fa-robot me-2"></i>AI Analysis Filters</h6>
                            <div class="row g-3">
                                <!-- Code Review Filter -->
                                <div class="col-md-4">
                                    <label for="code_review" class="form-label">Code Review</label>
                                    <select class="form-select" id="code_review" name="code_review">
                                        <option value="">All</option>
                                        <option value="true" {% if code_review_filter==true %}selected{% endif %}>
                                            Required</option>
                                        <option value="false" {% if code_review_filter==false %}selected{% endif %}>Not
                                            Required</option>
                                    </select>
                                </div>

                                <!-- Documentation Impact Filter -->
                                <div class="col-md-4">
                                    <label for="doc_impact" class="form-label">Documentation Impact</label>
                                    <select class="form-select" id="doc_impact" name="doc_impact">
                                        <option value="">All</option>
                                        <option value="true" {% if doc_impact_filter==true %}selected{% endif %}>
                                            Required</option>
                                        <option value="false" {% if doc_impact_filter==false %}selected{% endif %}>Not
                                            Required</option>
                                    </select>
                                </div>

                                <!-- Risk Level Filter -->
                                <div class="col-md-4">
                                    <label for="risk_level" class="form-label">Risk Level</label>
                                    <select class="form-select" id="risk_level" name="risk_level">
                                        <option value="">All</option>
                                        <option value="CRITICAL" {% if risk_level_filter=='CRITICAL' %}selected{% endif
                                            %}>Critical</option>
                                        <option value="HIGH" {% if risk_level_filter=='HIGH' %}selected{% endif %}>High
                                        </option>
                                        <option value="MEDIUM" {% if risk_level_filter=='MEDIUM' %}selected{% endif %}>
                                            Medium</option>
                                        <option value="LOW" {% if risk_level_filter=='LOW' %}selected{% endif %}>Low
                                        </option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Display & Organization -->
                        <div class="filter-section">
                            <h6><i class="fas fa-cogs me-2"></i>Display & Organization</h6>
                            <div class="row g-3">
                                <div class="col-md-3">
                                    <label for="sort_by" class="form-label">Sort By</label>
                                    <select class="form-select" id="sort_by" name="sort_by">
                                        <option value="date" {% if sort_by=='date' %}selected{% endif %}>Commit Date
                                        </option>
                                        <option value="processed_time" {% if sort_by=='processed_time' %}selected{%
                                            endif %}>Processing Date</option>
                                        <option value="repository" {% if sort_by=='repository' %}selected{% endif %}>
                                            Repository</option>
                                        <option value="author" {% if sort_by=='author' %}selected{% endif %}>Author
                                        </option>
                                        <option value="revision" {% if sort_by=='revision' %}selected{% endif %}>
                                            Revision</option>
                                        <option value="size" {% if sort_by=='size' %}selected{% endif %}>Size</option>
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label for="sort_order" class="form-label">Order</label>
                                    <select class="form-select" id="sort_order" name="sort_order">
                                        <option value="desc" {% if sort_order=='desc' %}selected{% endif %}>Descending
                                        </option>
                                        <option value="asc" {% if sort_order=='asc' %}selected{% endif %}>Ascending
                                        </option>
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label for="per_page" class="form-label">Per Page</label>
                                    <select class="form-select" id="per_page" name="per_page"
                                        onchange="saveFilterSettings(); this.form.submit();">
                                        <option value="10" {% if per_page==10 %}selected{% endif %}>10</option>
                                        <option value="25" {% if per_page==25 %}selected{% endif %}>25</option>
                                        <option value="50" {% if per_page==50 %}selected{% endif %}>50</option>
                                        <option value="100" {% if per_page==100 %}selected{% endif %}>100</option>
                                    </select>
                                </div>

                                <div class="col-md-2">
                                    <label for="view_mode" class="form-label">View Mode</label>
                                    <select class="form-select" id="view_mode" name="view_mode">
                                        <option value="table" {% if view_mode=='table' %}selected{% endif %}>Table
                                        </option>
                                        <option value="cards" {% if view_mode=='cards' %}selected{% endif %}>Cards
                                        </option>
                                        <option value="repository_groups" {% if view_mode=='repository_groups'
                                            %}selected{% endif %}>By Repository</option>
                                    </select>
                                </div>

                                <div class="col-md-3 d-flex align-items-end">
                                    <div class="btn-group w-100" role="group">
                                        <button type="submit" class="btn btn-primary" onclick="saveFilterSettings()">
                                            <i class="fas fa-filter"></i> Apply Filters
                                        </button>
                                        <a href="{{ url_for('documents_page') }}" class="btn btn-outline-secondary"
                                            onclick="clearFilterSettings()">
                                            <i class="fas fa-times"></i> Clear All
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Hidden fields to preserve pagination -->
                        <input type="hidden" name="page" value="1">
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Documents Table -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm border-primary" style="border-width: 2px;">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-file-alt me-2"></i> Documents
                    {% if total_count > 0 %}
                    <span class="badge bg-light text-primary ms-2">{{ total_count }} found</span>
                    {% endif %}
                    <!-- View Mode Indicator -->
                    <span class="badge bg-secondary ms-2">
                        {% if view_mode == 'cards' %}
                        <i class="fas fa-th-large me-1"></i>Cards View
                        {% elif view_mode == 'repository_groups' %}
                        <i class="fas fa-layer-group me-1"></i>By Repository
                        {% else %}
                        <i class="fas fa-table me-1"></i>Table View
                        {% endif %}
                    </span>
                </h5>
                <div>
                    <button class="btn btn-outline-light btn-sm" onclick="cleanupOrphanedDocuments()"
                        title="Remove orphaned documents">
                        <i class="fas fa-broom"></i> Cleanup
                    </button>
                    <button class="btn btn-outline-light btn-sm" onclick="deleteAllDocuments()"
                        title="Delete documents (respects current filters - deletes only visible/filtered documents, or all documents if no filters active)">
                        <i class="fas fa-trash"></i> Delete All
                    </button>

                </div>
            </div>
            <div class="card-body">
                {% if documents %}
                <!-- Table View -->
                {% if view_mode == 'table' or not view_mode %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th style="min-width: 120px;">Repository</th>
                                <th style="width: 80px;">Revision</th>
                                <th style="width: 130px;">Commit Date</th>
                                <th style="width: 130px;">Processing Date</th>
                                <th class="d-none d-lg-table-cell" style="width: 120px;">Author</th>
                                <th style="min-width: 200px;">Commit Message</th>
                                <th style="width: 100px;">Change Request</th>
                                <th class="d-none d-xl-table-cell" style="width: 100px;">AI Model</th>
                                <th style="width: 90px;">Code Review</th>
                                <th class="d-none d-lg-table-cell" style="width: 90px;">Docs Impact</th>
                                <th style="width: 80px;">Risk Level</th>
                                <th class="d-none d-xl-table-cell" style="width: 100px;">User Feedback</th>
                                <th class="d-none d-md-table-cell" style="width: 70px;">Size</th>
                                <th style="width: 140px;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for doc in documents %}
                            <tr>
                                <td>
                                    <span class="badge bg-primary">{{ doc.repository_name }}</span>
                                </td>
                                <td>
                                    <strong>{{ doc.revision }}</strong>
                                </td>
                                <td>
                                    <small class="text-muted">{{ doc.date.strftime('%Y-%m-%d %H:%M') }}</small>
                                </td>
                                <td>
                                    {% if doc.processed_time %}
                                    <small class="text-muted">{{ doc.processed_time.strftime('%Y-%m-%d %H:%M')
                                        }}</small>
                                    {% else %}
                                    <small class="text-muted">-</small>
                                    {% endif %}
                                </td>
                                <td class="d-none d-lg-table-cell">{{ doc.author }}</td>
                                <td>
                                    <span class="text-truncate d-inline-block" style="max-width: 250px;"
                                        title="{{ doc.commit_message }}">
                                        {{ doc.commit_message }}
                                    </span>
                                </td>
                                <td>
                                    {% if doc.change_request_numbers and doc.change_request_numbers|length > 0 %}
                                    {% for cr_num in doc.change_request_numbers %}
                                    <span class="badge bg-primary me-1" title="Change Request #{{ cr_num }}">
                                        <i class="fas fa-ticket-alt me-1"></i>{{ cr_num }}
                                    </span>
                                    {% endfor %}
                                    {% else %}
                                    <span class="text-muted">-</span>
                                    {% endif %}
                                </td>
                                <td class="d-none d-xl-table-cell">
                                    {% if doc.ai_model_used %}
                                    <span class="badge bg-success" title="AI Model: {{ doc.ai_model_used }}">
                                        <i class="fas fa-robot me-1"></i>{{ doc.ai_model_used }}
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary" title="No AI model information">
                                        <i class="fas fa-question me-1"></i>N/A
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if doc.code_review_recommended is not none %}
                                    {% if doc.code_review_recommended %}
                                    <span class="badge bg-warning text-dark" title="Code review recommended">
                                        <i class="fas fa-eye"></i>
                                        {% if doc.code_review_priority %}{{ doc.code_review_priority }}{% else %}YES{%
                                        endif %}
                                    </span>
                                    {% else %}
                                    <span class="badge bg-success" title="No code review needed">
                                        <i class="fas fa-check"></i> NO
                                    </span>
                                    {% endif %}
                                    {% else %}
                                    <span class="badge bg-secondary" title="Not analyzed">
                                        <i class="fas fa-question"></i> N/A
                                    </span>
                                    {% endif %}
                                </td>
                                <td class="d-none d-lg-table-cell">
                                    {% if doc.documentation_impact is not none %}
                                    {% if doc.documentation_impact %}
                                    <span class="badge bg-info" title="Documentation updates needed">
                                        <i class="fas fa-file-alt"></i> YES
                                    </span>
                                    {% else %}
                                    <span class="badge bg-success" title="No documentation impact">
                                        <i class="fas fa-check"></i> NO
                                    </span>
                                    {% endif %}
                                    {% else %}
                                    <span class="badge bg-secondary" title="Not analyzed">
                                        <i class="fas fa-question"></i> N/A
                                    </span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if doc.risk_level %}
                                    <span
                                        class="badge bg-{{ 'dark' if doc.risk_level == 'CRITICAL' else 'danger' if doc.risk_level == 'HIGH' else 'warning' if doc.risk_level == 'MEDIUM' else 'success' }}"
                                        title="Risk Level: {{ doc.risk_level }}{% if doc.heuristic_context and doc.heuristic_context.get('indicators', {}).get('risk_confidence') %} (Confidence: {{ doc.heuristic_context.indicators.risk_confidence }}){% endif %}">
                                        {% if doc.risk_level == 'CRITICAL' %}
                                        <i class="fas fa-skull-crossbones"></i>
                                        {% elif doc.risk_level == 'HIGH' %}
                                        <i class="fas fa-exclamation-triangle"></i>
                                        {% elif doc.risk_level == 'MEDIUM' %}
                                        <i class="fas fa-exclamation-circle"></i>
                                        {% elif doc.risk_level == 'LOW' %}
                                        <i class="fas fa-info-circle"></i>
                                        {% endif %}
                                        {{ doc.risk_level }}
                                        {% if doc.heuristic_context and doc.heuristic_context.get('indicators',
                                        {}).get('risk_confidence') %}
                                        <br><small class="opacity-75">{{
                                            (doc.heuristic_context.indicators.risk_confidence | float * 100) | round
                                            }}%</small>
                                        {% endif %}
                                    </span>
                                    {% else %}
                                    <span class="badge bg-light text-dark">Unknown</span>
                                    {% endif %}
                                </td>
                                <td class="d-none d-xl-table-cell">
                                    <!-- User Feedback Indicators -->
                                    <div class="d-flex gap-1">
                                        {% if doc.user_code_review_status %}
                                        <span
                                            class="badge bg-{{ 'success' if doc.user_code_review_status == 'approved' else 'warning' if doc.user_code_review_status == 'needs_changes' else 'danger' if doc.user_code_review_status == 'rejected' else 'info' }}"
                                            title="Code Review: {{ doc.user_code_review_status|title }}">
                                            <i class="fas fa-code-branch"></i>
                                        </span>
                                        {% endif %}
                                        {% if doc.user_documentation_rating %}
                                        <span
                                            class="badge bg-{{ 'success' if doc.user_documentation_rating >= 4 else 'warning' if doc.user_documentation_rating >= 3 else 'danger' }}"
                                            title="Documentation Rating: {{ doc.user_documentation_rating }}/5">
                                            <i class="fas fa-star"></i> {{ doc.user_documentation_rating }}
                                        </span>
                                        {% endif %}
                                        {% if doc.user_risk_assessment_override %}
                                        <span
                                            class="badge bg-{{ 'dark' if doc.user_risk_assessment_override == 'CRITICAL' else 'danger' if doc.user_risk_assessment_override == 'HIGH' else 'warning' if doc.user_risk_assessment_override == 'MEDIUM' else 'success' }}"
                                            title="Risk Override: {{ doc.user_risk_assessment_override }}">
                                            <i
                                                class="fas fa-{{ 'skull-crossbones' if doc.user_risk_assessment_override == 'CRITICAL' else 'exclamation-triangle' }}"></i>
                                        </span>
                                        {% endif %}
                                        {% if not doc.user_code_review_status and not doc.user_documentation_rating and
                                        not doc.user_risk_assessment_override %}
                                        <small class="text-muted">No feedback</small>
                                        {% endif %}
                                    </div>
                                </td>
                                <td class="d-none d-md-table-cell">
                                    <small class="text-muted">{{ (doc.size / 1024)|round(1) }} KB</small>
                                </td>
                                <td>
                                    <div class="btn-group btn-group-sm" role="group">
                                        <a href="{{ url_for('view_document', doc_id=doc.id) }}?v={{ (doc.processed_time.timestamp() if doc.processed_time else doc.file_modified_time)|int }}"
                                            class="btn btn-outline-primary" title="View Document">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <div class="dropdown">
                                            <button class="btn btn-outline-secondary dropdown-toggle" type="button"
                                                data-bs-toggle="dropdown" aria-expanded="false" title="More Actions"
                                                data-bs-boundary="viewport" data-bs-container="body">
                                                <i class="fas fa-ellipsis-v"></i>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-end">
                                                <li>
                                                    <button class="dropdown-item" onclick="toggleDiff('{{ doc.id }}')">
                                                        <i class="fas fa-code me-2"></i>View Diff
                                                    </button>
                                                </li>
                                                <li>
                                                    <a class="dropdown-item"
                                                        href="{{ url_for('view_document', doc_id=doc.id, include_diff='true', diff_format='side-by-side') }}">
                                                        <i class="fas fa-columns me-2"></i>Side-by-Side Diff
                                                    </a>
                                                </li>
                                                <li>
                                                    <hr class="dropdown-divider">
                                                </li>
                                                <li>
                                                    <button class="dropdown-item"
                                                        onclick="showRescanModal('{{ doc.id }}', '{{ doc.display_name }}')">
                                                        <i class="fas fa-redo me-2"></i>Rescan
                                                    </button>
                                                </li>
                                                <li>
                                                    <hr class="dropdown-divider">
                                                </li>
                                                <li>
                                                    <button class="dropdown-item text-danger"
                                                        onclick="deleteDocument('{{ doc.id }}', '{{ doc.display_name }}')">
                                                        <i class="fas fa-trash me-2"></i>Delete
                                                    </button>
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            <!-- Diff content row (initially hidden) -->
                            <tr id="diff-row-{{ doc.id }}" class="diff-row" style="display: none;">
                                <td colspan="13" class="p-0">
                                    <div class="bg-light border-top">
                                        <div class="p-3">
                                            <div class="d-flex justify-content-between align-items-center mb-2">
                                                <h6 class="mb-0 text-muted">
                                                    <i class="fas fa-code"></i> Code Changes (Diff)
                                                </h6>
                                                <div class="d-flex align-items-center gap-2">
                                                    <div class="btn-group btn-group-sm" role="group">
                                                        <input type="radio" class="btn-check"
                                                            name="diffFormat{{ doc.id }}" id="unified{{ doc.id }}"
                                                            value="unified" checked>
                                                        <label class="btn btn-outline-secondary"
                                                            for="unified{{ doc.id }}"
                                                            onclick="changeDiffFormat('{{ doc.id }}', 'unified')">Unified</label>

                                                        <input type="radio" class="btn-check"
                                                            name="diffFormat{{ doc.id }}" id="sideBySide{{ doc.id }}"
                                                            value="side-by-side">
                                                        <label class="btn btn-outline-secondary"
                                                            for="sideBySide{{ doc.id }}"
                                                            onclick="changeDiffFormat('{{ doc.id }}', 'side-by-side')">Side-by-Side</label>
                                                    </div>
                                                    <button class="btn btn-sm btn-outline-secondary"
                                                        onclick="toggleDiff('{{ doc.id }}')" title="Hide Diff">
                                                        <i class="fas fa-times"></i>
                                                    </button>
                                                </div>
                                            </div>
                                            <div id="diff-content-{{ doc.id }}" class="diff-content">
                                                <div class="text-center text-muted py-3">
                                                    <i class="fas fa-spinner fa-spin"></i> Loading diff...
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}

                <!-- Cards View -->
                {% if view_mode == 'cards' %}
                <div class="row card-view">
                    {% for doc in documents %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 shadow-sm">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <span class="badge bg-primary">{{ doc.repository_name }}</span>
                                <small class="text-muted">Rev {{ doc.revision }}</small>
                            </div>
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="fas fa-user me-1"></i>{{ doc.author }}
                                </h6>
                                <p class="card-text text-truncate" title="{{ doc.commit_message }}">
                                    {{ doc.commit_message }}
                                </p>
                                <div class="row text-center mb-3">
                                    <div class="col-4">
                                        {% if doc.code_review_recommended %}
                                        <span class="badge bg-warning">Review Required</span>
                                        {% else %}
                                        <span class="badge bg-success">No Review</span>
                                        {% endif %}
                                    </div>
                                    <div class="col-4">
                                        {% if doc.documentation_impact %}
                                        <span class="badge bg-info">Docs Update</span>
                                        {% else %}
                                        <span class="badge bg-secondary">No Docs</span>
                                        {% endif %}
                                    </div>
                                    <div class="col-4">
                                        {% if doc.risk_level %}
                                        <span
                                            class="badge bg-{{ 'dark' if doc.risk_level == 'CRITICAL' else 'danger' if doc.risk_level == 'HIGH' else 'warning' if doc.risk_level == 'MEDIUM' else 'success' }}"
                                            title="Risk Level: {{ doc.risk_level }}{% if doc.heuristic_context and doc.heuristic_context.get('indicators', {}).get('risk_confidence') %} (Confidence: {{ doc.heuristic_context.indicators.risk_confidence }}){% endif %}">
                                            {% if doc.risk_level == 'CRITICAL' %}
                                            <i class="fas fa-skull-crossbones"></i>
                                            {% elif doc.risk_level == 'HIGH' %}
                                            <i class="fas fa-exclamation-triangle"></i>
                                            {% elif doc.risk_level == 'MEDIUM' %}
                                            <i class="fas fa-exclamation-circle"></i>
                                            {% elif doc.risk_level == 'LOW' %}
                                            <i class="fas fa-info-circle"></i>
                                            {% endif %}
                                            {{ doc.risk_level }}
                                            {% if doc.heuristic_context and doc.heuristic_context.get('indicators',
                                            {}).get('risk_confidence') %}
                                            <small class="opacity-75">({{
                                                (doc.heuristic_context.indicators.risk_confidence | float * 100) | round
                                                }}%)</small>
                                            {% endif %}
                                        </span>
                                        {% else %}
                                        <span class="badge bg-light text-dark">Unknown</span>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex justify-content-between align-items-center">
                                <div class="d-flex flex-column">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar me-1"></i>Commit: {{ doc.date.strftime('%Y-%m-%d
                                        %H:%M') }}
                                    </small>
                                    {% if doc.processed_time %}
                                    <small class="text-muted mt-1">
                                        <i class="fas fa-clock me-1"></i>Processed: {{
                                        doc.processed_time.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                    {% endif %}
                                    {% if doc.ai_model_used %}
                                    <small class="text-success mt-1">
                                        <i class="fas fa-robot me-1"></i>{{ doc.ai_model_used }}
                                    </small>
                                    {% endif %}
                                </div>
                                <div class="btn-group btn-group-sm">
                                    <a href="{{ url_for('view_document', doc_id=doc.id) }}?v={{ (doc.processed_time.timestamp() if doc.processed_time else doc.file_modified_time)|int }}"
                                        class="btn btn-outline-primary" title="View Document">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <button class="btn btn-outline-info" onclick="toggleDiff('{{ doc.id }}')"
                                        title="View Diff">
                                        <i class="fas fa-code"></i>
                                    </button>
                                    <button class="btn btn-outline-danger"
                                        onclick="deleteDocument('{{ doc.id }}', '{{ doc.display_name }}')"
                                        title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Repository Groups View -->
                {% if view_mode == 'repository_groups' %}
                {% set grouped_docs = documents | groupby('repository_name') %}
                {% for repository_name, repo_docs in grouped_docs %}
                <div class="mb-4 repository-group">
                    <div class="card shadow-sm">
                        <div class="card-header bg-info text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-folder me-2"></i>{{ repository_name }}
                                <span class="badge bg-light text-info ms-2">{{ repo_docs | list | length }}
                                    documents</span>
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th style="width: 80px;">Revision</th>
                                            <th style="width: 130px;">Commit Date</th>
                                            <th style="width: 130px;">Processing Date</th>
                                            <th class="d-none d-lg-table-cell" style="width: 120px;">Author</th>
                                            <th style="min-width: 200px;">Commit Message</th>
                                            <th style="width: 100px;">Change Request</th>
                                            <th class="d-none d-xl-table-cell" style="width: 100px;">AI Model</th>
                                            <th style="width: 90px;">Code Review</th>
                                            <th class="d-none d-lg-table-cell" style="width: 90px;">Docs Impact</th>
                                            <th style="width: 80px;">Risk</th>
                                            <th style="width: 100px;">Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for doc in repo_docs %}
                                        <tr>
                                            <td><strong>{{ doc.revision }}</strong></td>
                                            <td><small class="text-muted">{{ doc.date.strftime('%Y-%m-%d %H:%M')
                                                    }}</small></td>
                                            <td>
                                                {% if doc.processed_time %}
                                                <small class="text-muted">{{ doc.processed_time.strftime('%Y-%m-%d
                                                    %H:%M') }}</small>
                                                {% else %}
                                                <small class="text-muted">-</small>
                                                {% endif %}
                                            </td>
                                            <td class="d-none d-lg-table-cell">{{ doc.author }}</td>
                                            <td>
                                                <span class="text-truncate d-inline-block" style="max-width: 200px;"
                                                    title="{{ doc.commit_message }}">
                                                    {{ doc.commit_message }}
                                                </span>
                                            </td>
                                            <td>
                                                {% if doc.change_request_numbers and doc.change_request_numbers|length >
                                                0 %}
                                                {% for cr_num in doc.change_request_numbers %}
                                                <span class="badge bg-primary me-1"
                                                    title="Change Request #{{ cr_num }}">
                                                    <i class="fas fa-ticket-alt me-1"></i>{{ cr_num }}
                                                </span>
                                                {% endfor %}
                                                {% else %}
                                                <span class="text-muted">-</span>
                                                {% endif %}
                                            </td>
                                            <td class="d-none d-xl-table-cell">
                                                {% if doc.ai_model_used %}
                                                <span class="badge bg-success"
                                                    title="AI Model: {{ doc.ai_model_used }}">
                                                    <i class="fas fa-robot me-1"></i>{{ doc.ai_model_used }}
                                                </span>
                                                {% else %}
                                                <span class="badge bg-secondary" title="No AI model information">
                                                    <i class="fas fa-question me-1"></i>N/A
                                                </span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if doc.code_review_recommended %}
                                                <span class="badge bg-warning">Required</span>
                                                {% else %}
                                                <span class="badge bg-success">Not Required</span>
                                                {% endif %}
                                            </td>
                                            <td class="d-none d-lg-table-cell">
                                                {% if doc.documentation_impact %}
                                                <span class="badge bg-info">Required</span>
                                                {% else %}
                                                <span class="badge bg-secondary">Not Required</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                {% if doc.risk_level %}
                                                <span
                                                    class="badge bg-{{ 'dark' if doc.risk_level == 'CRITICAL' else 'danger' if doc.risk_level == 'HIGH' else 'warning' if doc.risk_level == 'MEDIUM' else 'success' }}"
                                                    title="Risk Level: {{ doc.risk_level }}{% if doc.heuristic_context and doc.heuristic_context.get('indicators', {}).get('risk_confidence') %} (Confidence: {{ doc.heuristic_context.indicators.risk_confidence }}){% endif %}">
                                                    {% if doc.risk_level == 'CRITICAL' %}
                                                    <i class="fas fa-skull-crossbones"></i>
                                                    {% elif doc.risk_level == 'HIGH' %}
                                                    <i class="fas fa-exclamation-triangle"></i>
                                                    {% elif doc.risk_level == 'MEDIUM' %}
                                                    <i class="fas fa-exclamation-circle"></i>
                                                    {% elif doc.risk_level == 'LOW' %}
                                                    <i class="fas fa-info-circle"></i>
                                                    {% endif %}
                                                    {{ doc.risk_level }}
                                                    {% if doc.heuristic_context and
                                                    doc.heuristic_context.get('indicators', {}).get('risk_confidence')
                                                    %}
                                                    <br><small class="opacity-75">{{
                                                        (doc.heuristic_context.indicators.risk_confidence | float * 100)
                                                        | round }}% confidence</small>
                                                    {% endif %}
                                                </span>
                                                {% else %}
                                                <span class="badge bg-light text-dark">Unknown</span>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm" role="group">
                                                    <a href="{{ url_for('view_document', doc_id=doc.id) }}?v={{ (doc.processed_time.timestamp() if doc.processed_time else doc.file_modified_time)|int }}"
                                                        class="btn btn-outline-primary" title="View Document">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <div class="dropdown">
                                                        <button class="btn btn-outline-secondary dropdown-toggle"
                                                            type="button" data-bs-toggle="dropdown"
                                                            aria-expanded="false" title="More Actions"
                                                            data-bs-boundary="viewport" data-bs-container="body">
                                                            <i class="fas fa-ellipsis-v"></i>
                                                        </button>
                                                        <ul class="dropdown-menu dropdown-menu-end">
                                                            <li>
                                                                <button class="dropdown-item"
                                                                    onclick="toggleDiff('{{ doc.id }}')">
                                                                    <i class="fas fa-code me-2"></i>View Diff
                                                                </button>
                                                            </li>
                                                            <li>
                                                                <hr class="dropdown-divider">
                                                            </li>
                                                            <li>
                                                                <button class="dropdown-item"
                                                                    onclick="showRescanModal('{{ doc.id }}', '{{ doc.display_name }}')">
                                                                    <i class="fas fa-redo me-2"></i>Rescan
                                                                </button>
                                                            </li>
                                                            <li>
                                                                <hr class="dropdown-divider">
                                                            </li>
                                                            <li>
                                                                <button class="dropdown-item text-danger"
                                                                    onclick="deleteDocument('{{ doc.id }}', '{{ doc.display_name }}')">
                                                                    <i class="fas fa-trash me-2"></i>Delete
                                                                </button>
                                                            </li>
                                                        </ul>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                {% endfor %}
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-file-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No Documents Found</h5>
                    <p class="text-muted">Documents will appear here after commits are processed and analyzed by the AI.
                    </p>
                    <a href="{{ url_for('index') }}" class="btn btn-primary">
                        <i class="fas fa-arrow-left"></i> Back to Dashboard
                    </a>
                </div>
                {% endif %}

                <!-- Pagination Controls -->
                {% if total_count > 0 %}
                <div class="d-flex justify-content-between align-items-center mt-4 px-3 pb-3">
                    <div class="d-flex align-items-center">
                        <div class="text-muted me-3">
                            Showing {{ ((page - 1) * per_page + 1) }} to {{ [page * per_page, total_count]|min }} of {{
                            total_count }} documents
                        </div>
                        <div class="d-flex align-items-center">
                            <label for="perPageSelect" class="form-label text-muted me-2 mb-0">Per page:</label>
                            <select id="perPageSelect" class="form-select form-select-sm" style="width: auto;"
                                onchange="changePerPage(this.value)">
                                <option value="10" {% if per_page==10 %}selected{% endif %}>10</option>
                                <option value="25" {% if per_page==25 %}selected{% endif %}>25</option>
                                <option value="50" {% if per_page==50 %}selected{% endif %}>50</option>
                                <option value="100" {% if per_page==100 %}selected{% endif %}>100</option>
                                <option value="200" {% if per_page==200 %}selected{% endif %}>200</option>
                            </select>
                        </div>
                    </div>
                    {% if total_pages > 1 %}
                    <nav aria-label="Documents pagination">
                        <ul class="pagination pagination-sm mb-0">
                            <!-- Previous Page -->
                            {% if page > 1 %}
                            <li class="page-item">
                                <a class="page-link"
                                    href="{{ url_for('documents_page', page=page-1, per_page=per_page, repository=repository_filter, code_review=code_review_filter, doc_impact=doc_impact_filter, risk_level=risk_level_filter, author=author_filter, date_from=date_from, date_to=date_to, search=search_query, sort_by=sort_by, sort_order=sort_order, view_mode=view_mode) }}">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <span class="page-link">
                                    <i class="fas fa-chevron-left"></i> Previous
                                </span>
                            </li>
                            {% endif %}

                            <!-- Page Numbers -->
                            {% set start_page = [1, page - 2]|max %}
                            {% set end_page = [total_pages, page + 2]|min %}

                            {% if start_page > 1 %}
                            <li class="page-item">
                                <a class="page-link"
                                    href="{{ url_for('documents_page', page=1, per_page=per_page, repository=repository_filter, code_review=code_review_filter, doc_impact=doc_impact_filter, risk_level=risk_level_filter, author=author_filter, date_from=date_from, date_to=date_to, search=search_query, sort_by=sort_by, sort_order=sort_order, view_mode=view_mode) }}">1</a>
                            </li>
                            {% if start_page > 2 %}
                            <li class="page-item disabled">
                                <span class="page-link">...</span>
                            </li>
                            {% endif %}
                            {% endif %}

                            {% for p in range(start_page, end_page + 1) %}
                            {% if p == page %}
                            <li class="page-item active">
                                <span class="page-link">{{ p }}</span>
                            </li>
                            {% else %}
                            <li class="page-item">
                                <a class="page-link"
                                    href="{{ url_for('documents_page', page=p, per_page=per_page, repository=repository_filter, code_review=code_review_filter, doc_impact=doc_impact_filter, risk_level=risk_level_filter, author=author_filter, date_from=date_from, date_to=date_to, search=search_query, sort_by=sort_by, sort_order=sort_order, view_mode=view_mode) }}">{{
                                    p }}</a>
                            </li>
                            {% endif %}
                            {% endfor %}

                            {% if end_page < total_pages %} {% if end_page < total_pages - 1 %} <li
                                class="page-item disabled">
                                <span class="page-link">...</span>
                                </li>
                                {% endif %}
                                <li class="page-item">
                                    <a class="page-link"
                                        href="{{ url_for('documents_page', page=total_pages, per_page=per_page, repository=repository_filter, code_review=code_review_filter, doc_impact=doc_impact_filter, risk_level=risk_level_filter, author=author_filter, date_from=date_from, date_to=date_to, search=search_query, sort_by=sort_by, sort_order=sort_order, view_mode=view_mode) }}">{{
                                        total_pages }}</a>
                                </li>
                                {% endif %}

                                <!-- Next Page -->
                                {% if page < total_pages %} <li class="page-item">
                                    <a class="page-link"
                                        href="{{ url_for('documents_page', page=page+1, per_page=per_page, repository=repository_filter, code_review=code_review_filter, doc_impact=doc_impact_filter, risk_level=risk_level_filter, author=author_filter, date_from=date_from, date_to=date_to, search=search_query, sort_by=sort_by, sort_order=sort_order, view_mode=view_mode) }}">
                                        Next <i class="fas fa-chevron-right"></i>
                                    </a>
                                    </li>
                                    {% else %}
                                    <li class="page-item disabled">
                                        <span class="page-link">
                                            Next <i class="fas fa-chevron-right"></i>
                                        </span>
                                    </li>
                                    {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteDocumentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Delete Document</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to delete the document:</p>
                <p><strong id="deleteDocumentName"></strong></p>
                <p class="text-warning">
                    <i class="fas fa-exclamation-triangle"></i>
                    This action cannot be undone.
                </p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <form id="deleteDocumentForm" method="post" style="display: inline;">
                    <button type="submit" class="btn btn-danger">
                        <i class="fas fa-trash"></i> Delete Document
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- Rescan Document Modal -->
<div class="modal fade" id="rescanModal" tabindex="-1" aria-labelledby="rescanModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="rescanModalLabel">
                    <i class="fas fa-redo text-warning me-2"></i>Rescan Document with AI Model
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    <strong>Rescan Document:</strong> This will reprocess the selected document with a different AI
                    model.
                    The existing analysis will be replaced with new AI-generated content.
                </div>

                <div class="mb-3">
                    <label class="form-label"><strong>Document:</strong></label>
                    <div id="rescanDocumentInfo" class="p-2 bg-light rounded">
                        <!-- Document info will be populated here -->
                    </div>
                </div>

                <div class="mb-3">
                    <label for="rescanModelSelect" class="form-label">
                        <i class="fas fa-robot me-2"></i><strong>Select AI Model:</strong>
                    </label>
                    <select class="form-select" id="rescanModelSelect">
                        <option value="">Loading models...</option>
                    </select>
                    <div class="form-text">
                        Choose a different AI model to reprocess this document. Different models may provide
                        different perspectives on code review, documentation impact, and risk assessment.
                    </div>
                </div>

                <div class="mb-3">
                    <label for="rescanAggressivenessSelect" class="form-label">
                        <i class="fas fa-shield-alt me-2"></i><strong>Risk Assessment Aggressiveness:</strong>
                    </label>
                    <select class="form-select" id="rescanAggressivenessSelect">
                        <option value="CONSERVATIVE">Conservative - High scrutiny, stricter thresholds (best for
                            stable/legacy systems)</option>
                        <option value="BALANCED" selected>Balanced - Standard risk assessment for active development
                        </option>
                        <option value="AGGRESSIVE">Aggressive - More tolerant of changes (fast-moving development)
                        </option>
                        <option value="VERY_AGGRESSIVE">Very Aggressive - Most tolerant (experimental/prototype code)
                        </option>
                    </select>
                    <div class="form-text">
                        Control how strictly the AI assesses risk levels. Conservative settings flag more changes as
                        high-risk,
                        while aggressive settings are more tolerant of code changes.
                    </div>
                </div>

                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="rescanPreserveUserFeedback" checked>
                        <label class="form-check-label" for="rescanPreserveUserFeedback">
                            Preserve existing user feedback and ratings
                        </label>
                    </div>
                </div>

                <div id="rescanStatus" class="mt-3" style="display: none;">
                    <!-- Status messages will appear here -->
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-warning" id="confirmRescanBtn">
                    <i class="fas fa-redo me-2"></i>Rescan Document
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block scripts %}
<script>
    function refreshDocuments() {
        location.reload();
    }

    function clearCacheAndRefresh() {
        // Clear cache first, then refresh
        fetch('/api/documents/clear-cache', {
            method: 'POST'
        })
            .then(response => response.json())
            .then(data => {
                console.log('Cache cleared:', data);
                // Reload after cache clear
                location.reload();
            })
            .catch(error => {
                console.error('Error clearing cache:', error);
                // Reload anyway
                location.reload();
            });
    }

    function forceHardRefresh() {
        // Force hard refresh - bypass all caches
        console.log('Forcing hard refresh - bypassing all caches');

        // Clear browser cache for this page
        if ('caches' in window) {
            caches.keys().then(function (names) {
                for (let name of names) {
                    caches.delete(name);
                }
            });
        }

        // Clear server-side cache and force reload with cache-busting
        fetch('/api/documents/clear-cache', {
            method: 'POST',
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        })
            .then(response => response.json())
            .then(data => {
                console.log('Server cache cleared:', data);
                // Force reload with cache-busting timestamp
                const timestamp = new Date().getTime();
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('_t', timestamp);
                window.location.href = currentUrl.toString();
            })
            .catch(error => {
                console.error('Error clearing server cache:', error);
                // Force reload anyway with cache-busting
                const timestamp = new Date().getTime();
                const currentUrl = new URL(window.location);
                currentUrl.searchParams.set('_t', timestamp);
                window.location.href = currentUrl.toString();
            });
    }

    function loadDocuments(page = 1, forceRefresh = false) {
        // If forceRefresh is true or we're changing pages, do a full reload to ensure consistency
        if (forceRefresh || page !== getCurrentPage()) {
            // Get current URL parameters to preserve filters
            const urlParams = new URLSearchParams(window.location.search);

            // Update page parameter
            urlParams.set('page', page);

            // Reload with current filters but updated page
            window.location.search = urlParams.toString();
        } else {
            // For same-page refreshes, just reload the page to ensure we get the latest data
            // This is simpler and more reliable than trying to update the DOM
            location.reload();
        }
    }

    function getCurrentPage() {
        const urlParams = new URLSearchParams(window.location.search);
        return parseInt(urlParams.get('page') || '1');
    }



    function changePerPage(newPerPage) {
        // Get current URL parameters
        const urlParams = new URLSearchParams(window.location.search);

        // Update per_page parameter
        urlParams.set('per_page', newPerPage);

        // Reset to page 1 when changing per_page to avoid empty pages
        urlParams.set('page', '1');

        // Navigate to new URL
        window.location.search = urlParams.toString();
    }

    // Enhanced filter functionality
    function clearAllFilters() {
        // Clear all form inputs
        document.getElementById('filtersForm').reset();

        // Navigate to clean URL
        window.location.href = "{{ url_for('documents_page') }}";
    }

    function applyQuickFilter(filterType, filterValue) {
        const urlParams = new URLSearchParams(window.location.search);
        urlParams.set(filterType, filterValue);
        urlParams.set('page', '1'); // Reset to first page
        window.location.search = urlParams.toString();
    }

    // Auto-submit form when certain filters change
    document.addEventListener('DOMContentLoaded', function () {
        const autoSubmitElements = ['repository', 'author', 'sort_by', 'sort_order', 'view_mode'];

        autoSubmitElements.forEach(function (elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.addEventListener('change', function () {
                    // Reset to page 1 when filters change
                    document.querySelector('input[name="page"]').value = '1';
                    // Save all filter settings before submitting
                    saveAllFilterSettings();
                    // Preserve current panel state before form submission
                    const filtersPanel = document.getElementById('filtersCollapse');
                    const isCurrentlyOpen = filtersPanel && filtersPanel.classList.contains('show');
                    savePanelState('documents_filters', isCurrentlyOpen);
                    document.getElementById('filtersForm').submit();
                });
            }
        });

        // Add search functionality with debounce
        const searchInput = document.getElementById('search');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', function () {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function () {
                    if (searchInput.value.length >= 3 || searchInput.value.length === 0) {
                        document.querySelector('input[name="page"]').value = '1';
                        // Save all filter settings before submitting
                        saveAllFilterSettings();
                        document.getElementById('filtersForm').submit();
                    }
                }, 500); // 500ms debounce
            });
        }

        // Add event listeners for quick date selection buttons
        // Commit date buttons
        document.getElementById('commit-today')?.addEventListener('click', () => setCommitDateRange(0, false, true));
        document.getElementById('commit-last-7-days')?.addEventListener('click', () => setCommitDateRange(7));
        document.getElementById('commit-last-30-days')?.addEventListener('click', () => setCommitDateRange(30));
        document.getElementById('commit-last-week')?.addEventListener('click', () => setCommitDateRange(0, true));

        // Processing date buttons
        document.getElementById('processed-today')?.addEventListener('click', () => setProcessingDateRange(0, false, true));
        document.getElementById('processed-last-7-days')?.addEventListener('click', () => setProcessingDateRange(7));
        document.getElementById('processed-last-30-days')?.addEventListener('click', () => setProcessingDateRange(30));
        document.getElementById('processed-last-week')?.addEventListener('click', () => setProcessingDateRange(0, true));

        // Clear date filters buttons
        document.getElementById('clear-commit-dates')?.addEventListener('click', clearCommitDateFilters);
        document.getElementById('clear-processing-dates')?.addEventListener('click', clearProcessingDateFilters);

        // Clear button highlights when manual date input is used
        const dateInputs = ['date_from', 'date_to', 'processed_date_from', 'processed_date_to'];
        dateInputs.forEach(inputId => {
            const input = document.getElementById(inputId);
            if (input) {
                input.addEventListener('input', clearDateButtonHighlights);
            }
        });

        // Initialize panel collapse states
        initializePanelStates();

        // Add keyboard shortcuts
        document.addEventListener('keydown', function (e) {
            // Ctrl+F to focus search
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.getElementById('search').focus();
            }

            // Escape to clear search
            if (e.key === 'Escape' && document.activeElement === document.getElementById('search')) {
                document.getElementById('search').value = '';
                // Preserve current panel state before form submission
                const filtersPanel = document.getElementById('filtersCollapse');
                const isCurrentlyOpen = filtersPanel && filtersPanel.classList.contains('show');
                savePanelState('documents_filters', isCurrentlyOpen);
                document.getElementById('filtersForm').submit();
            }
        });
    });

    function cleanupOrphanedDocuments() {
        if (confirm('This will remove all documents that belong to repositories that no longer exist. Continue?')) {
            fetch('/api/documents/cleanup-orphaned', {
                method: 'POST'
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        alert(`Success: ${data.message}`);
                        location.reload();
                    } else {
                        alert('Error: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('An error occurred while cleaning up orphaned documents.');
                });
        }
    }



    function deleteAllDocuments() {
        // Collect current filter parameters
        const filters = getCurrentFilters();
        const hasFilters = Object.values(filters).some(value => value && value !== '');

        // Create context-aware warning message
        let warningMessage, confirmMessage;
        if (hasFilters) {
            warningMessage = '⚠️ WARNING: This will permanently delete ALL FILTERED documents (database records AND physical files) that match your current search/filter criteria. This action cannot be undone. Are you sure?';
            confirmMessage = 'This is your final confirmation. Delete all filtered documents?';
        } else {
            warningMessage = '⚠️ WARNING: This will permanently delete ALL documents in the entire system (database records AND physical files). This action cannot be undone. Are you sure?';
            confirmMessage = 'This is your final confirmation. Delete ALL documents in the entire system?';
        }

        if (confirm(warningMessage)) {
            if (confirm(confirmMessage)) {
                // Send filters to backend
                fetch('/api/documents/delete-filtered', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(filters)
                })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert(`Success: ${data.message}`);
                            // Use force hard refresh to ensure all caches are cleared
                            forceHardRefresh();
                        } else {
                            alert('Error: ' + data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred while deleting documents.');
                    });
            }
        }
    }

    function getCurrentFilters() {
        // Extract current filter values from the page
        return {
            repository_id: document.getElementById('repository')?.value || '',
            search_query: document.getElementById('search')?.value || '',
            author_filter: document.getElementById('author')?.value || '',
            date_from: document.getElementById('date_from')?.value || '',
            date_to: document.getElementById('date_to')?.value || '',
            processed_date_from: document.getElementById('processed_date_from')?.value || '',
            processed_date_to: document.getElementById('processed_date_to')?.value || '',
            code_review_filter: document.getElementById('code_review')?.value || '',
            doc_impact_filter: document.getElementById('doc_impact')?.value || '',
            risk_level_filter: document.getElementById('risk_level')?.value || ''
        };
    }

    // Quick Date Selection Functions
    function setCommitDateRange(days, isCurrentWeek = false, isToday = false) {
        const today = new Date();
        let fromDate, toDate;

        if (isToday) {
            // Set both from and to dates to today
            fromDate = today;
            toDate = today;
        } else if (isCurrentWeek) {
            // Calculate current working week (Monday to Friday)
            const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
            const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Handle Sunday as 0

            const monday = new Date(today);
            monday.setDate(today.getDate() + mondayOffset);

            const friday = new Date(monday);
            friday.setDate(monday.getDate() + 4);

            fromDate = monday;
            toDate = friday;
        } else {
            // Calculate last N days
            fromDate = new Date(today);
            fromDate.setDate(today.getDate() - days);
            toDate = today;
        }

        // Format dates as YYYY-MM-DD
        const formatDate = (date) => date.toISOString().split('T')[0];

        // Set the date inputs
        document.getElementById('date_from').value = formatDate(fromDate);
        document.getElementById('date_to').value = formatDate(toDate);

        // Clear processing date filters
        document.getElementById('processed_date_from').value = '';
        document.getElementById('processed_date_to').value = '';

        // Update button highlights
        clearDateButtonHighlights();
        const buttonId = isToday ? 'commit-today' :
            isCurrentWeek ? 'commit-last-week' :
                days === 7 ? 'commit-last-7-days' :
                    days === 30 ? 'commit-last-30-days' : null;

        if (buttonId) {
            const button = document.getElementById(buttonId);
            button.classList.remove('btn-outline-primary');
            button.classList.add('btn-primary');
        }

        // Preserve current panel state before form submission
        const filtersPanel = document.getElementById('filtersCollapse');
        const isCurrentlyOpen = filtersPanel && filtersPanel.classList.contains('show');
        savePanelState('documents_filters', isCurrentlyOpen);

        // Auto-submit the form
        document.getElementById('filtersForm').submit();
    }

    function setProcessingDateRange(days, isCurrentWeek = false, isToday = false) {
        const today = new Date();
        let fromDate, toDate;

        if (isToday) {
            // Set both from and to dates to today
            fromDate = today;
            toDate = today;
        } else if (isCurrentWeek) {
            // Calculate current working week (Monday to Friday)
            const currentDay = today.getDay(); // 0 = Sunday, 1 = Monday, etc.
            const mondayOffset = currentDay === 0 ? -6 : 1 - currentDay; // Handle Sunday as 0

            const monday = new Date(today);
            monday.setDate(today.getDate() + mondayOffset);

            const friday = new Date(monday);
            friday.setDate(monday.getDate() + 4);

            fromDate = monday;
            toDate = friday;
        } else {
            // Calculate last N days
            fromDate = new Date(today);
            fromDate.setDate(today.getDate() - days);
            toDate = today;
        }

        // Format dates as YYYY-MM-DD
        const formatDate = (date) => date.toISOString().split('T')[0];

        // Set the processing date inputs
        document.getElementById('processed_date_from').value = formatDate(fromDate);
        document.getElementById('processed_date_to').value = formatDate(toDate);

        // Clear commit date filters
        document.getElementById('date_from').value = '';
        document.getElementById('date_to').value = '';

        // Update button highlights
        clearDateButtonHighlights();
        const buttonId = isToday ? 'processed-today' :
            isCurrentWeek ? 'processed-last-week' :
                days === 7 ? 'processed-last-7-days' :
                    days === 30 ? 'processed-last-30-days' : null;

        if (buttonId) {
            const button = document.getElementById(buttonId);
            button.classList.remove('btn-outline-success');
            button.classList.add('btn-success');
        }

        // Preserve current panel state before form submission
        const filtersPanel = document.getElementById('filtersCollapse');
        const isCurrentlyOpen = filtersPanel && filtersPanel.classList.contains('show');
        savePanelState('documents_filters', isCurrentlyOpen);

        // Auto-submit the form
        document.getElementById('filtersForm').submit();
    }

    function clearDateButtonHighlights() {
        const commitButtons = [
            'commit-today',
            'commit-last-7-days',
            'commit-last-30-days',
            'commit-last-week'
        ];

        const processedButtons = [
            'processed-today',
            'processed-last-7-days',
            'processed-last-30-days',
            'processed-last-week'
        ];

        commitButtons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.classList.remove('btn-primary');
                button.classList.add('btn-outline-primary');
            }
        });

        processedButtons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-success');
            }
        });
    }

    function clearCommitDateFilters() {
        // Clear only commit date input fields
        document.getElementById('date_from').value = '';
        document.getElementById('date_to').value = '';

        // Clear only commit button highlights
        const commitButtons = [
            'commit-today',
            'commit-last-7-days',
            'commit-last-30-days',
            'commit-last-week'
        ];

        commitButtons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.classList.remove('btn-primary');
                button.classList.add('btn-outline-primary');
            }
        });

        // Preserve current panel state before form submission
        const filtersPanel = document.getElementById('filtersCollapse');
        const isCurrentlyOpen = filtersPanel && filtersPanel.classList.contains('show');
        savePanelState('documents_filters', isCurrentlyOpen);

        // Auto-submit the form
        document.getElementById('filtersForm').submit();
    }

    function clearProcessingDateFilters() {
        // Clear only processing date input fields
        document.getElementById('processed_date_from').value = '';
        document.getElementById('processed_date_to').value = '';

        // Clear only processing button highlights
        const processedButtons = [
            'processed-today',
            'processed-last-7-days',
            'processed-last-30-days',
            'processed-last-week'
        ];

        processedButtons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.classList.remove('btn-success');
                button.classList.add('btn-outline-success');
            }
        });

        // Preserve current panel state before form submission
        const filtersPanel = document.getElementById('filtersCollapse');
        const isCurrentlyOpen = filtersPanel && filtersPanel.classList.contains('show');
        savePanelState('documents_filters', isCurrentlyOpen);

        // Auto-submit the form
        document.getElementById('filtersForm').submit();
    }

    // Panel Collapse State Management
    function savePanelState(panelId, isExpanded) {
        try {
            const panelStates = JSON.parse(localStorage.getItem('reposense_panel_states') || '{}');
            panelStates[panelId] = isExpanded;
            localStorage.setItem('reposense_panel_states', JSON.stringify(panelStates));
            console.log(`Panel state saved: ${panelId} = ${isExpanded}`);
        } catch (error) {
            console.warn('Failed to save panel state:', error);
        }
    }

    function loadPanelState(panelId, defaultExpanded = true) {
        try {
            const panelStates = JSON.parse(localStorage.getItem('reposense_panel_states') || '{}');
            return panelStates.hasOwnProperty(panelId) ? panelStates[panelId] : defaultExpanded;
        } catch (error) {
            console.warn('Failed to load panel state:', error);
            return defaultExpanded;
        }
    }

    function initializePanelStates() {
        // Initialize Documents page filters panel
        const filtersPanel = document.getElementById('filtersCollapse');
        if (filtersPanel) {
            const shouldExpand = loadPanelState('documents_filters', true);

            // Set the initial state properly
            if (shouldExpand) {
                filtersPanel.classList.add('show');
            } else {
                filtersPanel.classList.remove('show');
            }

            // Initialize Bootstrap collapse without forcing show/hide
            const bsCollapse = new bootstrap.Collapse(filtersPanel, { toggle: false });

            // Save state when panel is toggled
            filtersPanel.addEventListener('shown.bs.collapse', () => savePanelState('documents_filters', true));
            filtersPanel.addEventListener('hidden.bs.collapse', () => savePanelState('documents_filters', false));

            // Update button icon based on state
            const toggleButton = document.querySelector('[data-bs-target="#filtersCollapse"]');
            if (toggleButton) {
                const updateIcon = () => {
                    const icon = toggleButton.querySelector('i');
                    if (filtersPanel.classList.contains('show')) {
                        icon.className = 'fas fa-chevron-up';
                    } else {
                        icon.className = 'fas fa-chevron-down';
                    }
                };

                filtersPanel.addEventListener('shown.bs.collapse', updateIcon);
                filtersPanel.addEventListener('hidden.bs.collapse', updateIcon);
                updateIcon(); // Set initial state
            }
        }
    }

    // Local Storage Functions for Filter Settings
    function saveFilterSettings() {
        // Save display and organization settings that should persist across sessions
        const persistentSettings = {
            sort_by: document.getElementById('sort_by')?.value || 'date',
            sort_order: document.getElementById('sort_order')?.value || 'desc',
            per_page: document.getElementById('per_page')?.value || '25',
            view_mode: document.getElementById('view_mode')?.value || 'table'
        };

        try {
            localStorage.setItem('reposense_filter_settings', JSON.stringify(persistentSettings));
            console.log('Filter settings saved to local storage:', persistentSettings);
        } catch (error) {
            console.warn('Failed to save filter settings to local storage:', error);
        }
    }

    function saveAllFilterSettings() {
        // Save all current filter settings including date ranges (for session persistence)
        const allSettings = {
            // Persistent settings
            sort_by: document.getElementById('sort_by')?.value || 'date',
            sort_order: document.getElementById('sort_order')?.value || 'desc',
            per_page: document.getElementById('per_page')?.value || '25',
            view_mode: document.getElementById('view_mode')?.value || 'table',

            // Filter settings (session-based)
            repository: document.getElementById('repository')?.value || '',
            author: document.getElementById('author')?.value || '',
            search: document.getElementById('search')?.value || '',
            code_review: document.getElementById('code_review')?.value || '',
            doc_impact: document.getElementById('doc_impact')?.value || '',
            risk_level: document.getElementById('risk_level')?.value || '',

            // Date filters
            date_from: document.getElementById('date_from')?.value || '',
            date_to: document.getElementById('date_to')?.value || '',
            processed_date_from: document.getElementById('processed_date_from')?.value || '',
            processed_date_to: document.getElementById('processed_date_to')?.value || ''
        };

        try {
            // Save persistent settings
            const persistentSettings = {
                sort_by: allSettings.sort_by,
                sort_order: allSettings.sort_order,
                per_page: allSettings.per_page,
                view_mode: allSettings.view_mode
            };
            localStorage.setItem('reposense_filter_settings', JSON.stringify(persistentSettings));

            // Save session settings (including date filters)
            sessionStorage.setItem('reposense_session_filters', JSON.stringify(allSettings));
            console.log('All filter settings saved:', allSettings);
        } catch (error) {
            console.warn('Failed to save filter settings:', error);
        }
    }

    function loadFilterSettings() {
        // Load and apply persistent settings from local storage
        // If no URL parameters are present, redirect to apply saved settings to server-side sorting
        try {
            const savedSettings = localStorage.getItem('reposense_filter_settings');
            const sessionSettings = sessionStorage.getItem('reposense_session_filters');

            if (savedSettings) {
                const settings = JSON.parse(savedSettings);
                console.log('Loading filter settings from local storage:', settings);

                // Get current URL parameters to check if user has specific settings
                const urlParams = new URLSearchParams(window.location.search);

                // Check if we have any display/organization parameters in URL
                const hasDisplayParams = urlParams.has('sort_by') || urlParams.has('sort_order') ||
                    urlParams.has('per_page') || urlParams.has('view_mode');

                if (!hasDisplayParams) {
                    // No display parameters in URL - redirect with saved settings to apply server-side sorting
                    let needsRedirect = false;
                    const newParams = new URLSearchParams(window.location.search);

                    if (settings.sort_by && settings.sort_by !== 'date') {
                        newParams.set('sort_by', settings.sort_by);
                        needsRedirect = true;
                    }
                    if (settings.sort_order && settings.sort_order !== 'desc') {
                        newParams.set('sort_order', settings.sort_order);
                        needsRedirect = true;
                    }
                    if (settings.per_page && settings.per_page !== '25') {
                        newParams.set('per_page', settings.per_page);
                        needsRedirect = true;
                    }
                    if (settings.view_mode && settings.view_mode !== 'table') {
                        newParams.set('view_mode', settings.view_mode);
                        needsRedirect = true;
                    }

                    if (needsRedirect) {
                        console.log('Redirecting to apply saved settings:', Object.fromEntries(newParams));
                        window.location.search = newParams.toString();
                        return; // Exit early since we're redirecting
                    }
                }

                // Apply settings to form elements only if not specified in URL (for display consistency)
                if (settings.sort_by && document.getElementById('sort_by') && !urlParams.has('sort_by')) {
                    document.getElementById('sort_by').value = settings.sort_by;
                }
                if (settings.sort_order && document.getElementById('sort_order') && !urlParams.has('sort_order')) {
                    document.getElementById('sort_order').value = settings.sort_order;
                }
                if (settings.per_page && document.getElementById('per_page') && !urlParams.has('per_page')) {
                    document.getElementById('per_page').value = settings.per_page;
                }
                if (settings.view_mode && document.getElementById('view_mode') && !urlParams.has('view_mode')) {
                    document.getElementById('view_mode').value = settings.view_mode;
                }
            }
        } catch (error) {
            console.warn('Failed to load filter settings from local storage:', error);
        }
    }

    function clearFilterSettings() {
        // Clear saved settings from local storage
        try {
            localStorage.removeItem('reposense_filter_settings');
            console.log('Filter settings cleared from local storage');
        } catch (error) {
            console.warn('Failed to clear filter settings from local storage:', error);
        }
    }



    function deleteDocument(docId, docName) {
        document.getElementById('deleteDocumentName').textContent = docName;
        document.getElementById('deleteDocumentForm').action = '/api/documents/' + docId + '/delete';

        new bootstrap.Modal(document.getElementById('deleteDocumentModal')).show();
    }

    // Handle delete form submission
    document.getElementById('deleteDocumentForm').addEventListener('submit', function (e) {
        e.preventDefault();

        const form = e.target;
        const formData = new FormData(form);

        fetch(form.action, {
            method: 'POST',
            body: formData
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    // Close modal and refresh page
                    bootstrap.Modal.getInstance(document.getElementById('deleteDocumentModal')).hide();
                    location.reload();
                } else {
                    alert('Error: ' + data.message);
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('An error occurred while deleting the document.');
            });
    });

    // Auto-refresh removed - users can manually refresh when needed

    // Load saved filter settings when page loads
    document.addEventListener('DOMContentLoaded', function () {
        loadFilterSettings();

        // Fix dropdown z-index issues with JavaScript
        fixDropdownZIndex();
    });

    // JavaScript fix for dropdown z-index issues
    function fixDropdownZIndex() {
        const dropdowns = document.querySelectorAll('.btn-group .dropdown');

        dropdowns.forEach(dropdown => {
            dropdown.addEventListener('shown.bs.dropdown', function () {
                const menu = this.querySelector('.dropdown-menu');
                if (menu) {
                    // Force the menu to the highest z-index
                    menu.style.zIndex = '99999';
                    menu.style.position = 'fixed';

                    // Move the menu to body to break out of stacking context
                    if (menu.parentElement !== document.body) {
                        const rect = menu.getBoundingClientRect();
                        menu.style.top = rect.top + 'px';
                        menu.style.left = rect.left + 'px';
                        document.body.appendChild(menu);
                    }
                }
            });

            dropdown.addEventListener('hidden.bs.dropdown', function () {
                const menu = document.querySelector('.dropdown-menu[style*="position: fixed"]');
                if (menu && menu.parentElement === document.body) {
                    // Move menu back to its original parent
                    this.appendChild(menu);
                    menu.style.position = '';
                    menu.style.top = '';
                    menu.style.left = '';
                }
            });
        });
    }

    // Diff toggle functionality
    function toggleDiff(docId) {
        const diffRow = document.getElementById(`diff-row-${docId}`);
        const diffContent = document.getElementById(`diff-content-${docId}`);

        if (diffRow.style.display === 'none') {
            // Show diff row
            diffRow.style.display = '';

            // Load diff content if not already loaded
            if (diffContent.innerHTML.includes('Loading diff...')) {
                loadDiffContent(docId, 'unified');
            }
        } else {
            // Hide diff row
            diffRow.style.display = 'none';
        }
    }

    // Load diff content with specified format
    function loadDiffContent(docId, format) {
        const diffContent = document.getElementById(`diff-content-${docId}`);

        // Show loading indicator
        diffContent.innerHTML = `
        <div class="text-center text-muted py-3">
            <i class="fas fa-spinner fa-spin"></i> Loading ${format} diff...
        </div>
    `;

        fetch(`/api/documents/${docId}/diff?format=${format}`)
            .then(response => response.json())
            .then(data => {
                if (data.error) {
                    diffContent.innerHTML = `
                    <div class="alert alert-warning mb-0">
                        <i class="fas fa-exclamation-triangle"></i> ${data.error}
                    </div>
                `;
                } else {
                    if (data.format === 'side-by-side') {
                        // Side-by-side format is already HTML
                        diffContent.innerHTML = `
                        <div style="max-height: 500px; overflow-y: auto;">
                            ${data.diff}
                        </div>
                    `;
                    } else {
                        // Unified format needs to be wrapped in pre/code
                        diffContent.innerHTML = `
                        <pre class="bg-dark text-light p-3 rounded mb-0" style="max-height: 400px; overflow-y: auto;"><code>${escapeHtml(data.diff)}</code></pre>
                    `;
                    }
                }
            })
            .catch(error => {
                console.error('Error loading diff:', error);
                diffContent.innerHTML = `
                <div class="alert alert-danger mb-0">
                    <i class="fas fa-exclamation-circle"></i> Error loading diff content
                </div>
            `;
            });
    }

    // Change diff format
    function changeDiffFormat(docId, format) {
        loadDiffContent(docId, format);
    }

    // Helper function to escape HTML
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // Processing Status Management for Documents Page
    let documentsProcessingInterval = null;

    function updateDocumentsProcessingStatus() {
        fetch('/api/processing-status')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const processing = data.processing;
                    const statusDiv = document.getElementById('documents-processing-status');
                    const messageSpan = document.getElementById('processing-message');

                    if (processing.queue_size > 0 || processing.current_tasks.length > 0) {
                        // Show processing status
                        statusDiv.style.display = 'flex';
                        statusDiv.classList.remove('d-none');

                        // Update message based on current state
                        let message = '';
                        if (processing.queue_size > 0) {
                            message = `Processing ${processing.queue_size} documents...`;
                        } else if (processing.current_tasks.length > 0) {
                            const activeTask = processing.current_tasks.find(t => t.status === 'active');
                            if (activeTask) {
                                message = activeTask.description;
                            } else {
                                message = 'Processing documents...';
                            }
                        }
                        messageSpan.textContent = message;

                        // Auto-refresh document list when processing is active
                        if (!window.processingRefreshActive) {
                            window.processingRefreshActive = true;
                            setTimeout(() => {
                                // Check if processing is still active
                                if (processing.queue_size === 0 && processing.current_tasks.length === 0) {
                                    // Processing finished, clear cache and refresh
                                    console.log('Processing completed, clearing cache and refreshing document list...');
                                    clearCacheAndRefresh();
                                    window.processingRefreshActive = false;
                                } else {
                                    // Still processing, continue monitoring
                                    window.processingRefreshActive = false;
                                }
                            }, 3000); // Check every 3 seconds for faster response
                        }
                    } else {
                        // Hide processing status
                        statusDiv.style.display = 'none';
                        statusDiv.classList.add('d-none');

                        // If we were processing before, clear cache and refresh the document list
                        if (window.wasProcessing) {
                            console.log('Processing stopped, clearing cache and refreshing document list...');
                            clearCacheAndRefresh();
                            window.wasProcessing = false;
                        }
                    }

                    // Track processing state
                    window.wasProcessing = (processing.queue_size > 0 || processing.current_tasks.length > 0);
                }
            })
            .catch(error => {
                console.error('Error fetching processing status:', error);
            });
    }

    // Start processing status updates when page loads
    document.addEventListener('DOMContentLoaded', function () {
        // Initial update
        updateDocumentsProcessingStatus();

        // Poll every 3 seconds for faster refresh response
        documentsProcessingInterval = setInterval(updateDocumentsProcessingStatus, 3000);
    });

    // Clean up interval when page unloads
    window.addEventListener('beforeunload', function () {
        if (documentsProcessingInterval) {
            clearInterval(documentsProcessingInterval);
        }
    });

    // Rescan Document Functionality
    let currentRescanDocId = null;

    function showRescanModal(docId, docName) {
        currentRescanDocId = docId;

        // Update document info in modal
        document.getElementById('rescanDocumentInfo').innerHTML = `
        <div class="d-flex align-items-center">
            <i class="fas fa-file-alt text-primary me-2"></i>
            <div>
                <strong>${docName}</strong><br>
                <small class="text-muted">Document ID: ${docId}</small>
            </div>
        </div>
    `;

        // Load available models
        loadModelsForRescan();

        // Load current repository aggressiveness (if available)
        loadRepositoryAggressiveness(docId);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('rescanModal'));
        modal.show();
    }

    function loadModelsForRescan() {
        const modelSelect = document.getElementById('rescanModelSelect');
        const statusDiv = document.getElementById('rescanStatus');

        // Show loading state
        modelSelect.innerHTML = '<option value="">Loading models...</option>';
        modelSelect.disabled = true;

        fetch('/api/ollama/models')
            .then(response => response.json())
            .then(data => {
                modelSelect.innerHTML = '';

                if (data.connected && data.models.length > 0) {
                    // Add default option
                    const defaultOption = document.createElement('option');
                    defaultOption.value = '';
                    defaultOption.textContent = 'Select a model...';
                    modelSelect.appendChild(defaultOption);

                    // Add available models
                    data.models.forEach(model => {
                        const option = document.createElement('option');
                        option.value = model;
                        option.textContent = model;
                        modelSelect.appendChild(option);
                    });

                    modelSelect.disabled = false;

                    // Show success message
                    statusDiv.innerHTML = `
                    <div class="alert alert-success">
                        <i class="fas fa-check me-2"></i>Found ${data.models.length} available models
                    </div>
                `;
                    statusDiv.style.display = 'block';

                } else if (!data.connected) {
                    modelSelect.innerHTML = '<option value="">Ollama server not connected</option>';
                    statusDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>Ollama server not connected
                    </div>
                `;
                    statusDiv.style.display = 'block';

                } else {
                    modelSelect.innerHTML = '<option value="">No models available</option>';
                    statusDiv.innerHTML = `
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>No models found on Ollama server
                    </div>
                `;
                    statusDiv.style.display = 'block';
                }
            })
            .catch(error => {
                console.error('Error loading models:', error);
                modelSelect.innerHTML = '<option value="">Error loading models</option>';
                statusDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>Error loading models from server
                </div>
            `;
                statusDiv.style.display = 'block';
            });
    }

    function loadRepositoryAggressiveness(docId) {
        // Try to get the repository aggressiveness for this document
        fetch(`/api/documents/${docId}`)
            .then(response => response.json())
            .then(data => {
                if (data.success && data.document && data.document.repository_id) {
                    // Get repository configuration
                    fetch(`/api/repositories/${data.document.repository_id}`)
                        .then(response => response.json())
                        .then(repoData => {
                            if (repoData.success && repoData.repository && repoData.repository.risk_aggressiveness) {
                                const aggressivenessSelect = document.getElementById('rescanAggressivenessSelect');
                                aggressivenessSelect.value = repoData.repository.risk_aggressiveness;
                            }
                        })
                        .catch(error => {
                            console.debug('Could not load repository aggressiveness:', error);
                            // Keep default BALANCED selection
                        });
                }
            })
            .catch(error => {
                console.debug('Could not load document info for aggressiveness:', error);
                // Keep default BALANCED selection
            });
    }

    // Handle rescan confirmation
    document.getElementById('confirmRescanBtn').addEventListener('click', function () {
        const modelSelect = document.getElementById('rescanModelSelect');
        const aggressivenessSelect = document.getElementById('rescanAggressivenessSelect');
        const preserveFeedback = document.getElementById('rescanPreserveUserFeedback').checked;
        const statusDiv = document.getElementById('rescanStatus');
        const confirmBtn = this;

        const selectedModel = modelSelect.value;
        const selectedAggressiveness = aggressivenessSelect.value;

        if (!selectedModel) {
            statusDiv.innerHTML = `
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle me-2"></i>Please select an AI model
            </div>
        `;
            statusDiv.style.display = 'block';
            return;
        }

        // Show loading state
        confirmBtn.disabled = true;
        confirmBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Rescanning...';

        statusDiv.innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-spinner fa-spin me-2"></i>Rescanning document with ${selectedModel} (${selectedAggressiveness.toLowerCase()} risk assessment)...
        </div>
    `;
        statusDiv.style.display = 'block';

        // Send rescan request (URL encode the document ID to handle slashes)
        const rescanUrl = `/api/documents/${encodeURIComponent(currentRescanDocId)}/rescan`;
        console.log('Rescan URL:', rescanUrl);
        console.log('Document ID:', currentRescanDocId);
        fetch(rescanUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                model: selectedModel,
                aggressiveness: selectedAggressiveness,
                preserve_user_feedback: preserveFeedback
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statusDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check me-2"></i>${data.message}
                </div>
            `;

                    // Close modal and refresh immediately - no delay needed
                    setTimeout(() => {
                        const modal = bootstrap.Modal.getInstance(document.getElementById('rescanModal'));
                        modal.hide();
                    }, 1000);

                    // Start immediate refresh cycle to catch the updated document
                    setTimeout(() => {
                        console.log('Rescan completed, starting immediate refresh cycle...');
                        clearCacheAndRefresh();
                    }, 2000);

                    // Also set up a backup refresh in case the first one doesn't catch it
                    setTimeout(() => {
                        console.log('Backup refresh after rescan...');
                        clearCacheAndRefresh();
                    }, 5000);

                } else {
                    // Check if recovery information is available
                    if (data.recovery_available) {
                        statusDiv.innerHTML = `
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Document Missing from Database</strong><br>
                        <small class="text-muted">Repository: ${data.repository_name} | Revision: ${data.revision} | Author: ${data.author}</small><br>
                        <small class="text-muted">Message: ${data.commit_message}</small><br><br>
                        <strong>Recovery Options:</strong><br>
                        <small>${data.suggestion}</small><br><br>
                        <button type="button" class="btn btn-sm btn-success me-2" onclick="recoverDocument('${currentRescanDocId}', '${data.revision}')">
                            <i class="fas fa-magic me-1"></i>Auto-Recover Document
                        </button>
                        <a href="/historical-scan" class="btn btn-sm btn-primary me-2">
                            <i class="fas fa-history me-1"></i>Go to Historical Scan
                        </a>
                        <button type="button" class="btn btn-sm btn-secondary" onclick="location.reload()">
                            <i class="fas fa-refresh me-1"></i>Refresh Page
                        </button>
                    </div>
                `;
                    } else {
                        statusDiv.innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-circle me-2"></i>${data.message}
                    </div>
                `;
                    }

                    // Re-enable button
                    confirmBtn.disabled = false;
                    confirmBtn.innerHTML = '<i class="fas fa-redo me-2"></i>Rescan Document';
                }
            })
            .catch(error => {
                console.error('Error rescanning document:', error);
                statusDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>Error rescanning document: ${error.message}
            </div>
        `;

                // Re-enable button
                confirmBtn.disabled = false;
                confirmBtn.innerHTML = '<i class="fas fa-redo me-2"></i>Rescan Document';
            });
    });

    // Check for recovery parameters on page load
    document.addEventListener('DOMContentLoaded', function () {
        const urlParams = new URLSearchParams(window.location.search);
        const recoveryDocId = urlParams.get('recovery_doc_id');
        const recoveryRepo = urlParams.get('recovery_repo');
        const recoveryRevision = urlParams.get('recovery_revision');

        if (recoveryDocId && recoveryRepo && recoveryRevision) {
            // Show recovery notification
            const alertHtml = `
            <div class="alert alert-warning alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <strong>Document Recovery Available</strong><br>
                Document <code>${recoveryDocId}</code> is missing from the database but exists in repository <strong>${recoveryRepo}</strong> (revision ${recoveryRevision}).
                <br><br>
                <button type="button" class="btn btn-sm btn-success me-2" onclick="quickRecoverDocument('${recoveryDocId}', '${recoveryRevision}')">
                    <i class="fas fa-magic me-1"></i>Quick Recover
                </button>
                <a href="/historical-scan" class="btn btn-sm btn-primary me-2">
                    <i class="fas fa-history me-1"></i>Historical Scan
                </a>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

            // Insert the alert at the top of the content
            const contentDiv = document.querySelector('.container-fluid');
            if (contentDiv) {
                contentDiv.insertAdjacentHTML('afterbegin', alertHtml);
            }

            // Clean up URL parameters
            const cleanUrl = window.location.pathname;
            window.history.replaceState({}, document.title, cleanUrl);
        }
    });

    // Quick recovery function for the notification
    function quickRecoverDocument(docId, revision) {
        const alertDiv = document.querySelector('.alert-warning');

        // Show loading state
        if (alertDiv) {
            alertDiv.innerHTML = `
            <i class="fas fa-spinner fa-spin me-2"></i>
            <strong>Recovering Document...</strong><br>
            Please wait while we recover document <code>${docId}</code> from the repository.
        `;
        }

        // Send recovery request
        fetch(`/api/documents/${docId}/recover`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                aggressiveness: 'BALANCED'
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    if (alertDiv) {
                        alertDiv.className = 'alert alert-success alert-dismissible fade show';
                        alertDiv.innerHTML = `
                    <i class="fas fa-check me-2"></i>
                    <strong>Document Recovered Successfully!</strong><br>
                    ${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                    }

                    // Refresh the page after a short delay
                    setTimeout(() => {
                        location.reload();
                    }, 2000);

                } else {
                    if (alertDiv) {
                        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                        alertDiv.innerHTML = `
                    <i class="fas fa-exclamation-circle me-2"></i>
                    <strong>Recovery Failed</strong><br>
                    ${data.message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                `;
                    }
                }
            })
            .catch(error => {
                console.error('Error recovering document:', error);
                if (alertDiv) {
                    alertDiv.className = 'alert alert-danger alert-dismissible fade show';
                    alertDiv.innerHTML = `
                <i class="fas fa-exclamation-circle me-2"></i>
                <strong>Recovery Error</strong><br>
                An error occurred while recovering the document: ${error.message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
                }
            });
    }

    // Document Recovery Functionality
    function recoverDocument(docId, revision) {
        const statusDiv = document.getElementById('rescanStatus');
        const aggressivenessSelect = document.getElementById('rescanAggressivenessSelect');
        const selectedAggressiveness = aggressivenessSelect.value;

        // Show loading state
        statusDiv.innerHTML = `
        <div class="alert alert-info">
            <i class="fas fa-spinner fa-spin me-2"></i>Recovering document from repository (revision ${revision})...
        </div>
    `;
        statusDiv.style.display = 'block';

        // Send recovery request (URL encode the document ID to handle slashes)
        fetch(`/api/documents/${encodeURIComponent(docId)}/recover`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                aggressiveness: selectedAggressiveness
            })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    statusDiv.innerHTML = `
                <div class="alert alert-success">
                    <i class="fas fa-check me-2"></i>${data.message}
                </div>
            `;

                    // Close modal and refresh immediately
                    setTimeout(() => {
                        const modal = bootstrap.Modal.getInstance(document.getElementById('rescanModal'));
                        modal.hide();
                    }, 1000);

                    // Immediate refresh for document recovery
                    setTimeout(() => {
                        console.log('Document recovery completed, refreshing...');
                        clearCacheAndRefresh();
                    }, 2000);

                } else {
                    statusDiv.innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-circle me-2"></i>Recovery failed: ${data.message}
                </div>
            `;
                }
            })
            .catch(error => {
                console.error('Error recovering document:', error);
                statusDiv.innerHTML = `
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle me-2"></i>Error recovering document: ${error.message}
            </div>
        `;
            });
    }


</script>
{% endblock %}