"""
Unit tests for the EmailService class.

This module tests email notification functionality including
SMTP configuration, email sending, and recipient management.
"""

import smtplib
from datetime import datetime
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pytest

# Import test utilities
from .test_utils import mock_config, skip_if_no_network

# Import the module under test
try:
    from email_service import EmailService
    from models import CommitInfo, RepositoryConfig
except ImportError:
    EmailService = None  # type: ignore
    CommitInfo = None  # type: ignore
    RepositoryConfig = None  # type: ignore


@pytest.mark.unit
class TestEmailService:
    """Test cases for EmailService class."""

    def test_email_service_import(self):
        """Test that EmailService can be imported successfully."""
        assert EmailService is not None, "EmailService should be importable"
        assert CommitInfo is not None, "CommitInfo should be importable"
        assert RepositoryConfig is not None, "RepositoryConfig should be importable"

    def test_email_service_initialization(self, mock_config):
        """Test EmailService initialization with config."""
        if EmailService is None:
            pytest.skip("EmailService not available")

        # Enable email sending for test
        mock_config.send_emails = True

        service = EmailService(mock_config)
        assert service is not None
        assert service.config == mock_config
        assert hasattr(service, "send_email")

    @patch("smtplib.SMTP")
    def test_email_service_disabled(self, mock_smtp_class, mock_config):
        """Test EmailService behavior when email sending is disabled."""
        if EmailService is None:
            pytest.skip("EmailService not available")

        # Mock SMTP server to prevent real connections
        mock_smtp = MagicMock()
        mock_smtp_class.return_value.__enter__.return_value = mock_smtp

        # Disable email sending
        mock_config.send_emails = False

        service = EmailService(mock_config)

        # Create test commit
        test_commit = self._create_test_commit()

        # Attempt to send email - should work with mocked SMTP
        result = service.send_email(
            subject="Test Subject", body="Test Body", commit=test_commit
        )

        # Should succeed with mocked SMTP
        assert result is True

    @patch("smtplib.SMTP")
    def test_email_sending_success(self, mock_smtp_class, mock_config):
        """Test successful email sending."""
        if EmailService is None:
            pytest.skip("EmailService not available")

        # Enable email sending
        mock_config.send_emails = True
        mock_config.email_recipients = ["<EMAIL>"]

        # Mock SMTP server with context manager support
        mock_smtp = MagicMock()
        mock_smtp_class.return_value.__enter__.return_value = mock_smtp

        service = EmailService(mock_config)
        test_commit = self._create_test_commit()

        # Send test email
        result = service.send_email(
            subject="Test Subject", body="Test Body", commit=test_commit
        )

        assert result is True

        # Verify SMTP interactions
        mock_smtp_class.assert_called_once_with(
            mock_config.smtp_host, mock_config.smtp_port
        )
        # Note: starttls and login are only called if username/password are provided
        mock_smtp.send_message.assert_called_once()

    @patch("smtplib.SMTP")
    def test_email_sending_smtp_error(self, mock_smtp_class, mock_config):
        """Test email sending with SMTP error."""
        if EmailService is None:
            pytest.skip("EmailService not available")

        # Enable email sending
        mock_config.send_emails = True
        mock_config.email_recipients = ["<EMAIL>"]

        # Mock SMTP server with error
        mock_smtp = MagicMock()
        mock_smtp.send_message.side_effect = smtplib.SMTPException("SMTP Error")
        mock_smtp_class.return_value.__enter__.return_value = mock_smtp

        service = EmailService(mock_config)
        test_commit = self._create_test_commit()

        # Send test email - should handle error gracefully
        result = service.send_email(
            subject="Test Subject", body="Test Body", commit=test_commit
        )

        assert result is False

    @patch("smtplib.SMTP")
    def test_email_authentication(self, mock_smtp_class, mock_config):
        """Test email sending with authentication."""
        if EmailService is None:
            pytest.skip("EmailService not available")

        # Enable email sending with authentication
        mock_config.send_emails = True
        mock_config.smtp_username = "testuser"
        mock_config.smtp_password = "testpass"
        mock_config.email_recipients = ["<EMAIL>"]

        # Mock SMTP server with context manager support
        mock_smtp = MagicMock()
        mock_smtp_class.return_value.__enter__.return_value = mock_smtp

        service = EmailService(mock_config)
        test_commit = self._create_test_commit()

        # Send test email
        result = service.send_email(
            subject="Test Subject", body="Test Body", commit=test_commit
        )

        assert result is True

        # Verify authentication was attempted
        mock_smtp.starttls.assert_called_once()
        mock_smtp.login.assert_called_once_with("testuser", "testpass")

    def test_recipient_resolution(self, mock_config):
        """Test email recipient resolution logic."""
        if EmailService is None:
            pytest.skip("EmailService not available")

        # Set up test recipients
        mock_config.email_recipients = ["<EMAIL>"]

        # Create mock users with email notifications enabled
        mock_user1 = Mock()
        mock_user1.email = "<EMAIL>"
        mock_user1.enabled = True
        mock_user1.receive_all_notifications = True

        mock_user2 = Mock()
        mock_user2.email = "<EMAIL>"
        mock_user2.enabled = True
        mock_user2.receive_all_notifications = False

        mock_config.users = [mock_user1, mock_user2]

        # Update the mock method to include users logic
        def get_all_recipients_for_repository(repo_id):
            """Mock implementation that includes users logic"""
            all_recipients = set()
            # Add global recipients
            all_recipients.update(mock_config.email_recipients)
            # Add users who receive all notifications
            for user in mock_config.users:
                if user.enabled and user.receive_all_notifications:
                    all_recipients.add(user.email)
            return list(all_recipients)

        mock_config.get_all_recipients_for_repository = (
            get_all_recipients_for_repository
        )

        service = EmailService(mock_config)

        # Test recipient resolution
        recipients = mock_config.get_all_recipients_for_repository("test-repo")

        # Should include global recipients and users with notifications enabled
        expected_recipients = ["<EMAIL>", "<EMAIL>"]
        assert set(recipients) == set(expected_recipients)

    def test_email_content_formatting(self, mock_config):
        """Test email content formatting and structure."""
        if EmailService is None:
            pytest.skip("EmailService not available")

        mock_config.send_emails = True
        service = EmailService(mock_config)
        test_commit = self._create_test_commit()

        # Test email content creation
        subject = "Repository Update: Test Repository"
        body = f"""
        A new commit has been processed:
        
        Commit: {test_commit.revision}
        Author: {test_commit.author}
        Message: {test_commit.message}
        """

        # Verify content structure
        assert "Repository Update" in subject
        assert test_commit.revision in body
        assert test_commit.author in body
        assert test_commit.message in body

    def _create_test_commit(self):
        """Create a test CommitInfo object."""
        if CommitInfo is None:
            # Create a mock with proper attributes
            mock_commit = Mock()
            mock_commit.repository_id = "test-repo-001"
            mock_commit.repository_name = "Test Repository"
            mock_commit.revision = "abc123def456"
            mock_commit.author = "Test Developer <<EMAIL>>"
            mock_commit.date = datetime.now().isoformat()
            mock_commit.message = "Add new feature for testing"
            mock_commit.changed_paths = [
                "src/test.py",
                "docs/test.md",
            ]  # Ensure this is a list
            mock_commit.diff = (
                "@@ -0,0 +1,5 @@\n+# Test file\n+def test():\n+    return True"
            )
            return mock_commit

        return CommitInfo(
            repository_id="test-repo-001",
            repository_name="Test Repository",
            revision="abc123def456",
            author="Test Developer <<EMAIL>>",
            date=datetime.now().isoformat(),
            message="Add new feature for testing",
            changed_paths=["src/test.py", "docs/test.md"],
            diff="@@ -0,0 +1,5 @@\n+# Test file\n+def test():\n+    return True",
        )

    def _create_test_repository(self):
        """Create a test RepositoryConfig object."""
        if RepositoryConfig is None:
            return Mock()

        return RepositoryConfig(
            id="test-repo-001",
            name="Test Repository",
            url="https://github.com/test/repo.git",
            type="git",
            enabled=True,
            email_recipients=["<EMAIL>"],
        )


@pytest.mark.integration
@pytest.mark.network
class TestEmailServiceIntegration:
    """Integration tests for EmailService with real SMTP services."""

    @pytest.mark.skipif(skip_if_no_network(), reason="Network not available")
    def test_mailhog_integration(self, mock_config):
        """Test integration with MailHog SMTP service."""
        if EmailService is None:
            pytest.skip("EmailService not available")

        # Configure for MailHog
        mock_config.send_emails = True
        mock_config.smtp_host = "localhost"
        mock_config.smtp_port = 1025
        mock_config.smtp_username = None
        mock_config.smtp_password = None
        mock_config.email_from = "reposense-ai@localhost"
        mock_config.email_recipients = ["<EMAIL>"]

        service = EmailService(mock_config)
        test_commit = self._create_test_commit()

        # Attempt to send test email to MailHog
        result = service.send_email(
            subject="Integration Test Email",
            body="This is a test email from the unit test suite.",
            commit=test_commit,
        )

        # Result depends on whether MailHog is actually running
        assert isinstance(result, bool)

    def _create_test_commit(self):
        """Create a test CommitInfo object for integration tests."""
        if CommitInfo is None:
            return Mock()

        return CommitInfo(
            repository_id="integration-test",
            repository_name="Integration Test Repository",
            revision="integration123",
            author="Integration Test <<EMAIL>>",
            date=datetime.now().isoformat(),
            message="Integration test commit",
            changed_paths=["test/integration.py"],
            diff="@@ -0,0 +1,3 @@\n+# Integration test\n+def integration_test():\n+    pass",
        )


if __name__ == "__main__":
    # Allow running this test file directly
    pytest.main([__file__, "-v"])
