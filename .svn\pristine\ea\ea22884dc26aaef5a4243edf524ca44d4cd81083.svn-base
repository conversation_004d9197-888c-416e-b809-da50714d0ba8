#!/usr/bin/env python3
"""
LDAP Connection Troubleshooting Tool for RepoSense AI
Comprehensive testing of LDAP connectivity and configuration
"""

import argparse
import sys
import socket
import time
from ldap3 import Server, Connection, ALL, SUBTREE
from ldap3.core.exceptions import LDAPException

class LDAPTroubleshooter:
    def __init__(self):
        self.server_uri = "ldap://localhost:1389"
        self.internal_uri = "ldap://openldap:1389"
        self.base_dn = "dc=reposense,dc=local"
        self.admin_dn = "cn=admin,dc=reposense,dc=local"
        self.admin_password = "adminpassword"
        
    def test_network_connectivity(self):
        """Test basic network connectivity to LDAP server"""
        print("🌐 Testing Network Connectivity")
        print("=" * 50)
        
        # Test external port
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(5)
            result = sock.connect_ex(('localhost', 1389))
            sock.close()
            
            if result == 0:
                print("✅ External LDAP port (1389) is accessible")
            else:
                print("❌ External LDAP port (1389) is NOT accessible")
                return False
        except Exception as e:
            print(f"❌ Network connectivity test failed: {e}")
            return False
            
        return True
    
    def test_ldap_connection(self, server_uri=None):
        """Test LDAP connection and authentication"""
        if server_uri is None:
            server_uri = self.server_uri
            
        print(f"\n🔐 Testing LDAP Connection: {server_uri}")
        print("=" * 50)
        
        try:
            # Test server connection
            server = Server(server_uri, get_info=ALL)
            print(f"✅ Server object created successfully")
            
            # Test anonymous bind
            conn = Connection(server)
            if conn.bind():
                print("✅ Anonymous bind successful")
                conn.unbind()
            else:
                print("❌ Anonymous bind failed")
            
            # Test admin authentication
            conn = Connection(server, self.admin_dn, self.admin_password, auto_bind=True)
            print("✅ Admin authentication successful")
            
            # Test base DN search
            conn.search(self.base_dn, '(objectClass=*)', SUBTREE, attributes=['*'])
            print(f"✅ Base DN search successful - Found {len(conn.entries)} entries")
            
            # Show server info
            if server.info:
                print(f"📋 Server Info:")
                print(f"   Vendor: {server.info.vendor_name}")
                print(f"   Version: {server.info.vendor_version}")
                print(f"   Naming Contexts: {server.info.naming_contexts}")
            
            conn.unbind()
            return True
            
        except LDAPException as e:
            print(f"❌ LDAP connection failed: {e}")
            return False
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            return False
    
    def test_user_operations(self):
        """Test user creation and authentication"""
        print("\n👤 Testing User Operations")
        print("=" * 50)
        
        try:
            server = Server(self.server_uri, get_info=ALL)
            conn = Connection(server, self.admin_dn, self.admin_password, auto_bind=True)
            
            # Test user search
            conn.search(f"ou=users,{self.base_dn}", '(objectClass=inetOrgPerson)', SUBTREE, 
                       attributes=['cn', 'uid', 'mail'])
            
            print(f"✅ Found {len(conn.entries)} users in directory")
            
            for entry in conn.entries:
                print(f"   👤 {entry.cn.value} ({entry.uid.value})")
                
                # Test user authentication
                user_dn = str(entry.entry_dn)
                try:
                    # We'll use a known test user password
                    if entry.uid.value == 'testuser':
                        user_conn = Connection(server, user_dn, 'testpass123', auto_bind=True)
                        print(f"   ✅ Authentication test passed for {entry.uid.value}")
                        user_conn.unbind()
                except:
                    print(f"   ⚠️  Authentication test skipped for {entry.uid.value} (password unknown)")
            
            conn.unbind()
            return True
            
        except LDAPException as e:
            print(f"❌ User operations test failed: {e}")
            return False
    
    def test_ldap_account_manager_config(self):
        """Test LDAP Account Manager specific configuration"""
        print("\n🔧 LDAP Account Manager Configuration Test")
        print("=" * 50)
        
        # Test internal container connectivity (what LAM uses)
        print("Testing internal container connectivity...")
        
        # Since we can't directly test from LAM container, we'll test the configuration
        # that LAM should be using
        
        config_info = {
            "LDAP Server": "openldap:1389",
            "Base DN": self.base_dn,
            "Admin DN": self.admin_dn,
            "Admin Password": "adminpassword",
            "TLS": "Disabled",
            "Port": "1389"
        }
        
        print("📋 Expected LAM Configuration:")
        for key, value in config_info.items():
            print(f"   {key}: {value}")
        
        # Test if the configuration works
        try:
            server = Server("ldap://localhost:1389", get_info=ALL)  # External test
            conn = Connection(server, self.admin_dn, self.admin_password, auto_bind=True)
            print("✅ Configuration parameters are correct")
            conn.unbind()
            return True
        except Exception as e:
            print(f"❌ Configuration test failed: {e}")
            return False
    
    def run_full_diagnostic(self):
        """Run complete LDAP diagnostic"""
        print("🔍 LDAP Complete Diagnostic Report")
        print("=" * 60)
        print(f"Timestamp: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
        
        results = []
        
        # Test 1: Network connectivity
        results.append(("Network Connectivity", self.test_network_connectivity()))
        
        # Test 2: LDAP connection
        results.append(("LDAP Connection", self.test_ldap_connection()))
        
        # Test 3: User operations
        results.append(("User Operations", self.test_user_operations()))
        
        # Test 4: LAM configuration
        results.append(("LAM Configuration", self.test_ldap_account_manager_config()))
        
        # Summary
        print("\n📊 Diagnostic Summary")
        print("=" * 30)
        
        passed = 0
        total = len(results)
        
        for test_name, result in results:
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{test_name}: {status}")
            if result:
                passed += 1
        
        print(f"\nOverall: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! LDAP system is fully functional.")
        else:
            print("⚠️  Some tests failed. Check the detailed output above.")
            
        return passed == total

def main():
    parser = argparse.ArgumentParser(description="LDAP Connection Troubleshooting Tool")
    parser.add_argument('--test', choices=['network', 'connection', 'users', 'lam', 'full'], 
                       default='full', help='Type of test to run')
    
    args = parser.parse_args()
    
    troubleshooter = LDAPTroubleshooter()
    
    if args.test == 'network':
        troubleshooter.test_network_connectivity()
    elif args.test == 'connection':
        troubleshooter.test_ldap_connection()
    elif args.test == 'users':
        troubleshooter.test_user_operations()
    elif args.test == 'lam':
        troubleshooter.test_ldap_account_manager_config()
    elif args.test == 'full':
        troubleshooter.run_full_diagnostic()

if __name__ == "__main__":
    main()
