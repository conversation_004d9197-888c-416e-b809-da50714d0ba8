#!/usr/bin/env python3
"""
Test script to verify the processing status endpoint is working correctly.
This helps diagnose CORS and API response issues.
"""

import requests
import json
import sys
from pathlib import Path

def test_processing_status_endpoint():
    """Test the processing status endpoint"""
    print("🧪 Testing Processing Status Endpoint")
    print("=" * 50)
    
    # Test different ports
    ports = [5000, 5001]
    
    for port in ports:
        print(f"\n🔍 Testing port {port}...")
        url = f"http://localhost:{port}/api/processing-status"
        
        try:
            # Test with requests library (similar to curl)
            response = requests.get(url, timeout=5)
            
            print(f"✅ Status Code: {response.status_code}")
            print(f"✅ Content-Type: {response.headers.get('Content-Type', 'Not set')}")
            print(f"✅ CORS Headers:")
            print(f"   Access-Control-Allow-Origin: {response.headers.get('Access-Control-Allow-Origin', 'Not set')}")
            print(f"   Access-Control-Allow-Methods: {response.headers.get('Access-Control-Allow-Methods', 'Not set')}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ JSON Response: {json.dumps(data, indent=2)}")
                    
                    # Validate response structure
                    if 'success' in data and 'processing' in data:
                        processing = data['processing']
                        required_fields = ['queue_size', 'active_threads', 'processed_count', 'error_count', 'running', 'current_tasks']
                        
                        missing_fields = [field for field in required_fields if field not in processing]
                        if missing_fields:
                            print(f"❌ Missing fields: {missing_fields}")
                        else:
                            print("✅ All required fields present")
                    else:
                        print("❌ Invalid response structure")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ Invalid JSON response: {e}")
                    print(f"Raw response: {response.text}")
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"Response: {response.text}")
                
        except requests.exceptions.ConnectionError:
            print(f"❌ Connection failed - server not running on port {port}")
        except requests.exceptions.Timeout:
            print(f"❌ Request timeout on port {port}")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")

def test_browser_simulation():
    """Simulate browser request with CORS preflight"""
    print(f"\n🌐 Testing Browser CORS Simulation")
    print("=" * 50)
    
    # Test the port that's actually working
    url = "http://localhost:5001/api/processing-status"
    
    try:
        # Simulate CORS preflight request
        print("🔍 Testing CORS preflight (OPTIONS request)...")
        options_response = requests.options(
            url,
            headers={
                'Origin': 'http://localhost:5001',
                'Access-Control-Request-Method': 'GET',
                'Access-Control-Request-Headers': 'Content-Type'
            },
            timeout=5
        )
        
        print(f"✅ OPTIONS Status: {options_response.status_code}")
        print(f"✅ CORS Headers in OPTIONS response:")
        for header, value in options_response.headers.items():
            if 'access-control' in header.lower():
                print(f"   {header}: {value}")
        
        # Simulate actual browser GET request
        print("\n🔍 Testing actual GET request with Origin header...")
        get_response = requests.get(
            url,
            headers={
                'Origin': 'http://localhost:5001',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            },
            timeout=5
        )
        
        print(f"✅ GET Status: {get_response.status_code}")
        print(f"✅ Content-Type: {get_response.headers.get('Content-Type', 'Not set')}")
        
        if get_response.status_code == 200:
            try:
                data = get_response.json()
                print("✅ Browser simulation successful - JSON response received")
            except json.JSONDecodeError:
                print("❌ Browser simulation failed - invalid JSON")
        else:
            print(f"❌ Browser simulation failed - HTTP {get_response.status_code}")
            
    except Exception as e:
        print(f"❌ Browser simulation error: {e}")

def main():
    """Main test function"""
    print("🧪 RepoSense AI Processing Status Endpoint Test")
    print("=" * 60)
    
    test_processing_status_endpoint()
    test_browser_simulation()
    
    print("\n" + "=" * 60)
    print("🎯 Test Summary:")
    print("If all tests pass, the processing status endpoint should work in browsers.")
    print("If tests fail, check:")
    print("1. Server is running and accessible")
    print("2. CORS headers are properly set")
    print("3. Content-Type headers are correct")
    print("4. No proxy/firewall blocking requests")

if __name__ == "__main__":
    main()
