services:
  reposense-ai:
    build:
      context: ./reposense_ai
      dockerfile: Dockerfile
    image: reposense-ai:latest
    container_name: reposense-ai
    restart: unless-stopped
    ports:
      - "5001:5000"
    volumes:
      # Essential volumes - always mounted for data persistence
      - ./reposense_ai/data:/app/data
      - ./reposense_ai/logs:/app/logs
      # Development volumes - mount source code for hot reloads
      - ./reposense_ai:/app
      # Exclude build artifacts to avoid conflicts (but keep data visible)
      - /app/node_modules
      - /app/.git
    environment:
      # Web interface settings (minimal environment)
      - REPOSENSE_AI_WEB_HOST=0.0.0.0
      - REPOSENSE_AI_WEB_PORT=5000
      # Development settings (override via .env file)
      - REPOSENSE_AI_LOG_LEVEL=${REPOSENSE_AI_LOG_LEVEL:-INFO}
      - REPOSENSE_AI_DB_DEBUG=${REPOSENSE_AI_DB_DEBUG:-false}
    # Standalone mode - use external Ollama service (uncomment to override web config)
    # - OLLAMA_BASE_URL=http://localhost:11434
    # Optional deployment overrides (uncomment if needed):
    # - OLLAMA_MODEL=qwen3
    networks:
      - reposense-ai-network
    extra_hosts:
      - "host.docker.internal:host-gateway"
      - "sundc:***********"
    healthcheck:
      test: ["CMD", "python", "-c", "import requests; requests.get('http://localhost:5000/health', timeout=5)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mailhog:
    image: mailhog/mailhog:latest
    container_name: reposense-ai-mailhog
    restart: unless-stopped
    ports:
      - "1025:1025"  # SMTP port
      - "8025:8025"  # Web UI port
    networks:
      - reposense-ai-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8025"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 10s

  openldap:
    image: bitnami/openldap:latest
    container_name: reposense-ai-openldap
    restart: unless-stopped
    ports:
      - "1389:1389"  # LDAP port (external access)
      - "1636:1636"  # LDAPS port
    environment:
      - LDAP_ADMIN_USERNAME=admin
      - LDAP_ADMIN_PASSWORD=adminpassword
      - LDAP_USERS=user01,user02,user03
      - LDAP_PASSWORDS=password1,password2,password3
      - LDAP_ROOT=dc=reposense,dc=local
      - LDAP_USER_DC=users
      - LDAP_GROUP=developers
      - LDAP_ENABLE_TLS=no
      - LDAP_SKIP_DEFAULT_TREE=no
      - LDAP_EXTRA_SCHEMAS=cosine,inetorgperson,nis
    networks:
      - reposense-ai-network
    volumes:
      - ldap_data:/bitnami/openldap
    healthcheck:
      test: ["CMD", "ldapsearch", "-x", "-H", "ldap://localhost:1389", "-b", "dc=reposense,dc=local", "-D", "cn=admin,dc=reposense,dc=local", "-w", "adminpassword", "(objectclass=*)", "-LLL"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  ldap-admin:
    image: ldapaccountmanager/lam:stable
    container_name: reposense-ai-ldap-admin
    restart: unless-stopped
    ports:
      - "8081:80"  # Web UI port (changed to avoid VMware conflict)
    environment:
      - LAM_SERVER_URL=ldap://openldap:1389
      - LAM_DOMAIN=dc=reposense,dc=local
      - LAM_ADMINS=cn=admin,dc=reposense,dc=local
      - LAM_PASSWORD=adminpassword
      - LAM_LANG=en_US.utf8
    volumes:
      - ldap-admin-config:/var/lib/ldap-account-manager/config
    networks:
      - reposense-ai-network
    depends_on:
      - openldap
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/lam/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

# Network for standalone operation
networks:
  reposense-ai-network:
    driver: bridge

# Volumes for data persistence
volumes:
  ldap_data:
    driver: local
  ldap-admin-config:
    driver: local
