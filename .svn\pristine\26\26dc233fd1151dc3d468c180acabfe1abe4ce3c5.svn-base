#!/usr/bin/env python3
"""
LDAP Test Runner for RepoSense AI

This script runs all LDAP-related unit tests and provides detailed reporting.
It can run tests with or without an actual LDAP server connection.

Usage:
    python run_ldap_tests.py                    # Run all LDAP tests
    python run_ldap_tests.py --unit-only        # Run only unit tests (no network)
    python run_ldap_tests.py --integration      # Run integration tests (requires LDAP server)
    python run_ldap_tests.py --verbose          # Run with verbose output
    python run_ldap_tests.py --coverage         # Run with coverage reporting
"""

import argparse
import os
import subprocess
import sys
from pathlib import Path


def run_command(cmd, description=""):
    """Run a command and return the result."""
    print(f"\n{'='*60}")
    if description:
        print(f"Running: {description}")
    print(f"Command: {' '.join(cmd)}")
    print('='*60)
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, check=False)
        print(result.stdout)
        if result.stderr:
            print("STDERR:", result.stderr)
        return result.returncode == 0
    except Exception as e:
        print(f"Error running command: {e}")
        return False


def check_ldap_availability():
    """Check if LDAP library is available."""
    try:
        import ldap3
        print("✅ LDAP library (ldap3) is available")
        return True
    except ImportError:
        print("❌ LDAP library (ldap3) not available")
        print("   Install with: pip install ldap3")
        return False


def check_pytest_availability():
    """Check if pytest is available."""
    try:
        import pytest
        print("✅ pytest is available")
        return True
    except ImportError:
        print("❌ pytest not available")
        print("   Install with: pip install pytest")
        return False


def main():
    parser = argparse.ArgumentParser(description="Run LDAP tests for RepoSense AI")
    parser.add_argument("--unit-only", action="store_true", 
                       help="Run only unit tests (no network/integration tests)")
    parser.add_argument("--integration", action="store_true",
                       help="Run integration tests (requires LDAP server)")
    parser.add_argument("--network", action="store_true",
                       help="Run network tests (requires LDAP server)")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="Run with verbose output")
    parser.add_argument("--coverage", action="store_true",
                       help="Run with coverage reporting")
    parser.add_argument("--html-report", action="store_true",
                       help="Generate HTML coverage report")
    
    args = parser.parse_args()
    
    # Get the directory containing this script
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    print("LDAP Test Runner for RepoSense AI")
    print("="*50)
    
    # Check prerequisites
    if not check_pytest_availability():
        sys.exit(1)
    
    ldap_available = check_ldap_availability()
    if not ldap_available:
        print("\nWarning: LDAP library not available. Some tests will be skipped.")
    
    # Build pytest command
    pytest_cmd = ["python", "-m", "pytest"]
    
    # Add test files
    test_files = [
        "test_ldap_sync_service.py",
        "test_ldap_authentication.py"
    ]
    
    # Add markers based on arguments
    markers = []
    if args.unit_only:
        markers.append("-m unit")
        print("\n🧪 Running UNIT tests only")
    elif args.integration:
        markers.append("-m integration")
        print("\n🔗 Running INTEGRATION tests")
    elif args.network:
        markers.append("-m network")
        print("\n🌐 Running NETWORK tests")
    else:
        print("\n🧪 Running ALL LDAP tests")
    
    # Add verbosity
    if args.verbose:
        pytest_cmd.append("-v")
    
    # Add coverage
    if args.coverage:
        pytest_cmd.extend([
            "--cov=../ldap_sync_service",
            "--cov=../user_database", 
            "--cov-report=term-missing"
        ])
        if args.html_report:
            pytest_cmd.append("--cov-report=html:htmlcov")
    
    # Add markers
    pytest_cmd.extend(markers)
    
    # Add test files
    pytest_cmd.extend(test_files)
    
    # Run the tests
    success = run_command(pytest_cmd, "LDAP Unit Tests")
    
    if args.coverage and args.html_report:
        print(f"\n📊 Coverage report generated in: {script_dir}/htmlcov/index.html")
    
    # Summary
    print("\n" + "="*60)
    if success:
        print("✅ LDAP tests completed successfully!")
    else:
        print("❌ Some LDAP tests failed!")
        
    print("\nTest Categories:")
    print("  • Unit Tests: Basic functionality without external dependencies")
    print("  • Integration Tests: Tests requiring LDAP server connection")
    print("  • Network Tests: Tests requiring network connectivity")
    
    if not ldap_available:
        print("\n⚠️  Note: Install ldap3 library for full test coverage:")
        print("   pip install ldap3")
    
    print("\nFor more test options, run:")
    print("  python run_ldap_tests.py --help")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
