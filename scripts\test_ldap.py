#!/usr/bin/env python3
"""
LDAP Testing Script for RepoSense AI

This script helps test LDAP connectivity and authentication
against the test LDAP server included in the Docker Compose setup.
"""

import sys
import argparse
from typing import Optional, Dict, Any

try:
    import ldap3
    from ldap3 import Server, Connection, ALL, SUBTREE
except ImportError:
    print("❌ ldap3 library not found. Install with: pip install ldap3")
    sys.exit(1)


class LDAPTester:
    """Simple LDAP testing utility"""
    
    def __init__(self, host: str = "localhost", port: int = 1389):
        self.host = host
        self.port = port
        self.base_dn = "dc=reposense,dc=local"
        self.admin_dn = "cn=admin,dc=reposense,dc=local"
        self.admin_password = "adminpassword"
        self.server = Server(f"ldap://{host}:{port}", get_info=ALL)
    
    def test_connection(self) -> bool:
        """Test basic LDAP server connectivity"""
        print(f"🔗 Testing connection to ldap://{self.host}:{self.port}")
        
        try:
            conn = Connection(self.server)
            if conn.bind():
                print("✅ Connection successful!")
                conn.unbind()
                return True
            else:
                print("❌ Connection failed!")
                return False
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    def test_admin_auth(self) -> bool:
        """Test admin authentication"""
        print(f"🔐 Testing admin authentication...")
        
        try:
            conn = Connection(self.server, self.admin_dn, self.admin_password)
            if conn.bind():
                print("✅ Admin authentication successful!")
                conn.unbind()
                return True
            else:
                print("❌ Admin authentication failed!")
                return False
        except Exception as e:
            print(f"❌ Admin authentication error: {e}")
            return False
    
    def test_user_auth(self, username: str, password: str) -> bool:
        """Test user authentication"""
        user_dn = f"cn={username},ou=users,{self.base_dn}"
        print(f"👤 Testing user authentication for: {user_dn}")
        
        try:
            conn = Connection(self.server, user_dn, password)
            if conn.bind():
                print("✅ User authentication successful!")
                conn.unbind()
                return True
            else:
                print("❌ User authentication failed!")
                return False
        except Exception as e:
            print(f"❌ User authentication error: {e}")
            return False
    
    def list_users(self) -> Optional[list]:
        """List all users in the directory"""
        print(f"📋 Listing users in directory...")
        
        try:
            conn = Connection(self.server, self.admin_dn, self.admin_password)
            if not conn.bind():
                print("❌ Failed to bind as admin")
                return None
            
            search_base = f"ou=users,{self.base_dn}"
            search_filter = "(objectClass=inetOrgPerson)"
            
            conn.search(search_base, search_filter, SUBTREE, attributes=['cn', 'sn', 'givenName', 'uid'])
            
            if conn.entries:
                print(f"✅ Found {len(conn.entries)} users:")
                users = []
                for entry in conn.entries:
                    user_info = {
                        'dn': str(entry.entry_dn),
                        'cn': str(entry.cn) if entry.cn else '',
                        'sn': str(entry.sn) if entry.sn else '',
                        'givenName': str(entry.givenName) if entry.givenName else '',
                        'uid': str(entry.uid) if entry.uid else ''
                    }
                    users.append(user_info)
                    print(f"   • {user_info['cn']} ({user_info['dn']})")
                
                conn.unbind()
                return users
            else:
                print("❌ No users found")
                conn.unbind()
                return []
                
        except Exception as e:
            print(f"❌ Error listing users: {e}")
            return None
    
    def search_user(self, username: str) -> Optional[Dict[str, Any]]:
        """Search for a specific user"""
        print(f"🔍 Searching for user: {username}")
        
        try:
            conn = Connection(self.server, self.admin_dn, self.admin_password)
            if not conn.bind():
                print("❌ Failed to bind as admin")
                return None
            
            search_base = f"ou=users,{self.base_dn}"
            search_filter = f"(cn={username})"
            
            conn.search(search_base, search_filter, SUBTREE, attributes=['*'])
            
            if conn.entries:
                entry = conn.entries[0]
                print(f"✅ User found: {entry.entry_dn}")
                
                user_info = {}
                for attr in entry.entry_attributes:
                    user_info[attr] = str(entry[attr])
                    print(f"   {attr}: {entry[attr]}")
                
                conn.unbind()
                return user_info
            else:
                print(f"❌ User '{username}' not found")
                conn.unbind()
                return None
                
        except Exception as e:
            print(f"❌ Error searching for user: {e}")
            return None
    
    def run_full_test(self) -> bool:
        """Run a comprehensive test suite"""
        print("🧪 Running full LDAP test suite...")
        print("=" * 50)
        
        success = True
        
        # Test 1: Basic connection
        if not self.test_connection():
            success = False
        print()
        
        # Test 2: Admin authentication
        if not self.test_admin_auth():
            success = False
        print()
        
        # Test 3: List users
        users = self.list_users()
        if users is None:
            success = False
        print()
        
        # Test 4: Test default users
        default_users = [
            ("user01", "password1"),
            ("user02", "password2"),
            ("user03", "password3")
        ]
        
        for username, password in default_users:
            if not self.test_user_auth(username, password):
                success = False
            print()
        
        print("=" * 50)
        if success:
            print("✅ All tests passed!")
        else:
            print("❌ Some tests failed!")
        
        return success


def main():
    parser = argparse.ArgumentParser(description="Test LDAP connectivity and authentication")
    parser.add_argument("--host", default="localhost", help="LDAP server host")
    parser.add_argument("--port", type=int, default=1389, help="LDAP server port")
    parser.add_argument("--test", choices=["connection", "admin", "users", "full"], 
                       default="full", help="Type of test to run")
    parser.add_argument("--username", help="Username to test authentication")
    parser.add_argument("--password", help="Password for user authentication")
    parser.add_argument("--search", help="Search for a specific user")
    
    args = parser.parse_args()
    
    tester = LDAPTester(args.host, args.port)
    
    if args.search:
        tester.search_user(args.search)
    elif args.username and args.password:
        tester.test_user_auth(args.username, args.password)
    elif args.test == "connection":
        tester.test_connection()
    elif args.test == "admin":
        tester.test_admin_auth()
    elif args.test == "users":
        tester.list_users()
    elif args.test == "full":
        tester.run_full_test()


if __name__ == "__main__":
    main()
