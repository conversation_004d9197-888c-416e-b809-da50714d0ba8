#!/usr/bin/env python3
"""
Simple LDAP Browser for RepoSense AI
Provides basic LDAP directory browsing and user management functionality
"""

import argparse
import sys
from ldap3 import Server, Connection, ALL, SUBTREE
from ldap3.core.exceptions import LDAPException
import json

class LDAPBrowser:
    def __init__(self, server_uri="ldap://localhost:1389", base_dn="dc=reposense,dc=local"):
        self.server_uri = server_uri
        self.base_dn = base_dn
        self.admin_dn = "cn=admin,dc=reposense,dc=local"
        self.admin_password = "adminpassword"
        
    def connect(self):
        """Connect to LDAP server"""
        try:
            server = Server(self.server_uri, get_info=ALL)
            conn = Connection(server, self.admin_dn, self.admin_password, auto_bind=True)
            return conn
        except LDAPException as e:
            print(f"❌ Failed to connect to LDAP server: {e}")
            return None
    
    def browse_directory(self):
        """Browse the LDAP directory structure"""
        conn = self.connect()
        if not conn:
            return
            
        print(f"🌳 LDAP Directory Structure: {self.base_dn}")
        print("=" * 60)
        
        try:
            # Search for all entries
            conn.search(self.base_dn, '(objectClass=*)', SUBTREE, attributes=['*'])
            
            for entry in conn.entries:
                dn = str(entry.entry_dn)
                print(f"\n📁 {dn}")
                
                # Show key attributes
                if hasattr(entry, 'objectClass'):
                    print(f"   Object Classes: {', '.join(entry.objectClass.values)}")
                if hasattr(entry, 'cn'):
                    print(f"   Common Name: {entry.cn.value}")
                if hasattr(entry, 'mail'):
                    print(f"   Email: {entry.mail.value}")
                if hasattr(entry, 'uid'):
                    print(f"   User ID: {entry.uid.value}")
                    
        except LDAPException as e:
            print(f"❌ Error browsing directory: {e}")
        finally:
            conn.unbind()
    
    def list_users(self):
        """List all users in the directory"""
        conn = self.connect()
        if not conn:
            return
            
        print("👥 LDAP Users")
        print("=" * 40)
        
        try:
            # Search for user entries
            conn.search(self.base_dn, '(objectClass=inetOrgPerson)', SUBTREE, 
                       attributes=['cn', 'uid', 'mail', 'givenName', 'sn'])
            
            if not conn.entries:
                print("No users found in the directory.")
                return
                
            for entry in conn.entries:
                print(f"\n👤 {entry.entry_dn}")
                if hasattr(entry, 'cn'):
                    print(f"   Name: {entry.cn.value}")
                if hasattr(entry, 'uid'):
                    print(f"   Username: {entry.uid.value}")
                if hasattr(entry, 'mail'):
                    print(f"   Email: {entry.mail.value}")
                if hasattr(entry, 'givenName') and hasattr(entry, 'sn'):
                    print(f"   Full Name: {entry.givenName.value} {entry.sn.value}")
                    
        except LDAPException as e:
            print(f"❌ Error listing users: {e}")
        finally:
            conn.unbind()
    
    def create_user(self, username, password, email, first_name, last_name):
        """Create a new user"""
        conn = self.connect()
        if not conn:
            return False
            
        user_dn = f"cn={username},ou=users,{self.base_dn}"
        
        try:
            # User attributes
            attributes = {
                'objectClass': ['inetOrgPerson', 'organizationalPerson', 'person'],
                'cn': username,
                'sn': last_name,
                'givenName': first_name,
                'mail': email,
                'userPassword': password,
                'uid': username
            }
            
            # Add the user
            conn.add(user_dn, attributes=attributes)
            
            if conn.result['result'] == 0:
                print(f"✅ User '{username}' created successfully!")
                return True
            else:
                print(f"❌ Failed to create user: {conn.result['description']}")
                return False
                
        except LDAPException as e:
            print(f"❌ Error creating user: {e}")
            return False
        finally:
            conn.unbind()
    
    def test_user_auth(self, username, password):
        """Test user authentication"""
        user_dn = f"cn={username},ou=users,{self.base_dn}"
        
        try:
            server = Server(self.server_uri, get_info=ALL)
            conn = Connection(server, user_dn, password, auto_bind=True)
            print(f"✅ User '{username}' authenticated successfully!")
            conn.unbind()
            return True
        except LDAPException as e:
            print(f"❌ Authentication failed for user '{username}': {e}")
            return False
    
    def server_info(self):
        """Display server information"""
        conn = self.connect()
        if not conn:
            return
            
        print("🖥️  LDAP Server Information")
        print("=" * 50)
        print(f"Server URI: {self.server_uri}")
        print(f"Base DN: {self.base_dn}")
        print(f"Server Info: {conn.server.info}")
        
        conn.unbind()

def main():
    parser = argparse.ArgumentParser(description="Simple LDAP Browser for RepoSense AI")
    parser.add_argument('--action', choices=['browse', 'users', 'create', 'test', 'info'], 
                       default='browse', help='Action to perform')
    parser.add_argument('--username', help='Username for create/test actions')
    parser.add_argument('--password', help='Password for create/test actions')
    parser.add_argument('--email', help='Email for create action')
    parser.add_argument('--first-name', help='First name for create action')
    parser.add_argument('--last-name', help='Last name for create action')
    
    args = parser.parse_args()
    
    browser = LDAPBrowser()
    
    if args.action == 'browse':
        browser.browse_directory()
    elif args.action == 'users':
        browser.list_users()
    elif args.action == 'create':
        if not all([args.username, args.password, args.email, args.first_name, args.last_name]):
            print("❌ For create action, you must provide: --username, --password, --email, --first-name, --last-name")
            sys.exit(1)
        browser.create_user(args.username, args.password, args.email, args.first_name, args.last_name)
    elif args.action == 'test':
        if not all([args.username, args.password]):
            print("❌ For test action, you must provide: --username, --password")
            sys.exit(1)
        browser.test_user_auth(args.username, args.password)
    elif args.action == 'info':
        browser.server_info()

if __name__ == "__main__":
    main()
