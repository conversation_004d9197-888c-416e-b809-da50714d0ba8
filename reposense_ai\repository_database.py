#!/usr/bin/env python3
"""
Repository Database Management for RepoSense AI
Handles database operations for repository management
"""

import json
import logging
import sqlite3
import threading
from contextlib import contextmanager
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Tuple

from models import (
    HistoricalScanConfig,
    HistoricalScanStatus,
    RepositoryConfig,
    RepositoryUserRelationship,
)


class RepositoryDatabase:
    """Database operations for repository management"""

    def __init__(self, db_path: str):
        self.db_path = Path(db_path)
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()

        # Ensure database directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

    def _serialize_historical_scan(self, scan_config: HistoricalScanConfig) -> str:
        """Serialize HistoricalScanConfig to JSON string"""
        if not scan_config:
            return "{}"

        data = {
            "enabled": scan_config.enabled,
            "start_revision": scan_config.start_revision,
            "end_revision": scan_config.end_revision,
            "scan_status": scan_config.scan_status.value
            if scan_config.scan_status
            else "NOT_STARTED",
            "scan_started_at": scan_config.scan_started_at.isoformat()
            if scan_config.scan_started_at
            else None,
            "scan_completed_at": scan_config.scan_completed_at.isoformat()
            if scan_config.scan_completed_at
            else None,
            "total_revisions": scan_config.total_revisions,
            "processed_revisions": scan_config.processed_revisions,
            "failed_revisions": scan_config.failed_revisions,
            "error_message": scan_config.error_message,
        }
        return json.dumps(data)

    def _deserialize_historical_scan(self, json_str: str) -> HistoricalScanConfig:
        """Deserialize HistoricalScanConfig from JSON string"""
        if not json_str:
            return HistoricalScanConfig()

        try:
            data = json.loads(json_str)

            # Parse datetime fields
            scan_started_at = None
            if data.get("scan_started_at"):
                try:
                    scan_started_at = datetime.fromisoformat(data["scan_started_at"])
                except ValueError:
                    pass

            scan_completed_at = None
            if data.get("scan_completed_at"):
                try:
                    scan_completed_at = datetime.fromisoformat(
                        data["scan_completed_at"]
                    )
                except ValueError:
                    pass

            # Parse scan status
            scan_status = HistoricalScanStatus.NOT_STARTED
            if data.get("scan_status"):
                try:
                    scan_status = HistoricalScanStatus(data["scan_status"])
                except ValueError:
                    pass

            return HistoricalScanConfig(
                enabled=data.get("enabled", False),
                start_revision=data.get("start_revision", 1),
                end_revision=data.get("end_revision"),
                scan_status=scan_status,
                scan_started_at=scan_started_at,
                scan_completed_at=scan_completed_at,
                total_revisions=data.get("total_revisions", 0),
                processed_revisions=data.get("processed_revisions", 0),
                failed_revisions=data.get("failed_revisions", 0),
                error_message=data.get("error_message"),
            )
        except (json.JSONDecodeError, TypeError, KeyError):
            return HistoricalScanConfig()

    @contextmanager
    def _get_connection(self):
        """Get database connection with proper locking"""
        with self._lock:
            conn = sqlite3.connect(str(self.db_path), timeout=30.0)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            try:
                yield conn
            finally:
                conn.close()

    def _serialize_json_field(self, data) -> str:
        """Serialize data to JSON string"""
        if data is None:
            return "[]" if isinstance(data, list) else "{}"
        return json.dumps(data)

    def _deserialize_json_field(self, json_str: str, default_type=list):
        """Deserialize JSON string to data"""
        if not json_str:
            return [] if default_type == list else {}

        try:
            return json.loads(json_str)
        except (json.JSONDecodeError, TypeError):
            return [] if default_type == list else {}

    def _repository_from_row(self, row: sqlite3.Row) -> RepositoryConfig:
        """Convert database row to RepositoryConfig object"""
        # Parse JSON fields
        assigned_users = self._deserialize_json_field(row["assigned_users"])
        email_recipients = self._deserialize_json_field(row["email_recipients"])
        user_relationships_data = self._deserialize_json_field(
            row["user_relationships"]
        )
        path_watchers = self._deserialize_json_field(row["path_watchers"], dict)

        # Parse historical scan config
        historical_scan = self._deserialize_historical_scan(
            row["historical_scan"] or ""
        )

        # Parse user relationships
        user_relationships = []
        for rel_data in user_relationships_data:
            try:
                # Handle notification_preferences deserialization
                if "notification_preferences" in rel_data:
                    prefs_data = rel_data["notification_preferences"]
                    # Convert enabled_categories back to enum objects
                    enabled_categories = []
                    for cat_value in prefs_data.get("enabled_categories", []):
                        try:
                            from models import NotificationCategory

                            enabled_categories.append(NotificationCategory(cat_value))
                        except ValueError:
                            continue

                    # Create RepositoryNotificationPreferences object
                    from models import RepositoryNotificationPreferences

                    notification_prefs = RepositoryNotificationPreferences(
                        enabled_categories=enabled_categories,
                        notify_on_own_commits=prefs_data.get(
                            "notify_on_own_commits", False
                        ),
                        notify_on_high_risk_only=prefs_data.get(
                            "notify_on_high_risk_only", False
                        ),
                        notify_on_specific_paths=prefs_data.get(
                            "notify_on_specific_paths", []
                        ),
                        notify_on_processing_failures=prefs_data.get(
                            "notify_on_processing_failures", True
                        ),
                        notify_on_scan_completion=prefs_data.get(
                            "notify_on_scan_completion", True
                        ),
                        immediate_notification=prefs_data.get(
                            "immediate_notification", True
                        ),
                        digest_frequency=prefs_data.get("digest_frequency", "never"),
                    )
                    rel_data["notification_preferences"] = notification_prefs

                # Convert relationship_type back to enum
                if "relationship_type" in rel_data:
                    from models import RepositoryRelationshipType

                    rel_data["relationship_type"] = RepositoryRelationshipType(
                        rel_data["relationship_type"]
                    )

                user_relationships.append(RepositoryUserRelationship(**rel_data))
            except (TypeError, ValueError) as e:
                self.logger.warning(f"Failed to deserialize user relationship: {e}")
                continue

        # Parse datetime fields
        last_commit_date = None
        if row["last_commit_date"]:
            try:
                last_commit_date = datetime.fromisoformat(row["last_commit_date"])
            except ValueError:
                pass

        last_processed_time = None
        if row["last_processed_time"]:
            try:
                last_processed_time = datetime.fromisoformat(row["last_processed_time"])
            except ValueError:
                pass

        return RepositoryConfig(
            id=row["id"],
            name=row["name"],
            url=row["url"],
            type=row["type"],
            username=row["username"],
            password=row["password"],
            last_revision=row["last_revision"],
            last_commit_date=last_commit_date,
            last_processed_time=last_processed_time,
            enabled=bool(row["enabled"]),
            branch_path=row["branch_path"],
            monitor_all_branches=bool(row["monitor_all_branches"]),
            assigned_users=assigned_users,
            email_recipients=email_recipients,
            risk_aggressiveness=row["risk_aggressiveness"],
            risk_description=row["risk_description"],
            historical_scan=historical_scan,
            user_relationships=user_relationships,
            path_watchers=path_watchers,
        )

    def _repository_to_row_data(self, repo: RepositoryConfig) -> dict:
        """Convert RepositoryConfig object to database row data"""
        # Serialize user relationships
        user_relationships_data = []
        for rel in repo.user_relationships:
            rel_dict = {
                "user_id": rel.user_id,
                "repository_id": rel.repository_id,
                "relationship_type": rel.relationship_type.value,
                "assigned_by": rel.assigned_by,
                "assigned_date": rel.assigned_date,
                "last_modified": rel.last_modified,
                "can_modify_settings": rel.can_modify_settings,
                "can_assign_users": rel.can_assign_users,
                "can_view_sensitive_data": rel.can_view_sensitive_data,
            }
            # Add notification preferences if they exist
            if (
                hasattr(rel, "notification_preferences")
                and rel.notification_preferences
            ):
                rel_dict["notification_preferences"] = {
                    "enabled_categories": [
                        cat.value
                        for cat in rel.notification_preferences.enabled_categories
                    ],
                    "notify_on_own_commits": rel.notification_preferences.notify_on_own_commits,
                    "notify_on_high_risk_only": rel.notification_preferences.notify_on_high_risk_only,
                    "notify_on_specific_paths": rel.notification_preferences.notify_on_specific_paths,
                    "notify_on_processing_failures": rel.notification_preferences.notify_on_processing_failures,
                    "notify_on_scan_completion": rel.notification_preferences.notify_on_scan_completion,
                    "immediate_notification": rel.notification_preferences.immediate_notification,
                    "digest_frequency": rel.notification_preferences.digest_frequency,
                }
            user_relationships_data.append(rel_dict)

        return {
            "id": repo.id,
            "name": repo.name,
            "url": repo.url,
            "type": repo.type,
            "username": repo.username,
            "password": repo.password,
            "last_revision": repo.last_revision,
            "last_commit_date": repo.last_commit_date.isoformat()
            if repo.last_commit_date
            else None,
            "last_processed_time": repo.last_processed_time.isoformat()
            if repo.last_processed_time
            else None,
            "enabled": int(repo.enabled),
            "branch_path": repo.branch_path,
            "monitor_all_branches": int(repo.monitor_all_branches),
            "assigned_users": json.dumps(repo.assigned_users),
            "email_recipients": json.dumps(repo.email_recipients),
            "risk_aggressiveness": repo.risk_aggressiveness,
            "risk_description": repo.risk_description,
            "historical_scan": self._serialize_historical_scan(repo.historical_scan),
            "user_relationships": json.dumps(user_relationships_data),
            "path_watchers": json.dumps(repo.path_watchers),
            "created_date": datetime.now().isoformat(),
            "last_modified": datetime.now().isoformat(),
        }

    def create_repository(self, repo: RepositoryConfig) -> bool:
        """Create a new repository in the database"""
        try:
            with self._get_connection() as conn:
                row_data = self._repository_to_row_data(repo)

                conn.execute(
                    """
                    INSERT INTO repositories (
                        id, name, url, type, username, password, last_revision,
                        last_commit_date, last_processed_time, enabled, branch_path,
                        monitor_all_branches, assigned_users, email_recipients,
                        risk_aggressiveness, risk_description, historical_scan,
                        user_relationships, path_watchers, created_date, last_modified
                    ) VALUES (
                        :id, :name, :url, :type, :username, :password, :last_revision,
                        :last_commit_date, :last_processed_time, :enabled, :branch_path,
                        :monitor_all_branches, :assigned_users, :email_recipients,
                        :risk_aggressiveness, :risk_description, :historical_scan,
                        :user_relationships, :path_watchers, :created_date, :last_modified
                    )
                """,
                    row_data,
                )

                conn.commit()
                self.logger.info(f"Created repository in database: {repo.name}")
                return True

        except sqlite3.IntegrityError as e:
            self.logger.error(f"Repository creation failed - integrity error: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Repository creation failed: {e}")
            return False

    def get_repository_by_id(self, repo_id: str) -> Optional[RepositoryConfig]:
        """Get repository by ID"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM repositories WHERE id = ?", (repo_id,)
                )
                row = cursor.fetchone()
                return self._repository_from_row(row) if row else None
        except Exception as e:
            self.logger.error(f"Error getting repository by ID {repo_id}: {e}")
            return None

    def get_repository_by_name(self, name: str) -> Optional[RepositoryConfig]:
        """Get repository by name"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM repositories WHERE name = ? COLLATE NOCASE", (name,)
                )
                row = cursor.fetchone()
                return self._repository_from_row(row) if row else None
        except Exception as e:
            self.logger.error(f"Error getting repository by name {name}: {e}")
            return None

    def get_all_repositories(self) -> List[RepositoryConfig]:
        """Get all repositories"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("SELECT * FROM repositories ORDER BY name")
                return [self._repository_from_row(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Error getting all repositories: {e}")
            return []

    def get_enabled_repositories(self) -> List[RepositoryConfig]:
        """Get all enabled repositories"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM repositories WHERE enabled = 1 ORDER BY name"
                )
                return [self._repository_from_row(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Error getting enabled repositories: {e}")
            return []

    def update_repository(self, repo: RepositoryConfig) -> bool:
        """Update an existing repository"""
        try:
            with self._get_connection() as conn:
                row_data = self._repository_to_row_data(repo)

                conn.execute(
                    """
                    UPDATE repositories SET
                        name = :name,
                        url = :url,
                        type = :type,
                        username = :username,
                        password = :password,
                        last_revision = :last_revision,
                        last_commit_date = :last_commit_date,
                        last_processed_time = :last_processed_time,
                        enabled = :enabled,
                        branch_path = :branch_path,
                        monitor_all_branches = :monitor_all_branches,
                        assigned_users = :assigned_users,
                        email_recipients = :email_recipients,
                        risk_aggressiveness = :risk_aggressiveness,
                        risk_description = :risk_description,
                        historical_scan = :historical_scan,
                        user_relationships = :user_relationships,
                        path_watchers = :path_watchers,
                        last_modified = :last_modified
                    WHERE id = :id
                """,
                    row_data,
                )

                if conn.total_changes == 0:
                    self.logger.warning(
                        f"No repository found with ID {repo.id} to update"
                    )
                    return False

                conn.commit()
                self.logger.info(f"Updated repository in database: {repo.name}")
                return True

        except sqlite3.IntegrityError as e:
            self.logger.error(f"Repository update failed - integrity error: {e}")
            return False
        except Exception as e:
            self.logger.error(f"Repository update failed: {e}")
            return False

    def delete_repository(self, repo_id: str) -> bool:
        """Delete a repository from the database"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute(
                    "DELETE FROM repositories WHERE id = ?", (repo_id,)
                )

                if cursor.rowcount == 0:
                    self.logger.warning(
                        f"No repository found with ID {repo_id} to delete"
                    )
                    return False

                conn.commit()
                self.logger.info(f"Deleted repository from database: {repo_id}")
                return True

        except Exception as e:
            self.logger.error(f"Repository deletion failed: {e}")
            return False

    def repository_exists(self, name: str = None, url: str = None) -> bool:
        """Check if a repository exists by name or URL"""
        try:
            with self._get_connection() as conn:
                if name:
                    cursor = conn.execute(
                        "SELECT 1 FROM repositories WHERE name = ? COLLATE NOCASE",
                        (name,),
                    )
                    if cursor.fetchone():
                        return True

                if url:
                    cursor = conn.execute(
                        "SELECT 1 FROM repositories WHERE url = ?", (url,)
                    )
                    if cursor.fetchone():
                        return True

                return False
        except Exception as e:
            self.logger.error(f"Error checking repository existence: {e}")
            return False

    def get_repository_count(self) -> int:
        """Get total number of repositories"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM repositories")
                return cursor.fetchone()[0]
        except Exception as e:
            self.logger.error(f"Error getting repository count: {e}")
            return 0
