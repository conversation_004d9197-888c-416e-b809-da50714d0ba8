# Test Data for RepoSense AI

This directory contains test data and database initialization scripts for developing and testing the change request integration feature.

## Database Services

### MySQL Test Database
- **Container**: `reposense-ai-mysql-test`
- **Port**: `3306`
- **Database**: `change_requests`
- **Username**: `reposense`
- **Password**: `reposense123`
- **Root Password**: `testpassword`

### Adminer Database Management UI
- **Container**: `reposense-ai-adminer`
- **URL**: http://localhost:8080
- **Access**: Web-based database management interface

## Quick Start

1. **Start the test database services:**
   ```bash
   docker-compose up -d mysql-test adminer
   ```

2. **Access Adminer web interface:**
   - Open http://localhost:8080 in your browser
   - **System**: MySQL
   - **Server**: mysql-test
   - **Username**: reposense
   - **Password**: reposense123
   - **Database**: change_requests

3. **Test the database connection:**
   ```bash
   # From host machine
   mysql -h localhost -P 3306 -u reposense -preposense123 change_requests
   
   # Test query
   SELECT number, title, priority, status FROM change_requests LIMIT 5;
   ```

## Sample Data

The database is pre-populated with sample change requests for testing:

| Number | Title | Priority | Status | Category |
|--------|-------|----------|--------|----------|
| 123 | Fix login authentication bug | HIGH | IN_PROGRESS | Security |
| 124 | Add new reporting feature | MEDIUM | OPEN | Feature |
| 125 | Database performance optimization | HIGH | OPEN | Performance |
| 126 | Update API documentation | LOW | CLOSED | Documentation |
| 127 | Security patch deployment | HIGH | IN_PROGRESS | Security |
| 128 | Mobile app UI improvements | MEDIUM | OPEN | UI/UX |
| 129 | Payment gateway integration | HIGH | OPEN | Integration |
| 130 | Backup system enhancement | MEDIUM | IN_PROGRESS | Infrastructure |
| 131 | User notification preferences | LOW | OPEN | Feature |
| 132 | Data migration cleanup | MEDIUM | CLOSED | Maintenance |

## Testing Commit Messages

You can test the change request integration with commit messages like:

- `"Fix authentication bug - CR#123"`
- `"Implement reporting feature for Change Request 124"`
- `"Database optimization - addresses Issue #125"`
- `"Security patch deployment - Ticket-127"`
- `"UI improvements - resolves CR 128"`
- `"Payment integration work - Change #129"`

## Configuration for Testing

Add this to your RepoSense AI configuration:

```json
{
  "sql_config": {
    "enabled": true,
    "host": "mysql-test",
    "port": 3306,
    "database": "change_requests",
    "username": "reposense",
    "password": "reposense123",
    "driver": "mysql",
    "connection_timeout": 30,
    "query_timeout": 60,
    "change_request_query": "SELECT id, number, title, description, priority, status, created_date, assigned_to, category, risk_level FROM change_requests WHERE number = :change_request_number",
    "change_request_patterns": [
      "CR[#\\-\\s]*(\\d+)",
      "Change[#\\-\\s]*(\\d+)",
      "Request[#\\-\\s]*(\\d+)",
      "Ticket[#\\-\\s]*(\\d+)",
      "Issue[#\\-\\s]*(\\d+)",
      "#(\\d+)"
    ]
  }
}
```

## Database Schema

### change_requests table
- `id` - Unique identifier
- `number` - Change request number (used for matching)
- `title` - Brief description
- `description` - Detailed description
- `priority` - HIGH, MEDIUM, LOW
- `status` - Current status
- `created_date` - When created
- `assigned_to` - Email of assigned person
- `category` - Type of change
- `risk_level` - Risk assessment

### users table
- User information for assigned_to references

### change_request_updates table
- History of changes and comments

## Maintenance

### Reset Database
```bash
# Stop and remove containers
docker-compose down mysql-test adminer

# Remove volume (this will delete all data)
docker volume rm reposense_ai_mysql_test_data

# Restart services (will recreate with fresh data)
docker-compose up -d mysql-test adminer
```

### View Logs
```bash
# MySQL logs
docker logs reposense-ai-mysql-test

# Adminer logs
docker logs reposense-ai-adminer
```

### Backup Test Data
```bash
# Create backup
docker exec reposense-ai-mysql-test mysqldump -u root -ptestpassword change_requests > backup.sql

# Restore backup
docker exec -i reposense-ai-mysql-test mysql -u root -ptestpassword change_requests < backup.sql
```

## Development Notes

- The MySQL container uses the `mysql:8.0` image for compatibility
- Data persists in the `mysql_test_data` Docker volume
- Initialization scripts in `mysql-init/` run automatically on first startup
- Adminer provides a lightweight alternative to phpMyAdmin
- All services are connected via the `reposense-ai-network` Docker network

## Troubleshooting

### Connection Issues
- Ensure containers are running: `docker-compose ps`
- Check network connectivity: `docker network ls`
- Verify port availability: `netstat -an | grep 3306`

### Permission Issues
- MySQL 8.0 uses `caching_sha2_password` by default
- The test user is configured with proper permissions
- Use `mysql_native_password` if needed for compatibility

### Data Issues
- Check initialization logs: `docker logs reposense-ai-mysql-test`
- Verify data exists: Connect via Adminer and browse tables
- Reset if needed using the maintenance commands above
