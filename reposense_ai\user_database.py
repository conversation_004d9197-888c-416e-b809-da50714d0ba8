#!/usr/bin/env python3
"""
User Database Management for RepoSense AI
Handles database operations for user management
"""

import json
import logging
import sqlite3
import threading
from contextlib import contextmanager
from datetime import datetime
from pathlib import Path
from typing import List, Optional, Tuple

from models import NotificationCategory, NotificationPreferences, User, UserRole


class UserDatabase:
    """Database operations for user management"""

    def __init__(self, db_path: str):
        self.db_path = Path(db_path)
        self.logger = logging.getLogger(__name__)
        self._lock = threading.RLock()

        # Ensure database directory exists
        self.db_path.parent.mkdir(parents=True, exist_ok=True)

    def _serialize_notification_preferences(
        self, prefs: NotificationPreferences
    ) -> str:
        """Serialize NotificationPreferences to JSON string"""
        data = {
            "enabled_categories": [cat.value for cat in prefs.enabled_categories],
            "min_severity": prefs.min_severity,
            "email_enabled": prefs.email_enabled,
            "email_digest": prefs.email_digest,
            "digest_time": prefs.digest_time,
        }
        return json.dumps(data)

    def _deserialize_notification_preferences(
        self, json_str: str
    ) -> NotificationPreferences:
        """Deserialize NotificationPreferences from JSON string"""
        if not json_str:
            return NotificationPreferences()

        try:
            data = json.loads(json_str)
            enabled_categories = [
                NotificationCategory(cat) for cat in data.get("enabled_categories", [])
            ]
            return NotificationPreferences(
                enabled_categories=enabled_categories,
                min_severity=data.get("min_severity", "INFO"),
                email_enabled=data.get("email_enabled", True),
                email_digest=data.get("email_digest", False),
                digest_time=data.get("digest_time", "09:00"),
            )
        except (json.JSONDecodeError, ValueError, KeyError):
            # Return defaults if deserialization fails
            return NotificationPreferences()

    @contextmanager
    def _get_connection(self):
        """Get database connection with proper locking"""
        with self._lock:
            conn = sqlite3.connect(str(self.db_path), timeout=30.0)
            conn.row_factory = sqlite3.Row  # Enable column access by name
            try:
                yield conn
            finally:
                conn.close()

    def _user_from_row(self, row: sqlite3.Row) -> User:
        """Convert database row to User object"""
        # Parse JSON fields
        repository_subscriptions = json.loads(row["repository_subscriptions"] or "[]")

        # Handle LDAP fields (may not exist in older databases)
        try:
            ldap_groups = json.loads(row["ldap_groups"] or "[]")
        except (KeyError, IndexError):
            ldap_groups = []

        # Handle notification preferences
        notification_preferences = self._deserialize_notification_preferences(
            row["notification_preferences"] or ""
        )

        # If no preferences were stored, create defaults based on role
        if not notification_preferences.enabled_categories:
            notification_preferences = NotificationPreferences.get_defaults_for_role(
                UserRole(row["role"])
            )

        return User(
            id=row["id"],
            username=row["username"],
            email=row["email"],
            full_name=row["full_name"],
            role=UserRole(row["role"]),
            enabled=bool(row["enabled"]),
            receive_all_notifications=bool(row["receive_all_notifications"]),
            repository_subscriptions=repository_subscriptions,
            phone=row["phone"],
            department=row["department"],
            created_date=row["created_date"],
            last_modified=row["last_modified"],
            notification_preferences=notification_preferences,
            # LDAP fields (may not exist in older databases)
            ldap_synced=bool(row["ldap_synced"])
            if "ldap_synced" in row.keys()
            else False,
            ldap_dn=row["ldap_dn"] if "ldap_dn" in row.keys() else None,
            last_ldap_sync=row["last_ldap_sync"]
            if "last_ldap_sync" in row.keys()
            else None,
            ldap_groups=ldap_groups,
        )

    def _user_to_row_data(self, user: User) -> dict:
        """Convert User object to database row data"""
        return {
            "id": user.id,
            "username": user.username,
            "email": user.email,
            "full_name": user.full_name,
            "role": user.role.value,
            "enabled": int(user.enabled),
            "receive_all_notifications": int(user.receive_all_notifications),
            "repository_subscriptions": json.dumps(user.repository_subscriptions),
            "phone": user.phone,
            "department": user.department,
            "created_date": user.created_date,
            "last_modified": user.last_modified,
            "notification_preferences": self._serialize_notification_preferences(
                user.notification_preferences
            ),
            # LDAP fields
            "ldap_synced": int(user.ldap_synced),
            "ldap_dn": user.ldap_dn,
            "last_ldap_sync": user.last_ldap_sync,
            "ldap_groups": json.dumps(user.ldap_groups),
        }

    def create_user(self, user: User) -> bool:
        """Create a new user in the database"""
        try:
            with self._get_connection() as conn:
                row_data = self._user_to_row_data(user)

                conn.execute(
                    """
                    INSERT INTO users (
                        id, username, email, full_name, role, enabled,
                        receive_all_notifications, repository_subscriptions,
                        phone, department, created_date, last_modified,
                        notification_preferences, ldap_synced, ldap_dn,
                        last_ldap_sync, ldap_groups
                    ) VALUES (
                        :id, :username, :email, :full_name, :role, :enabled,
                        :receive_all_notifications, :repository_subscriptions,
                        :phone, :department, :created_date, :last_modified,
                        :notification_preferences, :ldap_synced, :ldap_dn,
                        :last_ldap_sync, :ldap_groups
                    )
                """,
                    row_data,
                )

                conn.commit()
                self.logger.info(
                    f"Created user in database: {user.username} ({user.email})"
                )
                return True

        except sqlite3.IntegrityError as e:
            self.logger.error(f"User creation failed - integrity error: {e}")
            return False
        except Exception as e:
            self.logger.error(f"User creation failed: {e}")
            return False

    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """Get user by ID"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("SELECT * FROM users WHERE id = ?", (user_id,))
                row = cursor.fetchone()
                return self._user_from_row(row) if row else None
        except Exception as e:
            self.logger.error(f"Error getting user by ID {user_id}: {e}")
            return None

    def get_user_by_email(self, email: str) -> Optional[User]:
        """Get user by email"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM users WHERE email = ? COLLATE NOCASE", (email,)
                )
                row = cursor.fetchone()
                return self._user_from_row(row) if row else None
        except Exception as e:
            self.logger.error(f"Error getting user by email {email}: {e}")
            return None

    def get_user_by_username(self, username: str) -> Optional[User]:
        """Get user by username"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM users WHERE username = ? COLLATE NOCASE", (username,)
                )
                row = cursor.fetchone()
                return self._user_from_row(row) if row else None
        except Exception as e:
            self.logger.error(f"Error getting user by username {username}: {e}")
            return None

    def get_all_users(self) -> List[User]:
        """Get all users"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("SELECT * FROM users ORDER BY username")
                return [self._user_from_row(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Error getting all users: {e}")
            return []

    def get_enabled_users(self) -> List[User]:
        """Get all enabled users"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM users WHERE enabled = 1 ORDER BY username"
                )
                return [self._user_from_row(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Error getting enabled users: {e}")
            return []

    def get_users_by_role(self, role: UserRole) -> List[User]:
        """Get all users with a specific role"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute(
                    "SELECT * FROM users WHERE role = ? AND enabled = 1 ORDER BY username",
                    (role.value,),
                )
                return [self._user_from_row(row) for row in cursor.fetchall()]
        except Exception as e:
            self.logger.error(f"Error getting users by role {role}: {e}")
            return []

    def update_user(self, user: User) -> bool:
        """Update an existing user"""
        try:
            with self._get_connection() as conn:
                row_data = self._user_to_row_data(user)

                conn.execute(
                    """
                    UPDATE users SET
                        username = :username,
                        email = :email,
                        full_name = :full_name,
                        role = :role,
                        enabled = :enabled,
                        receive_all_notifications = :receive_all_notifications,
                        repository_subscriptions = :repository_subscriptions,
                        phone = :phone,
                        department = :department,
                        last_modified = :last_modified,
                        notification_preferences = :notification_preferences,
                        ldap_synced = :ldap_synced,
                        ldap_dn = :ldap_dn,
                        last_ldap_sync = :last_ldap_sync,
                        ldap_groups = :ldap_groups
                    WHERE id = :id
                """,
                    row_data,
                )

                if conn.total_changes == 0:
                    self.logger.warning(f"No user found with ID {user.id} to update")
                    return False

                conn.commit()
                self.logger.info(
                    f"Updated user in database: {user.username} ({user.email})"
                )
                return True

        except sqlite3.IntegrityError as e:
            self.logger.error(f"User update failed - integrity error: {e}")
            return False
        except Exception as e:
            self.logger.error(f"User update failed: {e}")
            return False

    def delete_user(self, user_id: str) -> bool:
        """Delete a user from the database"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("DELETE FROM users WHERE id = ?", (user_id,))

                if cursor.rowcount == 0:
                    self.logger.warning(f"No user found with ID {user_id} to delete")
                    return False

                conn.commit()
                self.logger.info(f"Deleted user from database: {user_id}")
                return True

        except Exception as e:
            self.logger.error(f"User deletion failed: {e}")
            return False

    def user_exists(
        self, email: Optional[str] = None, username: Optional[str] = None
    ) -> bool:
        """Check if a user exists by email or username"""
        try:
            with self._get_connection() as conn:
                if email:
                    cursor = conn.execute(
                        "SELECT 1 FROM users WHERE email = ? COLLATE NOCASE", (email,)
                    )
                    if cursor.fetchone():
                        return True

                if username:
                    cursor = conn.execute(
                        "SELECT 1 FROM users WHERE username = ? COLLATE NOCASE",
                        (username,),
                    )
                    if cursor.fetchone():
                        return True

                return False
        except Exception as e:
            self.logger.error(f"Error checking user existence: {e}")
            return False

    def get_user_count(self) -> int:
        """Get total number of users"""
        try:
            with self._get_connection() as conn:
                cursor = conn.execute("SELECT COUNT(*) FROM users")
                return cursor.fetchone()[0]
        except Exception as e:
            self.logger.error(f"Error getting user count: {e}")
            return 0
