-- Create change requests table for testing change request integration
CREATE TABLE change_requests (
    id VARCHAR(50) PRIMARY KEY,
    number VARCHAR(20) UNIQUE NOT NULL,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    priority ENUM('HIGH', 'MEDIUM', 'LOW') DEFAULT 'MEDIUM',
    status VARCHAR(50) DEFAULT 'OPEN',
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    assigned_to VARCHAR(100),
    category VARCHAR(100),
    risk_level ENUM('HIGH', 'MEDIUM', 'LOW'),
    INDEX idx_number (number),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_created_date (created_date)
);

-- Insert sample change request data for testing
INSERT INTO change_requests (id, number, title, description, priority, status, assigned_to, category, risk_level) VALUES
('cr-001', '123', 'Fix login authentication bug', 'Users unable to login with special characters in password. This affects approximately 15% of our user base who use complex passwords with special characters. The issue appears to be related to URL encoding in the authentication service.', 'HIGH', 'IN_PROGRESS', '<EMAIL>', 'Security', 'HIGH'),

('cr-002', '124', 'Add new reporting feature', 'Implement quarterly sales reporting dashboard with interactive charts and export functionality. This feature has been requested by the sales team to better track performance metrics and identify trends.', 'MEDIUM', 'OPEN', '<EMAIL>', 'Feature', 'MEDIUM'),

('cr-003', '125', 'Database performance optimization', 'Optimize slow queries in user management module. Several queries are taking over 5 seconds to execute, particularly the user search and role assignment operations. This is impacting system responsiveness.', 'HIGH', 'OPEN', '<EMAIL>', 'Performance', 'MEDIUM'),

('cr-004', '126', 'Update API documentation', 'Update API documentation for v2.0 release including new endpoints, authentication changes, and response format updates. Documentation must be completed before public release.', 'LOW', 'CLOSED', '<EMAIL>', 'Documentation', 'LOW'),

('cr-005', '127', 'Security patch deployment', 'Deploy critical security patches for authentication system to address recently discovered vulnerabilities. This includes updates to JWT token handling and session management.', 'HIGH', 'IN_PROGRESS', '<EMAIL>', 'Security', 'HIGH'),

('cr-006', '128', 'Mobile app UI improvements', 'Improve mobile application user interface based on user feedback. Focus on navigation, button sizing, and overall user experience on smaller screens.', 'MEDIUM', 'OPEN', '<EMAIL>', 'UI/UX', 'LOW'),

('cr-007', '129', 'Payment gateway integration', 'Integrate new payment gateway to support additional payment methods including digital wallets and international payment options.', 'HIGH', 'OPEN', '<EMAIL>', 'Integration', 'HIGH'),

('cr-008', '130', 'Backup system enhancement', 'Enhance automated backup system to include incremental backups and faster recovery options. Current full backups are taking too long and impacting system performance.', 'MEDIUM', 'IN_PROGRESS', '<EMAIL>', 'Infrastructure', 'MEDIUM'),

('cr-009', '131', 'User notification preferences', 'Allow users to customize notification preferences including email frequency, notification types, and delivery methods.', 'LOW', 'OPEN', '<EMAIL>', 'Feature', 'LOW'),

('cr-010', '132', 'Data migration cleanup', 'Clean up legacy data from previous system migration. Remove obsolete records and optimize database storage usage.', 'MEDIUM', 'CLOSED', '<EMAIL>', 'Maintenance', 'LOW');

-- Create additional tables that might be referenced in change requests

-- Users table for assigned_to references
CREATE TABLE users (
    id VARCHAR(50) PRIMARY KEY,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100),
    department VARCHAR(50),
    role VARCHAR(50),
    created_date DATETIME DEFAULT CURRENT_TIMESTAMP
);

INSERT INTO users (id, email, full_name, department, role) VALUES
('user-001', '<EMAIL>', 'John Doe', 'Engineering', 'Senior Developer'),
('user-002', '<EMAIL>', 'Jane Smith', 'Engineering', 'Product Manager'),
('user-003', '<EMAIL>', 'Database Admin', 'IT Operations', 'Database Administrator'),
('user-004', '<EMAIL>', 'Technical Writer', 'Documentation', 'Technical Writer'),
('user-005', '<EMAIL>', 'Security Team', 'Security', 'Security Specialist'),
('user-006', '<EMAIL>', 'UI Designer', 'Design', 'UI/UX Designer'),
('user-007', '<EMAIL>', 'Payment Developer', 'Engineering', 'Integration Specialist'),
('user-008', '<EMAIL>', 'DevOps Team', 'IT Operations', 'DevOps Engineer'),
('user-009', '<EMAIL>', 'Frontend Developer', 'Engineering', 'Frontend Developer'),
('user-010', '<EMAIL>', 'Data Analyst', 'Analytics', 'Data Analyst');

-- Change request comments/updates table
CREATE TABLE change_request_updates (
    id VARCHAR(50) PRIMARY KEY,
    change_request_id VARCHAR(50),
    update_date DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_by VARCHAR(100),
    update_type ENUM('STATUS_CHANGE', 'COMMENT', 'ASSIGNMENT', 'PRIORITY_CHANGE') DEFAULT 'COMMENT',
    old_value VARCHAR(255),
    new_value VARCHAR(255),
    comment TEXT,
    FOREIGN KEY (change_request_id) REFERENCES change_requests(id),
    INDEX idx_change_request_id (change_request_id),
    INDEX idx_update_date (update_date)
);

-- Sample updates
INSERT INTO change_request_updates (id, change_request_id, updated_by, update_type, old_value, new_value, comment) VALUES
('update-001', 'cr-001', '<EMAIL>', 'STATUS_CHANGE', 'OPEN', 'IN_PROGRESS', 'Started working on authentication bug fix'),
('update-002', 'cr-004', '<EMAIL>', 'STATUS_CHANGE', 'IN_PROGRESS', 'CLOSED', 'Documentation update completed and reviewed'),
('update-003', 'cr-005', '<EMAIL>', 'COMMENT', NULL, NULL, 'Security patches tested in staging environment, ready for production deployment'),
('update-004', 'cr-008', '<EMAIL>', 'STATUS_CHANGE', 'OPEN', 'IN_PROGRESS', 'Beginning backup system enhancement implementation');
