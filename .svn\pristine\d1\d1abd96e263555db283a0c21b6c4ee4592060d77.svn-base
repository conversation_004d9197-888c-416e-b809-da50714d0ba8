#!/bin/bash

# LDAP Test Environment Startup Script
# This script starts the LDAP test environment and runs basic connectivity tests

set -e

echo "🔐 Starting LDAP Test Environment"
echo "=================================="

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null; then
    echo "❌ docker-compose not found. Please install Docker Compose."
    exit 1
fi

# Start LDAP services
echo "🚀 Starting LDAP services..."
docker-compose up -d openldap ldap-admin

# Wait for services to be ready
echo "⏳ Waiting for LDAP services to start..."
sleep 10

# Check if services are running
echo "🔍 Checking service status..."
if docker-compose ps | grep -q "openldap.*Up"; then
    echo "✅ OpenLDAP server is running"
else
    echo "❌ OpenLDAP server failed to start"
    docker-compose logs openldap
    exit 1
fi

if docker-compose ps | grep -q "ldap-admin.*Up"; then
    echo "✅ LDAP Admin interface is running"
else
    echo "❌ LDAP Admin interface failed to start"
    docker-compose logs ldap-admin
    exit 1
fi

# Wait a bit more for LDAP to fully initialize
echo "⏳ Waiting for LDAP directory to initialize..."
sleep 15

# Run basic connectivity test
echo "🧪 Running basic connectivity tests..."
if command -v python3 &> /dev/null; then
    if python3 -c "import ldap3" 2>/dev/null; then
        echo "✅ Python ldap3 library is available"
        echo "🔧 Running LDAP test script..."
        python3 scripts/test_ldap.py --test connection
    else
        echo "⚠️  Python ldap3 library not found. Install with: pip install ldap3"
        echo "📋 Manual testing instructions:"
        echo "   - LDAP Server: ldap://localhost:1389"
        echo "   - Admin DN: cn=admin,dc=reposense,dc=local"
        echo "   - Admin Password: adminpassword"
    fi
else
    echo "⚠️  Python3 not found. Skipping automated tests."
fi

# Test with ldapsearch if available
if command -v ldapsearch &> /dev/null; then
    echo "🔍 Testing with ldapsearch..."
    if ldapsearch -x -H ldap://localhost:1389 -b "dc=reposense,dc=local" -D "cn=admin,dc=reposense,dc=local" -w adminpassword "(objectclass=*)" -LLL >/dev/null 2>&1; then
        echo "✅ LDAP search test successful"
    else
        echo "❌ LDAP search test failed"
    fi
else
    echo "⚠️  ldapsearch not found. Install with: apt-get install ldap-utils (Ubuntu/Debian)"
fi

echo ""
echo "🎉 LDAP Test Environment Ready!"
echo "================================"
echo ""
echo "📋 Access Information:"
echo "   🌐 LDAP Admin UI: http://localhost:8080"
echo "   🔗 LDAP Server: ldap://localhost:1389"
echo "   🔐 Admin Login: cn=admin,dc=reposense,dc=local / adminpassword"
echo ""
echo "👥 Test Users:"
echo "   • user01 / password1"
echo "   • user02 / password2" 
echo "   • user03 / password3"
echo ""
echo "🧪 Run Tests:"
echo "   python3 scripts/test_ldap.py --test full"
echo "   python3 scripts/test_ldap.py --username user01 --password password1"
echo ""
echo "📚 Documentation: docs/LDAP_TESTING.md"
echo ""
echo "🛑 To stop: docker-compose down"
