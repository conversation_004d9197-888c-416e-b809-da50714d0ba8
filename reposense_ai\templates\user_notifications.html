{% extends "base.html" %}

{% block title %}Notification Preferences - {{ user.username }} - RepoSense AI{% endblock %}

{% block content %}
<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">Notification Preferences</h1>
            <p class="page-subtitle">Manage notification settings for {{ user.username }} ({{ user.email }})</p>
        </div>
        <a href="{{ url_for('users_page') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Users
        </a>
    </div>
</div>

<div class="row">
    <!-- Global Notification Preferences -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-bell"></i> Global Notification Preferences</h5>
            </div>
            <div class="card-body">
                <form id="globalPreferencesForm">
                    <div class="mb-3">
                        <label class="form-label">Notification Categories</label>
                        <div class="form-check-group">
                            {% for category in notification_categories %}
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="global_{{ category.value }}"
                                    name="enabled_categories" value="{{ category.value }}" {% if category in
                                    user.notification_preferences.enabled_categories %}checked{% endif %}>
                                <label class="form-check-label" for="global_{{ category.value }}">
                                    {{ category.value.replace('_', ' ').title() }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="min_severity" class="form-label">Minimum Severity Level</label>
                        <select class="form-select" id="min_severity" name="min_severity">
                            <option value="DEBUG" {% if user.notification_preferences.min_severity=='DEBUG' %}selected{%
                                endif %}>Debug</option>
                            <option value="INFO" {% if user.notification_preferences.min_severity=='INFO' %}selected{%
                                endif %}>Info</option>
                            <option value="WARNING" {% if user.notification_preferences.min_severity=='WARNING'
                                %}selected{% endif %}>Warning</option>
                            <option value="ERROR" {% if user.notification_preferences.min_severity=='ERROR' %}selected{%
                                endif %}>Error</option>
                            <option value="CRITICAL" {% if user.notification_preferences.min_severity=='CRITICAL'
                                %}selected{% endif %}>Critical</option>
                        </select>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="email_enabled" name="email_enabled" {% if
                            user.notification_preferences.email_enabled %}checked{% endif %}>
                        <label class="form-check-label" for="email_enabled">
                            Enable email notifications
                        </label>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="email_digest" name="email_digest" {% if
                            user.notification_preferences.email_digest %}checked{% endif %}>
                        <label class="form-check-label" for="email_digest">
                            Send daily digest instead of immediate notifications
                        </label>
                    </div>

                    <div class="mb-3">
                        <label for="digest_time" class="form-label">Daily Digest Time</label>
                        <input type="time" class="form-control" id="digest_time" name="digest_time"
                            value="{{ user.notification_preferences.digest_time }}">
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Global Preferences
                    </button>
                </form>
            </div>
        </div>
    </div>

    <!-- Repository Relationships -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-code-branch"></i> Repository Relationships</h5>
            </div>
            <div class="card-body">
                <form id="repositoryRelationshipsForm">
                    {% if repositories %}
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>Repository</th>
                                    <th>Relationship</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for repo in repositories %}
                                {% set user_relationship = repo.get_user_relationship(user.id) %}
                                <tr>
                                    <td>
                                        <strong>{{ repo.name }}</strong><br>
                                        <small class="text-muted">{{ repo.url }}</small>
                                    </td>
                                    <td>
                                        <select class="form-select form-select-sm relationship-select"
                                            data-repo-id="{{ repo.id }}" name="relationship_{{ repo.id }}">
                                            <option value="none" {% if not user_relationship %}selected{% endif %}>None
                                            </option>
                                            {% for rel_type in relationship_types %}
                                            <option value="{{ rel_type.value }}" {% if user_relationship and
                                                user_relationship.relationship_type==rel_type %}selected{% endif %}>
                                                {{ rel_type.value.title() }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary configure-btn"
                                            data-repo-id="{{ repo.id }}" data-repo-name="{{ repo.name }}" {% if not
                                            user_relationship %}disabled{% endif %}>
                                            <i class="fas fa-cog"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save"></i> Save Repository Relationships
                    </button>
                    {% else %}
                    <div class="text-center py-4">
                        <i class="fas fa-code-branch fa-3x text-muted mb-3"></i>
                        <h6>No Repositories Configured</h6>
                        <p class="text-muted">Add repositories to configure user relationships.</p>
                    </div>
                    {% endif %}
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Repository Notification Configuration Modal -->
<div class="modal fade" id="repoNotificationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Repository Notification Settings</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="repoNotificationForm">
                    <input type="hidden" id="modal_repo_id" name="repo_id">

                    <div class="mb-3">
                        <label class="form-label">Notification Categories</label>
                        <div class="form-check-group">
                            {% for category in notification_categories %}
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="repo_{{ category.value }}"
                                    name="repo_enabled_categories" value="{{ category.value }}">
                                <label class="form-check-label" for="repo_{{ category.value }}">
                                    {{ category.value.replace('_', ' ').title() }}
                                </label>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="notify_on_own_commits"
                            name="notify_on_own_commits">
                        <label class="form-check-label" for="notify_on_own_commits">
                            Notify on my own commits
                        </label>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="notify_on_high_risk_only"
                            name="notify_on_high_risk_only">
                        <label class="form-check-label" for="notify_on_high_risk_only">
                            Only notify on high-risk commits
                        </label>
                    </div>

                    <div class="mb-3">
                        <label for="notify_on_specific_paths" class="form-label">Watch Specific Paths (one per
                            line)</label>
                        <textarea class="form-control" id="notify_on_specific_paths" name="notify_on_specific_paths"
                            rows="3" placeholder="src/*.py&#10;docs/*.md&#10;config/*.json"></textarea>
                        <div class="form-text">Use glob patterns like *.py, src/**, etc.</div>
                    </div>

                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="immediate_notification"
                            name="immediate_notification" checked>
                        <label class="form-check-label" for="immediate_notification">
                            Send immediate notifications
                        </label>
                    </div>

                    <div class="mb-3">
                        <label for="digest_frequency" class="form-label">Digest Frequency</label>
                        <select class="form-select" id="digest_frequency" name="digest_frequency">
                            <option value="never">Never (immediate only)</option>
                            <option value="daily">Daily</option>
                            <option value="weekly">Weekly</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveRepoNotificationBtn">Save Settings</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Global notification preferences form
    document.getElementById('globalPreferencesForm').addEventListener('submit', function (e) {
        e.preventDefault();

        const formData = new FormData(this);
        const data = {
            enabled_categories: formData.getAll('enabled_categories'),
            min_severity: formData.get('min_severity'),
            email_enabled: formData.has('email_enabled'),
            email_digest: formData.has('email_digest'),
            digest_timer: formData.get('digest_time')
        };

        fetch(`/api/users/{{ user.id }}/notification-preferences`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(data)
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Global notification preferences updated successfully', 'success');
                } else {
                    showAlert('Error: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                showAlert('Error updating preferences: ' + error, 'danger');
            });
    });

    // Repository relationships form
    document.getElementById('repositoryRelationshipsForm').addEventListener('submit', function (e) {
        e.preventDefault();

        const relationships = [];
        const selects = document.querySelectorAll('.relationship-select');
        selects.forEach(select => {
            const repoId = select.dataset.repoId;
            const relationshipType = select.value;

            relationships.push({
                repository_id: repoId,
                relationship_type: relationshipType,
                notification_preferences: getRepoNotificationPreferences(repoId)
            });
        });

        fetch(`/api/users/{{ user.id }}/repository-relationships`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ relationships: relationships })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Repository relationships updated successfully', 'success');
                    updateConfigureButtons();
                } else {
                    showAlert('Error: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                showAlert('Error updating relationships: ' + error, 'danger');
            });
    });

    // Handle relationship select changes
    document.querySelectorAll('.relationship-select').forEach(select => {
        select.addEventListener('change', function () {
            const configureBtn = this.closest('tr').querySelector('.configure-btn');
            configureBtn.disabled = this.value === 'none';
        });
    });

    // Configure button handlers
    document.querySelectorAll('.configure-btn').forEach(btn => {
        btn.addEventListener('click', function () {
            const repoId = this.dataset.repoId;
            const repoName = this.dataset.repoName;

            document.getElementById('modal_repo_id').value = repoId;
            document.querySelector('#repoNotificationModal .modal-title').textContent =
                `Notification Settings - ${repoName}`;

            // Load current settings for this repository
            loadRepoNotificationSettings(repoId);

            new bootstrap.Modal(document.getElementById('repoNotificationModal')).show();
        });
    });

    // Initialize global storage for notification preferences
    window.repoNotificationPreferences = window.repoNotificationPreferences || {};

    // Save repository notification settings
    document.getElementById('saveRepoNotificationBtn').addEventListener('click', function () {
        const repoId = document.getElementById('modal_repo_id').value;

        // Collect preferences from the modal
        const enabledCategories = [];
        document.querySelectorAll('input[name="repo_enabled_categories"]:checked').forEach(cb => {
            enabledCategories.push(cb.value);
        });

        const specificPaths = document.getElementById('notify_on_specific_paths').value
            .split('\n')
            .map(path => path.trim())
            .filter(path => path.length > 0);

        const preferences = {
            enabled_categories: enabledCategories,
            notify_on_own_commits: document.getElementById('notify_on_own_commits').checked,
            notify_on_high_risk_only: document.getElementById('notify_on_high_risk_only').checked,
            notify_on_specific_paths: specificPaths,
            notify_on_processing_failures: document.getElementById('notify_on_processing_failures') ? document.getElementById('notify_on_processing_failures').checked : true,
            notify_on_scan_completion: document.getElementById('notify_on_scan_completion') ? document.getElementById('notify_on_scan_completion').checked : false,
            immediate_notification: document.getElementById('immediate_notification').checked,
            digest_frequency: document.getElementById('digest_frequency').value
        };

        // Store preferences globally so they can be retrieved when saving relationships
        window.repoNotificationPreferences[repoId] = preferences;

        console.log('Stored notification preferences for repo', repoId, preferences);

        bootstrap.Modal.getInstance(document.getElementById('repoNotificationModal')).hide();
        showAlert('Notification settings saved. Click "Save Repository Relationships" to apply changes.', 'success');
    });

    function updateConfigureButtons() {
        document.querySelectorAll('.relationship-select').forEach(select => {
            const configureBtn = select.closest('tr').querySelector('.configure-btn');
            configureBtn.disabled = select.value === 'none';
        });
    }

    function getRepoNotificationPreferences(repoId) {
        // Check if we have stored preferences for this repository from the modal
        if (window.repoNotificationPreferences && window.repoNotificationPreferences[repoId]) {
            return window.repoNotificationPreferences[repoId];
        }

        // Fallback to defaults - the modal loading will handle loading actual preferences

        // Fallback to defaults if no existing relationship found
        return {
            enabled_categories: ['commits'],
            notify_on_own_commits: false,
            notify_on_high_risk_only: false,
            notify_on_specific_paths: [],
            notify_on_processing_failures: true,
            notify_on_scan_completion: false,
            immediate_notification: true,
            digest_frequency: 'never'
        };
    }

    function loadRepoNotificationSettings(repoId) {
        // Load and populate the modal with current settings
        fetch(`/api/users/{{ user.id }}/repositories/${repoId}/notification-preferences`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const prefs = data.notification_preferences;

                    // Clear all checkboxes first
                    document.querySelectorAll('input[name="repo_enabled_categories"]').forEach(cb => {
                        cb.checked = false;
                    });

                    // Set enabled categories
                    prefs.enabled_categories.forEach(category => {
                        const checkbox = document.getElementById(`repo_${category}`);
                        if (checkbox) checkbox.checked = true;
                    });

                    // Set other preferences
                    document.getElementById('notify_on_own_commits').checked = prefs.notify_on_own_commits;
                    document.getElementById('notify_on_high_risk_only').checked = prefs.notify_on_high_risk_only;
                    document.getElementById('notify_on_specific_paths').value = prefs.notify_on_specific_paths.join('\n');
                    document.getElementById('immediate_notification').checked = prefs.immediate_notification;
                    document.getElementById('digest_frequency').value = prefs.digest_frequency;
                } else {
                    console.error('Failed to load notification settings:', data.message);
                    showAlert('Failed to load notification settings: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error loading notification settings:', error);
                showAlert('Error loading notification settings: ' + error, 'danger');
            });
    }

    function saveRepoNotificationSettings(repoId) {
        // Collect form data
        const enabledCategories = [];
        document.querySelectorAll('input[name="repo_enabled_categories"]:checked').forEach(cb => {
            enabledCategories.push(cb.value);
        });

        const specificPaths = document.getElementById('notify_on_specific_paths').value
            .split('\n')
            .map(path => path.trim())
            .filter(path => path.length > 0);

        const notificationPreferences = {
            enabled_categories: enabledCategories,
            notify_on_own_commits: document.getElementById('notify_on_own_commits').checked,
            notify_on_high_risk_only: document.getElementById('notify_on_high_risk_only').checked,
            notify_on_specific_paths: specificPaths,
            immediate_notification: document.getElementById('immediate_notification').checked,
            digest_frequency: document.getElementById('digest_frequency').value
        };

        // Save to API
        fetch(`/api/users/{{ user.id }}/repositories/${repoId}/notification-preferences`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({ notification_preferences: notificationPreferences })
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showAlert('Repository notification settings saved successfully', 'success');
                } else {
                    showAlert('Error saving settings: ' + data.message, 'danger');
                }
            })
            .catch(error => {
                console.error('Error saving notification settings:', error);
                showAlert('Error saving notification settings: ' + error, 'danger');
            });
    }

    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

        const container = document.querySelector('.page-header');
        container.parentNode.insertBefore(alertDiv, container.nextSibling);

        setTimeout(() => {
            alertDiv.remove();
        }, 5000);
    }
</script>
{% endblock %}