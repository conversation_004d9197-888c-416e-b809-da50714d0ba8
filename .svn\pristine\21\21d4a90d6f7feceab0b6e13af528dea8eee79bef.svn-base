"""
Pytest configuration and shared fixtures for RepoSense AI unit tests.

This file makes fixtures from test_utils.py available to all test files
in the unittests directory.
"""

# Import all fixtures from test_utils to make them available to all tests
from .test_utils import (
    mock_config,
    mock_document_record,
    mock_ldap_config,
    mock_ldap_user,
    mock_ollama_client,
    temp_dir,
)

# Re-export fixtures so they're available to pytest
__all__ = [
    "mock_config",
    "mock_document_record",
    "mock_ldap_config",
    "mock_ldap_user",
    "mock_ollama_client",
    "temp_dir",
]
