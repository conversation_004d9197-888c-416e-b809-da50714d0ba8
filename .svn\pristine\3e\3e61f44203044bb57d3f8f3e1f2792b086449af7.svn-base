[pytest]
# Pytest configuration for RepoSense AI
testpaths = unittests
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Test discovery patterns
addopts = 
    --verbose
    --tb=short
    --strict-markers
    --disable-warnings
    --color=yes
    --durations=10

# Markers for test categorization
markers =
    unit: Unit tests for individual components
    integration: Integration tests for component interactions
    slow: Tests that take longer to run
    database: Tests that require database access
    network: Tests that require network access
    ai: Tests that require AI/LLM services
    processor: Tests for document processing functionality
    analyzer: Tests for diff complexity analysis functionality
    metadata: Tests for metadata extraction functionality
    document_service: Tests for document service functionality
    
# Minimum version requirements
minversion = 6.0

# Test timeout (in seconds)
timeout = 300

# Coverage settings (if pytest-cov is installed)
# addopts = --cov=. --cov-report=html --cov-report=term-missing

# Logging configuration for tests
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(name)s: %(message)s
log_cli_date_format = %Y-%m-%d %H:%M:%S

# Ignore certain warnings during tests
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning
    ignore::UserWarning:requests.*
    ignore::pytest.PytestDeprecationWarning

# Asyncio configuration
asyncio_default_fixture_loop_scope = function
