#!/usr/bin/env python3
"""
Ollama client for AI-powered content generation
Handles documentation and email content generation using Ollama API
"""

import logging
from typing import Optional, Tuple

import requests
from models import CommitInfo, Config


class OllamaClient:
    """Client for Ollama API interactions"""

    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)

    def call_ollama(
        self, prompt: str, system_prompt: str = "", model: Optional[str] = None
    ) -> str:
        """Call Ollama API to generate content with optional model override"""
        try:
            # Use specified model or fall back to default
            selected_model = model or self.config.ollama_model

            # Check if model is available first
            if not self.is_model_available(selected_model):
                available_models = self.get_available_models()
                if available_models:
                    self.logger.warning(
                        f"Model '{selected_model}' not available. Available models: {available_models}"
                    )
                    return f"Error: Model '{selected_model}' not available on server. Available models: {', '.join(available_models)}"
                else:
                    self.logger.error(
                        f"No models available on Ollama server {self.config.ollama_host}"
                    )
                    return f"Error: No models available on Ollama server {self.config.ollama_host}"

            url = f"{self.config.ollama_host}/api/generate"

            payload = {
                "model": selected_model,
                "prompt": prompt,
                "system": system_prompt,
                "stream": False,
                "options": self._get_optimized_parameters(prompt, system_prompt),
            }

            # Log model selection for visibility
            if model and model != self.config.ollama_model:
                self.logger.info(
                    f"🎯 Using specialized model: {selected_model} (requested: {model}, default: {self.config.ollama_model})"
                )
            else:
                self.logger.debug(f"Using default model: {selected_model}")

            self.logger.debug(
                f"Calling Ollama API at {url} with model {selected_model}"
            )

            # Adjust timeout based on model size and complexity
            # Large models (20B+) need more time, especially for complex prompts
            timeout = self._get_timeout_for_model(selected_model)
            self.logger.debug(
                f"Using timeout of {timeout} seconds for model {selected_model}"
            )

            response = requests.post(url, json=payload, timeout=timeout)
            response.raise_for_status()

            result = response.json()
            return result.get("response", "")

        except requests.exceptions.Timeout as e:
            error_msg = f"Error: Ollama API timeout after {timeout} seconds with model '{selected_model}'. The model may be too large or the server may be overloaded. Try using a smaller/faster model or increasing timeout settings."
            self.logger.error(error_msg)
            return error_msg
        except requests.exceptions.ConnectionError as e:
            error_msg = f"Error: Cannot connect to Ollama server at {self.config.ollama_host}. Please verify that Ollama is running and accessible."
            self.logger.error(error_msg)
            return error_msg
        except requests.exceptions.HTTPError as e:
            if e.response.status_code == 404:
                error_msg = f"Error: Model '{selected_model}' not found on Ollama server. Please check model name and availability."
            elif e.response.status_code == 500:
                error_msg = f"Error: Ollama server internal error with model '{selected_model}'. The model may be corrupted or incompatible."
            else:
                error_msg = f"Error: Ollama API HTTP error {e.response.status_code} with model '{selected_model}': {e}"
            self.logger.error(error_msg)
            return error_msg
        except requests.exceptions.RequestException as e:
            error_msg = (
                f"Error: Ollama API request failed with model '{selected_model}': {e}"
            )
            self.logger.error(error_msg)
            return error_msg
        except Exception as e:
            error_msg = f"Error: Unexpected error calling Ollama with model '{selected_model}': {e}"
            self.logger.error(error_msg)
            return error_msg

    def _get_optimized_parameters(self, prompt: str, system_prompt: str) -> dict:
        """Get optimized LLM parameters based on the task type"""
        combined_text = (prompt + system_prompt).lower()

        # Detect different task types based on content analysis
        is_risk_assessment = any(
            keyword in combined_text
            for keyword in [
                "risk_level",
                "risk assessment",
                "critical",
                "security",
                "vulnerability",
                "priority",
                "confidence",
                "reasoning",
            ]
        )

        is_documentation_generation = any(
            keyword in combined_text
            for keyword in [
                "generate documentation",
                "technical documentation",
                "markdown format",
                "comprehensive documentation",
                "code changes",
                "commit documentation",
            ]
        )

        is_email_generation = any(
            keyword in combined_text
            for keyword in [
                "email subject",
                "email body",
                "email notification",
                "professional email",
                "stakeholders",
                "business impact",
            ]
        )

        is_structured_output = any(
            keyword in combined_text
            for keyword in [
                "json format",
                "respond in json",
                '{"',
                "structure:",
                "exactly:",
                "must be:",
            ]
        )

        is_content_scoring = any(
            keyword in combined_text
            for keyword in [
                "rate the relevance",
                "score",
                "relevance",
                "batch scoring",
                "section",
            ]
        )

        is_documentation_suggestions = any(
            keyword in combined_text
            for keyword in [
                "documentation suggestions",
                "improve documentation",
                "documentation input",
                "user documentation",
                "suggestions for",
            ]
        )

        # Apply task-specific parameters
        if is_risk_assessment or is_structured_output:
            # Conservative parameters for consistent, structured analysis
            return {
                "temperature": 0.1,  # Very low for consistency and accuracy
                "top_p": 0.8,  # Focused sampling for reliability
                "top_k": 20,  # Limited vocabulary for consistency
                "repeat_penalty": 1.1,  # Slight penalty to avoid repetition
                "num_predict": 2048,  # Reasonable response length for analysis
            }
        elif is_documentation_generation:
            # Balanced parameters for comprehensive, readable documentation
            return {
                "temperature": 0.4,  # Low-medium for consistency with some creativity
                "top_p": 0.9,  # Good sampling for varied expression
                "top_k": 50,  # Broader vocabulary for technical writing
                "repeat_penalty": 1.05,  # Light penalty for natural flow
                "num_predict": 4096,  # Longer responses for detailed documentation
            }
        elif is_email_generation:
            # Parameters for professional, concise communication
            return {
                "temperature": 0.3,  # Low for professional consistency
                "top_p": 0.85,  # Focused but not too narrow
                "top_k": 40,  # Good vocabulary for business communication
                "repeat_penalty": 1.1,  # Standard penalty
                "num_predict": 1024,  # Shorter responses for concise emails
            }
        elif is_content_scoring:
            # Very conservative for consistent scoring
            return {
                "temperature": 0.05,  # Extremely low for consistent scoring
                "top_p": 0.7,  # Very focused sampling
                "top_k": 15,  # Limited vocabulary for scoring consistency
                "repeat_penalty": 1.0,  # No penalty for scoring tasks
                "num_predict": 512,  # Short responses for scores
            }
        elif is_documentation_suggestions:
            # Creative but focused parameters for helpful suggestions
            return {
                "temperature": 0.6,  # Medium creativity for useful suggestions
                "top_p": 0.9,  # Good sampling diversity
                "top_k": 60,  # Broader vocabulary for suggestions
                "repeat_penalty": 1.05,  # Light penalty
                "num_predict": 2048,  # Good length for suggestions
            }
        else:
            # Default balanced parameters for general content generation
            return {
                "temperature": 0.5,  # Balanced creativity/consistency
                "top_p": 0.9,  # Good sampling diversity
                "top_k": 40,  # Standard vocabulary
                "repeat_penalty": 1.1,  # Standard repetition penalty
                "num_predict": 3072,  # Good length for general content
            }

    def test_connection(self, timeout: Optional[int] = None) -> bool:
        """Test connection to Ollama server (not model-specific)"""
        try:
            if timeout is None:
                timeout = getattr(self.config, "ollama_timeout_connection", 30)

            url = f"{self.config.ollama_host}/api/tags"
            self.logger.debug(
                f"Testing Ollama connection to {url} with timeout {timeout}s"
            )
            response = requests.get(url, timeout=timeout)
            success = response.status_code == 200
            self.logger.debug(
                f"Ollama connection test result: {success} (status: {response.status_code})"
            )
            return success
        except Exception as e:
            self.logger.debug(f"Ollama connection test failed: {e}")
            return False

    def get_available_models(self, timeout: Optional[int] = None) -> list:
        """Get list of available models from Ollama server"""
        try:
            if timeout is None:
                timeout = getattr(self.config, "ollama_timeout_connection", 30)

            url = f"{self.config.ollama_host}/api/tags"
            response = requests.get(url, timeout=timeout)
            response.raise_for_status()

            data = response.json()
            models = [model["name"] for model in data.get("models", [])]
            self.logger.debug(f"Available models: {models}")
            return models
        except Exception as e:
            self.logger.debug(f"Failed to get available models: {e}")
            return []

    def is_model_available(self, model_name: str | None = None) -> bool:
        """Check if a specific model is available"""
        if model_name is None:
            model_name = self.config.ollama_model

        available_models = self.get_available_models()
        return model_name in available_models

    def _get_timeout_for_model(self, model_name: str) -> int:
        """Get appropriate timeout based on model size and type"""
        # Get base timeout from config, with fallback
        base_timeout = getattr(self.config, "ollama_timeout_base", 180)

        # Adjust based on model characteristics
        model_lower = model_name.lower()

        # Very large models (20B+)
        if any(size in model_lower for size in ["20b", "33b", "70b", "180b"]):
            return 300  # 5 minutes

        # Large models (7B-13B)
        elif any(size in model_lower for size in ["7b", "8b", "12b", "13b", "14b"]):
            return 240  # 4 minutes

        # Medium models (3B-6B)
        elif any(size in model_lower for size in ["3b", "4b", "6b"]):
            return 180  # 3 minutes

        # Small models (1B-2B)
        elif any(size in model_lower for size in ["1b", "2b", "1.7b"]):
            return 120  # 2 minutes

        # Very small models (under 1B)
        elif any(size in model_lower for size in ["135m", "350m", "500m"]):
            return 60  # 1 minute

        # Special cases for known slow models
        if "deepseek" in model_lower or "codestral" in model_lower:
            return base_timeout + 60  # Add extra time for complex models

        # Default for unknown models
        return base_timeout

    def generate_documentation(
        self, commit: CommitInfo, model_override: Optional[str] = None
    ) -> str:
        """Generate documentation for a commit using Ollama with change request context"""

        # Build change request context section
        change_request_context = ""
        if commit.change_requests:
            self.logger.debug(
                f"Building change request context for {len(commit.change_requests)} change requests"
            )
            change_request_context = "\n\nChange Request Context:\n"
            for cr in commit.change_requests:
                self.logger.debug(f"Adding CR #{cr.number}: {cr.title}")
                change_request_context += f"""
- CR #{cr.number}: {cr.title}
  Priority: {cr.priority} | Status: {cr.status} | Risk Level: {cr.risk_level or "Unknown"}
  Description: {cr.description[:200]}{"..." if len(cr.description) > 200 else ""}
  Assigned to: {cr.assigned_to or "Unassigned"}
  Category: {cr.category or "N/A"}
"""
        else:
            self.logger.debug(
                f"No change requests found for commit {commit.revision} - using fallback documentation generation"
            )

        if commit.change_requests:
            system_prompt = """You are a technical documentation generator and code review advisor with access to change request information.
            Your task is to create comprehensive documentation that correlates code changes with their business context.

            Use the change request information to:
            1. Explain the business rationale for changes
            2. Validate that code changes match the change request scope exactly
            3. Identify any scope creep or missing implementations
            4. Assess alignment between actual implementation and requirements
            5. Evaluate risk based on change request priority and category
            6. Provide context-aware code review recommendations

            Focus on:
            1. What changes were made
            2. Why the changes were made (based on commit message and change requests)
            3. Business impact and alignment with change request requirements
            4. Technical implementation details
            5. Risk assessment considering change request context
            6. Code review recommendations
            7. Documentation impact assessment

            Write in markdown format with appropriate headers and formatting."""
        else:
            system_prompt = """You are a technical documentation generator and code review advisor.
            Your task is to create comprehensive documentation that analyzes code changes and their technical impact.

            Focus on:
            1. What changes were made
            2. Why the changes were made (based on commit message and code analysis)
            3. Technical implementation details
            4. General business impact assessment
            5. Risk assessment based on code complexity and scope
            6. Code review recommendations
            7. Documentation impact assessment

            Write in markdown format with appropriate headers and formatting."""

        # Define change request analysis prompt based on availability
        if commit.change_requests:
            change_request_analysis_prompt = """CRITICAL: Analyze how the code changes align with the change request requirements listed above. Be STRICT in your alignment assessment.

        ALIGNMENT VALIDATION CHECKLIST:
        - Do the actual code changes match the scope described in the change request?
        - Are all change request requirements addressed by the implementation?
        - Are there any code changes that go beyond the change request scope (scope creep)?
        - Are there any missing implementations that the change request requires?
        - Does the technical approach align with the change request category and priority?

        ALIGNMENT RATING GUIDELINES:
        - FULLY_ALIGNED: Implementation directly addresses the change request requirements with no significant deviations
        - PARTIALLY_ALIGNED: Implementation addresses some requirements but has missing features or minor scope deviations
        - MISALIGNED: Implementation does not address the change request requirements OR implements completely different functionality

        EXAMPLES OF MISALIGNMENT:
        - Change request asks for "reporting dashboard" but implementation creates a "trading bot"
        - Change request asks for "user authentication" but implementation creates a "game engine"
        - Change request asks for "database optimization" but implementation creates a "web scraper"

        Rate the alignment as: FULLY_ALIGNED, PARTIALLY_ALIGNED, or MISALIGNED and provide detailed reasoning."""
            business_impact_prompt = """Assess the business impact considering the change request priority and business context provided above. Specifically evaluate:
        - Does the implementation deliver the expected business value described in the change request?
        - Are there any business risks introduced by scope changes or missing requirements?
        - How does the actual implementation impact the change request timeline and deliverables?"""
        else:
            change_request_analysis_prompt = "No change request information available for this commit. Focus on technical analysis and general business impact."
            business_impact_prompt = "Assess the general business impact of this technical change based on the code modifications and commit message."

        prompt = f"""
        Generate comprehensive documentation and development process feedback for the following commit:

        Revision: {commit.revision}
        Author: {commit.author}
        Date: {commit.date}
        Message: {commit.message}

        {change_request_context}

        Changed files:
        {chr(10).join(commit.changed_paths)}

        Diff:
        {commit.diff}

        Please provide your analysis in the following format. Replace each section with actual analysis - do not include the placeholder text in square brackets:

        ## Commit Summary
        Provide a brief summary that correlates the code changes with any change request context available.

        ## Change Request Analysis
        {change_request_analysis_prompt}

        ## Technical Details
        Provide detailed technical analysis of what was changed, how it was implemented, and the technical approach used.

        ## Business Impact Assessment
        {business_impact_prompt}

        ## Risk Assessment
        {"Analyze how the code complexity aligns with the change request risk levels and priorities. Reference the specific risk levels and priorities from the change requests above." if commit.change_requests else "Evaluate the risk level based on code complexity and scope of changes. Consider areas affected, potential for introducing issues, and impact on system stability."}

        ## Code Review Recommendation
        Start with a clear decision: either "Yes, this commit should undergo a code review..." or "No, this commit does not require a code review..." Then provide detailed reasoning considering:
        - Complexity of changes
        - Risk level (high/medium/low) including change request priority
        - Areas affected (UI, backend, configuration, etc.)
        - Potential for introducing bugs
        - Security implications
        - Change request category and business impact
        - Alignment with change request requirements and scope validation results
        - Any identified scope creep or missing implementations that need review

        ## Documentation Impact
        Start with a clear decision: either "Yes, documentation updates are needed..." or "No, documentation updates are not required..." Then provide detailed reasoning considering:
        - Are user-facing features changed?
        - Are APIs or interfaces modified?
        - Are configuration options added/changed?
        - Are deployment procedures affected?
        - Should README, setup guides, or other docs be updated?

        ## Recommendations
        Provide any additional recommendations for follow-up actions, testing, or monitoring that should be considered for this change.

        ## Additional Analysis
        Provide any additional technical analysis or insights that would be helpful for understanding the changes and their implications.
        """

        try:
            self.logger.info(f"Generating documentation for revision {commit.revision}")

            # Validate input data
            if not commit:
                error_msg = "Error: Cannot generate documentation - commit information is missing"
                self.logger.error(error_msg)
                return error_msg

            if not commit.revision:
                error_msg = f"Error: Cannot generate documentation - revision number is missing for commit by {commit.author or 'unknown author'}"
                self.logger.error(error_msg)
                return error_msg

            if not commit.changed_paths:
                error_msg = f"Error: Cannot generate documentation for revision {commit.revision} - no changed files detected. This may indicate a repository access issue or an empty commit."
                self.logger.warning(error_msg)
                return error_msg

            # Test Ollama connection before attempting generation
            if not self.test_connection():
                error_msg = f"Error: Cannot generate documentation for revision {commit.revision} - Ollama service is not available. Please check that Ollama is running and accessible at {self.config.ollama_host}."
                self.logger.error(error_msg)
                return error_msg

            # Generate the main documentation using model override, specialized model, or default
            doc_model: Optional[str]
            if model_override:
                doc_model = model_override
                self.logger.info(
                    f"🎯 Using model override for documentation: {doc_model}"
                )
            else:
                doc_model = getattr(self.config, "ollama_model_documentation", None)
                if doc_model:
                    self.logger.info(
                        f"🎯 Using specialized documentation model: {doc_model}"
                    )
                else:
                    self.logger.debug(
                        f"No specialized documentation model configured, using default: {self.config.ollama_model}"
                    )

            self.logger.info(
                f"RESCAN_STEP: Calling call_ollama for documentation with model={doc_model}"
            )
            documentation = self.call_ollama(prompt, system_prompt, model=doc_model)
            self.logger.info(
                f"RESCAN_STEP: Documentation text length={len(documentation) if documentation else 0}"
            )

            # Check if generation was successful
            if not documentation:
                # Provide more specific context about potential causes
                context_info = []
                if hasattr(commit, "changed_paths") and commit.changed_paths:
                    file_count = len(commit.changed_paths)
                    context_info.append(f"{file_count} files changed")

                    # Check for different types of files that might cause issues
                    truly_binary_extensions = {
                        ".exe",
                        ".dll",
                        ".so",
                        ".dylib",
                        ".bin",
                        ".dat",
                        ".img",
                        ".iso",
                        ".jpg",
                        ".jpeg",
                        ".png",
                        ".gif",
                        ".bmp",
                        ".ico",
                        ".tiff",
                        ".webp",
                        ".mp3",
                        ".mp4",
                        ".avi",
                        ".mov",
                        ".wav",
                        ".flac",
                        ".ogg",
                    }

                    structured_doc_extensions = {
                        ".docx",
                        ".xlsx",
                        ".pptx",
                        ".doc",
                        ".xls",
                        ".ppt",
                        ".pdf",
                        ".odt",
                        ".ods",
                        ".odp",
                        ".rtf",
                    }

                    archive_extensions = {
                        ".zip",
                        ".tar",
                        ".gz",
                        ".rar",
                        ".7z",
                        ".bz2",
                        ".xz",
                    }

                    database_extensions = {
                        ".db",
                        ".sqlite",
                        ".sqlite3",
                        ".mdb",
                        ".accdb",
                    }

                    binary_files = [
                        f
                        for f in commit.changed_paths
                        if any(
                            f.lower().endswith(ext) for ext in truly_binary_extensions
                        )
                    ]
                    structured_docs = [
                        f
                        for f in commit.changed_paths
                        if any(
                            f.lower().endswith(ext) for ext in structured_doc_extensions
                        )
                    ]
                    archives = [
                        f
                        for f in commit.changed_paths
                        if any(f.lower().endswith(ext) for ext in archive_extensions)
                    ]
                    databases = [
                        f
                        for f in commit.changed_paths
                        if any(f.lower().endswith(ext) for ext in database_extensions)
                    ]

                    if binary_files:
                        context_info.append(
                            f"{len(binary_files)} binary files detected"
                        )
                    if structured_docs:
                        context_info.append(
                            f"{len(structured_docs)} structured documents (Office/PDF files)"
                        )
                    if archives:
                        context_info.append(f"{len(archives)} archive files")
                    if databases:
                        context_info.append(f"{len(databases)} database files")

                context_str = f" ({', '.join(context_info)})" if context_info else ""

                error_msg = f"Error: Documentation generation failed for revision {commit.revision}{context_str} - Ollama returned empty response. Possible causes: 1) Model overloaded/unavailable, 2) Context limit exceeded due to large diffs, 3) Binary files causing encoding issues, 4) Network connectivity problems. Check Ollama service status and consider using a smaller model or reducing diff size."
                self.logger.error(error_msg)
                return error_msg

            # More sophisticated error detection with context
            doc_lower = documentation.lower().strip()
            doc_start = documentation.strip()[:100]

            # Check for various error patterns
            is_error = (
                doc_start.startswith("Error:")
                or doc_start.startswith("I cannot")
                or doc_start.startswith("I'm unable")
                or (
                    doc_lower.startswith("error")
                    and not doc_lower.startswith("error handling")
                )
                or ("failed" in doc_lower[:100] and "error" in doc_lower[:100])
            )

            if is_error:
                # Analyze the response to provide better context
                context_clues = []

                if (
                    "utf-8" in doc_lower
                    or "encoding" in doc_lower
                    or "decode" in doc_lower
                ):
                    context_clues.append(
                        "encoding/UTF-8 issues detected - likely binary files in diff"
                    )
                if (
                    "context" in doc_lower
                    or "limit" in doc_lower
                    or "too large" in doc_lower
                ):
                    context_clues.append(
                        "context limit exceeded - diff too large for model"
                    )
                if (
                    "connection" in doc_lower
                    or "timeout" in doc_lower
                    or "network" in doc_lower
                ):
                    context_clues.append(
                        "network/connection issues with Ollama service"
                    )
                if len(documentation.strip()) < 50:
                    context_clues.append("truncated response - possible model overload")

                # Check if this looks like a partial response that got cut off
                if (
                    doc_start.startswith("## Summary")
                    or doc_start.startswith("# ")
                    or doc_start.startswith("**")
                    or doc_start.startswith("*")
                ):
                    context_clues.append(
                        "response appears to be markdown that was truncated - not actually an error"
                    )
                    # Don't treat this as an error, just log a warning
                    self.logger.warning(
                        f"AI response for revision {commit.revision} appears truncated but valid: '{doc_start}...'"
                    )
                    # Continue processing instead of returning error
                else:
                    context_str = (
                        f" Context: {', '.join(context_clues)}" if context_clues else ""
                    )
                    error_msg = f"Error: Documentation generation failed for revision {commit.revision} - AI model returned error: {documentation[:300]}...{context_str}"
                    self.logger.error(error_msg)
                    return error_msg

            # Check for incomplete responses (common with encoding issues)
            if (
                len(documentation.strip()) > 10
                and not any(
                    documentation.strip().endswith(end)
                    for end in [".", "!", "?", "```", "**", "*"]
                )
                and len(documentation.strip()) < 100
            ):
                self.logger.warning(
                    f"AI response may be incomplete for revision {commit.revision}: '{documentation[:100]}...'"
                )
                # Don't fail completely, but log the warning

            if len(documentation.strip()) < 50:
                error_msg = f"Error: Documentation generation failed for revision {commit.revision} - AI response too short ({len(documentation)} chars). This may indicate a model issue or insufficient context."
                self.logger.warning(error_msg)
                return error_msg

            # Fix any malformed tables in the generated documentation
            try:
                documentation = self._fix_ai_generated_tables(documentation)
            except Exception as e:
                self.logger.warning(
                    f"Table fixing failed for revision {commit.revision}: {e}"
                )
                # Continue with unfixed tables rather than failing completely

            # Add heuristic analysis section
            try:
                documentation_with_heuristics = self._add_heuristic_analysis_section(
                    documentation, commit
                )
                return documentation_with_heuristics
            except Exception as e:
                self.logger.warning(
                    f"Heuristic analysis addition failed for revision {commit.revision}: {e}"
                )
                # Return documentation without heuristics rather than failing completely
                return documentation

        except Exception as e:
            # Provide detailed context about the error
            error_context = self._analyze_error_context(e, commit)
            error_msg = f"Error: Critical failure generating documentation for revision {commit.revision if commit else 'unknown'} - {str(e)}. {error_context}"
            self.logger.error(error_msg)
            return error_msg

    def _analyze_error_context(self, error: Exception, commit=None) -> str:
        """Analyze an error and provide helpful context about potential causes"""
        error_str = str(error).lower()
        context_parts = []

        # UTF-8/Encoding errors
        if any(
            term in error_str
            for term in ["utf-8", "codec", "decode", "encoding", "invalid start byte"]
        ):
            context_parts.append(
                "ENCODING ISSUE: Binary files or non-UTF-8 content detected in repository"
            )
            if commit and hasattr(commit, "changed_paths"):
                # Categorize files more specifically
                truly_binary = [
                    f
                    for f in commit.changed_paths
                    if any(
                        f.lower().endswith(ext)
                        for ext in {
                            ".exe",
                            ".dll",
                            ".so",
                            ".bin",
                            ".jpg",
                            ".png",
                            ".mp3",
                            ".mp4",
                        }
                    )
                ]
                structured_docs = [
                    f
                    for f in commit.changed_paths
                    if any(
                        f.lower().endswith(ext)
                        for ext in {".docx", ".xlsx", ".pptx", ".pdf", ".odt"}
                    )
                ]

                if truly_binary:
                    context_parts.append(
                        f"Binary files: {', '.join(truly_binary[:3])}{'...' if len(truly_binary) > 3 else ''}"
                    )
                if structured_docs:
                    context_parts.append(
                        f"Structured documents: {', '.join(structured_docs[:3])}{'...' if len(structured_docs) > 3 else ''}"
                    )
                    context_parts.append(
                        "SOLUTION: Install document processing libraries (pip install python-docx openpyxl python-pptx pdfplumber) to extract text from Office files for better diff analysis."
                    )
                else:
                    context_parts.append(
                        "SOLUTION: Repository contains binary files causing encoding issues. Consider excluding binary file types from diff analysis."
                    )

        # Connection/Network errors
        elif any(
            term in error_str
            for term in ["connection", "timeout", "network", "refused", "unreachable"]
        ):
            context_parts.append("CONNECTIVITY ISSUE: Cannot reach Ollama service")
            context_parts.append(
                f"SOLUTION: Check if Ollama is running at {self.config.ollama_host}, verify network connectivity, and ensure firewall allows access."
            )

        # Model/Memory errors
        elif any(
            term in error_str
            for term in ["model", "memory", "context", "limit", "overload"]
        ):
            context_parts.append(
                "MODEL/MEMORY ISSUE: AI model may be overloaded or context limit exceeded"
            )
            if commit and hasattr(commit, "changed_paths"):
                file_count = len(commit.changed_paths)
                if file_count > 20:
                    context_parts.append(
                        f"Large commit detected: {file_count} files changed"
                    )
            context_parts.append(
                "SOLUTION: Try using a smaller model, reduce diff size, or wait for model to become available."
            )

        # Generic error
        else:
            context_parts.append("GENERAL ERROR: Unexpected issue during AI processing")
            context_parts.append(
                "SOLUTION: Check Ollama service logs, verify model availability, and ensure sufficient system resources."
            )

        return " | ".join(context_parts)

    def _add_heuristic_analysis_section(
        self, documentation: str, commit: CommitInfo
    ) -> str:
        """Add heuristic analysis section to the generated documentation"""
        try:
            # Import here to avoid circular imports
            from config_manager import ConfigManager
            from metadata_extractor import MetadataExtractor

            # Initialize metadata extractor for heuristic analysis
            config_manager = ConfigManager() if hasattr(self, "config") else None
            extractor = MetadataExtractor(
                ollama_client=self, config_manager=config_manager
            )

            # Create a temporary document record for heuristic analysis using the proper DocumentRecord class
            from datetime import datetime

            from document_database import DocumentRecord

            # Convert revision to int, fallback to 0 if not numeric
            try:
                revision_int = int(commit.revision)
            except (ValueError, TypeError):
                revision_int = 0  # Fallback for non-numeric revisions

            temp_doc_record = DocumentRecord(
                id=f"temp_{commit.revision}",
                repository_id=getattr(commit, "repository_id", "unknown_repo"),
                repository_name=getattr(
                    commit, "repository_id", "unknown_repo"
                ),  # Use repository_id as name fallback
                revision=revision_int,
                date=getattr(commit, "date", datetime.now()),
                filename=commit.changed_paths[0] if commit.changed_paths else "unknown",
                filepath=f"/tmp/temp_{commit.revision}.md",  # Temporary path
                size=len(documentation),
                author=getattr(commit, "author", "unknown"),
                commit_message=commit.message,
                changed_paths=commit.changed_paths,
                repository_type="svn",  # Default assumption
            )

            # Gather heuristic context
            heuristic_context = extractor._gather_heuristic_context(
                documentation, temp_doc_record
            )

            # Build change request summary section if change requests exist
            cr_summary_section = ""
            if commit.change_requests:
                config = config_manager.load_config() if config_manager else None
                cr_summary_section = self._format_change_request_summary_section(
                    commit.change_requests, config
                )

            # Build heuristic analysis section
            heuristic_section = self._format_heuristic_analysis_section(
                heuristic_context, commit
            )

            # Check if both sections already exist (prevent duplication)
            has_heuristic = "## Heuristic Analysis" in documentation
            has_cr_summary = "## Change Request Summary" in documentation

            if has_heuristic and (not commit.change_requests or has_cr_summary):
                self.logger.debug(
                    "Heuristic Analysis section already exists and no change request summary needed, skipping addition"
                )
                return documentation

            # Insert sections in the right order
            sections_to_add = []
            if cr_summary_section:
                sections_to_add.append(cr_summary_section)
            if (
                not has_heuristic
            ):  # Only add heuristic section if it doesn't already exist
                sections_to_add.append(heuristic_section)

            # Insert change request summary right after the main Commit Summary section
            if cr_summary_section and "## Commit Summary" in documentation:
                # Split at the first section after Commit Summary
                lines = documentation.split("\n")
                summary_end = -1

                for i, line in enumerate(lines):
                    if (
                        line.startswith("## ")
                        and "Commit Summary" not in line
                        and summary_end == -1
                    ):
                        # Find the first section after Commit Summary
                        summary_end = i
                        break

                if summary_end > 0:
                    # Insert change request summary after Commit Summary section
                    before_lines = lines[:summary_end]
                    after_lines = lines[summary_end:]

                    # Add change request summary
                    result_lines = (
                        before_lines + ["", cr_summary_section, ""] + after_lines
                    )
                    documentation = "\n".join(result_lines)

                    # Remove change request summary from sections_to_add since we already added it
                    sections_to_add = [
                        s for s in sections_to_add if s != cr_summary_section
                    ]

            # Insert remaining sections before the Recommendations section if it exists
            if sections_to_add:
                if "## Recommendations" in documentation:
                    parts = documentation.split("## Recommendations")
                    if len(parts) == 2:
                        # Ensure proper spacing
                        before_part = parts[0].rstrip()
                        after_part = parts[1]
                        sections_text = "\n\n".join(sections_to_add)
                        return f"{before_part}\n\n{sections_text}\n\n## Recommendations{after_part}"

                # If no Recommendations section, append at the end with proper spacing
                documentation = documentation.rstrip()
                sections_text = "\n\n".join(sections_to_add)
                return f"{documentation}\n\n{sections_text}"

            return documentation

        except Exception as e:
            self.logger.warning(f"Failed to add heuristic analysis section: {e}")
            return documentation

    def _format_heuristic_analysis_section(
        self, heuristic_context: dict, commit: CommitInfo
    ) -> str:
        """Format the heuristic analysis into a readable section"""
        section_parts = ["## Heuristic Analysis"]
        section_parts.append("")
        section_parts.append(
            "*This section shows the automated heuristic analysis that provides context indicators and preliminary assessments to help understand the AI's decision-making process.*"
        )
        section_parts.append("")

        # Add context indicators
        indicators = heuristic_context.get("indicators", {})
        if indicators:
            section_parts.append("### Context Indicators")
            section_parts.append("")

            # Complexity assessment
            complexity = indicators.get("complexity", "Unknown")
            section_parts.append(f"- **Complexity Assessment:** {complexity}")

            # Risk indicators
            risk_keywords = indicators.get("risk_keywords", [])
            if risk_keywords:
                section_parts.append(
                    f"- **Risk Keywords Detected:** {', '.join(risk_keywords)}"
                )
                section_parts.append(
                    f"- **Risk Assessment:** {indicators.get('risk_assessment', 'Unknown')}"
                )
            else:
                section_parts.append(
                    "- **Risk Assessment:** LOW - no high-risk keywords detected"
                )

            # Documentation indicators
            doc_keywords = indicators.get("doc_keywords", [])
            if doc_keywords:
                section_parts.append(
                    f"- **Documentation Keywords Detected:** {', '.join(doc_keywords)}"
                )
                section_parts.append(
                    f"- **Documentation Assessment:** {indicators.get('doc_assessment', 'Unknown')}"
                )
            else:
                section_parts.append(
                    "- **Documentation Assessment:** UNLIKELY - no documentation-related keywords detected"
                )

            # File type analysis
            file_type = indicators.get("file_type", "Unknown")
            section_parts.append(f"- **File Type Analysis:** {file_type}")

            section_parts.append("")

        # Add preliminary decisions
        decisions = heuristic_context.get("decisions", {})
        if decisions:
            section_parts.append("### Preliminary Heuristic Decisions")
            section_parts.append("")

            code_review = decisions.get("code_review_recommended")
            if code_review is not None:
                review_text = "✅ Recommended" if code_review else "❌ Not Required"
                section_parts.append(f"- **Code Review:** {review_text}")

                priority = decisions.get("code_review_priority")
                if priority:
                    section_parts.append(f"- **Review Priority:** {priority}")

            doc_impact = decisions.get("documentation_impact")
            if doc_impact is not None:
                impact_text = (
                    "📝 Updates Needed" if doc_impact else "✅ No Updates Required"
                )
                section_parts.append(f"- **Documentation Impact:** {impact_text}")

            risk_level = decisions.get("risk_level")
            if risk_level:
                risk_icon = {"HIGH": "🔴", "MEDIUM": "🟡", "LOW": "🟢"}.get(
                    risk_level, "⚪"
                )
                section_parts.append(f"- **Risk Level:** {risk_icon} {risk_level}")

            section_parts.append("")

        # Add reasoning
        reasoning = heuristic_context.get("reasoning", [])
        if reasoning:
            section_parts.append("### Heuristic Reasoning")
            section_parts.append("")
            for reason in reasoning:
                section_parts.append(f"- {reason}")
            section_parts.append("")

        # Add metadata about the analysis
        section_parts.append("### Analysis Metadata")
        section_parts.append("")
        section_parts.append(
            f"- **Files Analyzed:** {len(commit.changed_paths)} file(s)"
        )
        if commit.changed_paths:
            section_parts.append(f"- **Primary File:** {commit.changed_paths[0]}")
        section_parts.append(
            f"- **Commit Message Length:** {len(commit.message)} characters"
        )
        section_parts.append(f"- **Diff Size:** {len(commit.diff)} characters")

        return "\n".join(section_parts)

    def _format_change_request_summary_section(
        self, change_requests, config=None
    ) -> str:
        """Format change request summary section using configurable display settings"""
        if not change_requests:
            return ""

        try:
            # Get display configuration - config should be passed in from caller
            if not config or not config.sql_config.enabled:
                return ""

            display_config = config.sql_config.summary_display_config
            if not display_config.get("enabled", True):
                return ""

            # Build the summary section
            section_title = display_config.get(
                "section_title", "Change Request Summary"
            )
            section_parts = [f"## {section_title}", ""]

            for cr in change_requests:
                section_parts.append(f"### CR #{cr.number}")
                section_parts.append("")

                # Get configured display fields and labels
                display_fields = display_config.get(
                    "display_fields",
                    [
                        "title",
                        "priority",
                        "status",
                        "risk_level",
                        "category",
                        "assigned_to",
                        "created_date",
                        "description",
                    ],
                )
                field_labels = display_config.get("field_labels", {})
                show_empty = display_config.get("show_empty_fields", False)
                desc_max_length = display_config.get("description_max_length", 300)
                date_format = display_config.get("date_format", "%Y-%m-%d")

                # Format each configured field
                for field in display_fields:
                    if field == "number":
                        continue  # Already shown in header

                    # Get field value using configurable field mapping
                    field_mapping = config.sql_config.field_mappings
                    actual_field_name = field

                    # Map common fields to their configured database field names
                    if field == "priority":
                        actual_field_name = field_mapping.get(
                            "priority_field", "priority"
                        )
                    elif field == "category":
                        actual_field_name = field_mapping.get(
                            "category_field", "category"
                        )
                    elif field == "risk_level":
                        actual_field_name = field_mapping.get(
                            "risk_level_field", "risk_level"
                        )
                    elif field == "status":
                        actual_field_name = field_mapping.get("status_field", "status")

                    # Get the value from the change request object
                    value = (
                        getattr(cr, actual_field_name, None)
                        if hasattr(cr, actual_field_name)
                        else getattr(cr, field, None)
                    )

                    # Skip empty fields if configured to do so
                    if not value and not show_empty:
                        continue

                    # Format the value
                    if value:
                        if field == "description" and len(str(value)) > desc_max_length:
                            value = str(value)[:desc_max_length] + "..."
                        elif field == "created_date" and hasattr(value, "strftime"):
                            try:
                                value = value.strftime(date_format)
                            except (AttributeError, ValueError, TypeError):
                                value = str(value)
                        else:
                            value = str(value)
                    else:
                        value = "Not specified"

                    # Get human-readable label
                    label = field_labels.get(field, field.replace("_", " ").title())
                    section_parts.append(f"**{label}:** {value}")

                section_parts.append("")  # Add spacing between change requests

            return "\n".join(section_parts)

        except Exception as e:
            self.logger.error(f"Error formatting change request summary: {e}")
            return ""

    def _fix_ai_generated_tables(self, content: str) -> str:
        """Fix malformed markdown tables generated by AI"""
        if not content:
            return content

        lines = content.split("\n")
        fixed_lines: list[str] = []
        i = 0

        while i < len(lines):
            line = lines[i].strip()

            # Check if this line looks like a table row (contains | and has at least 2 cells)
            if (
                "|" in line
                and len([cell for cell in line.split("|") if cell.strip()]) >= 2
            ):
                # Found potential table start
                table_lines = []
                j = i

                # Collect all consecutive lines that look like table rows
                while j < len(lines):
                    current_line = lines[j].strip()
                    if (
                        "|" in current_line
                        and len(
                            [cell for cell in current_line.split("|") if cell.strip()]
                        )
                        >= 2
                    ):
                        table_lines.append(current_line)
                        j += 1
                    elif current_line == "":
                        # Empty line - might be end of table
                        j += 1
                        break
                    else:
                        # Non-table line
                        break

                if table_lines:
                    # Ensure there's a blank line before the table (but not if previous line is a header)
                    if (
                        fixed_lines
                        and fixed_lines[-1].strip()
                        and not fixed_lines[-1].strip().startswith("#")
                    ):
                        fixed_lines.append("")

                    # Process and fix table structure
                    processed_table = self._process_ai_table_lines(table_lines)
                    fixed_lines.extend(processed_table)

                    # Ensure there's a blank line after the table
                    fixed_lines.append("")
                    i = j
                else:
                    fixed_lines.append(lines[i])
                    i += 1
            else:
                fixed_lines.append(lines[i])
                i += 1

        return "\n".join(fixed_lines)

    def _process_ai_table_lines(self, table_lines: list) -> list:
        """Process AI-generated table lines to ensure proper markdown table format"""
        if not table_lines:
            return []

        processed_lines = []

        # Process first line as header
        header_line = table_lines[0]
        header_cells = [cell.strip() for cell in header_line.split("|") if cell.strip()]

        # Ensure proper header format
        formatted_header = "| " + " | ".join(header_cells) + " |"
        processed_lines.append(formatted_header)

        # Add separator line
        separator = "| " + " | ".join(["-------"] * len(header_cells)) + " |"
        processed_lines.append(separator)

        # Process remaining lines as data rows
        for i in range(1, len(table_lines)):
            line = table_lines[i]

            # Skip lines that look like separators
            if "-" in line and all(
                cell.strip() == "" or cell.strip().replace("-", "") == ""
                for cell in line.split("|")
            ):
                continue

            # Process data row
            cells = [cell.strip() for cell in line.split("|") if cell.strip()]

            # Pad cells to match header count
            while len(cells) < len(header_cells):
                cells.append("")

            # Truncate if too many cells
            if len(cells) > len(header_cells):
                cells = cells[: len(header_cells)]

            formatted_row = "| " + " | ".join(cells) + " |"
            processed_lines.append(formatted_row)

        return processed_lines

    def generate_email_content(self, commit: CommitInfo) -> Tuple[str, str]:
        """Generate email subject and body for a commit using Ollama with change request context"""

        # Build change request context for email
        change_request_context = ""
        if commit.change_requests:
            change_request_context = "\n\nRelated Change Requests:\n"
            for cr in commit.change_requests:
                change_request_context += f"- CR #{cr.number}: {cr.title} (Priority: {cr.priority}, Status: {cr.status})\n"

        system_prompt = """You are an email generator for code commit notifications with access to change request information. Create:
        1. A clear, concise subject line that includes change request references when available
        2. A professional email body that summarizes the changes and their business context

        The email should be informative but not overly technical. Focus on business impact
        and key changes that stakeholders should know about. When change requests are available,
        emphasize the business rationale and priority."""

        prompt = f"""
        Generate an email notification for the following repository commit:

        Revision: {commit.revision}
        Author: {commit.author}
        Date: {commit.date}
        Message: {commit.message}

        {change_request_context}

        Changed files:
        {chr(10).join(commit.changed_paths)}

        Diff (summary):
        {commit.diff[:2000]}{"..." if len(commit.diff) > 2000 else ""}

        Please generate:
        1. EMAIL SUBJECT: [subject line - include CR numbers if available]
        2. EMAIL BODY: [email content with business context from change requests]

        Keep the subject line under 60 characters and the email body professional and concise.
        """

        self.logger.info(f"Generating email content for revision {commit.revision}")

        # Use specialized documentation model for email generation if configured
        doc_model: Optional[str] = getattr(
            self.config, "ollama_model_documentation", None
        )
        if doc_model:
            self.logger.info(
                f"🎯 Using specialized documentation model for email: {doc_model}"
            )

        response = self.call_ollama(prompt, system_prompt, model=doc_model)

        # Parse response to extract subject and body
        lines = response.split("\n")
        subject = "Repository Commit Notification"
        body = response

        for line in lines:
            if line.strip().startswith("EMAIL SUBJECT:"):
                subject = line.replace("EMAIL SUBJECT:", "").strip()
                break

        # Find email body section
        body_start = response.find("EMAIL BODY:")
        if body_start != -1:
            body = response[body_start + len("EMAIL BODY:") :].strip()

        return subject, body
