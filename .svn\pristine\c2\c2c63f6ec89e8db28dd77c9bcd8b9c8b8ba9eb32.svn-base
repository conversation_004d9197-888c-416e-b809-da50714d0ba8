#!/usr/bin/env python3
"""
User Management Service for RepoSense AI
Handles user creation, management, and repository associations
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Tuple

from models import Config, RepositoryConfig, User, UserRole
from repository_database import RepositoryDatabase
from user_database import UserDatabase


class UserManagementService:
    """Service for managing users and their repository associations"""

    def __init__(self, config: Config, db_path: str = "/app/data/reposense.db"):
        self.config = config
        self.user_db = UserDatabase(db_path)
        self.repo_db = RepositoryDatabase(db_path)
        self.logger = logging.getLogger(__name__)

    def create_user(
        self,
        username: str,
        email: str,
        full_name: str,
        role: UserRole = UserRole.DEVELOPER,
        **kwargs,
    ) -> Tuple[bool, str, Optional[User]]:
        """
        Create a new user

        Args:
            username: Unique username
            email: User's email address
            full_name: User's full name
            role: User role (default: DEVELOPER)
            **kwargs: Additional user properties

        Returns:
            Tuple of (success, message, user_object)
        """
        # Validate input
        if not username or not email or not full_name:
            return False, "Username, email, and full name are required", None

        # Check for existing user
        if self.user_db.get_user_by_email(email):
            return False, f"User with email {email} already exists", None

        if self.user_db.get_user_by_username(username):
            return False, f"User with username {username} already exists", None

        # Create user
        user = User(
            username=username,
            email=email,
            full_name=full_name,
            role=role,
            phone=kwargs.get("phone"),
            department=kwargs.get("department"),
            receive_all_notifications=kwargs.get("receive_all_notifications", False),
            created_date=datetime.now().isoformat(),
            last_modified=datetime.now().isoformat(),
        )

        if self.user_db.create_user(user):
            self.logger.info(f"Created user: {username} ({email})")
            return True, "User created successfully", user
        else:
            return False, "Failed to add user to database", None

    def update_user(self, user_id: str, **kwargs) -> Tuple[bool, str]:
        """Update an existing user"""
        user = self.user_db.get_user_by_id(user_id)
        if not user:
            return False, "User not found"

        # Update fields
        if "username" in kwargs and kwargs["username"] != user.username:
            # Check if new username is available
            existing = self.user_db.get_user_by_username(kwargs["username"])
            if existing and existing.id != user_id:
                return False, "Username already exists"
            user.username = kwargs["username"]

        if "email" in kwargs and kwargs["email"] != user.email:
            # Check if new email is available
            existing = self.user_db.get_user_by_email(kwargs["email"])
            if existing and existing.id != user_id:
                return False, "Email already exists"
            user.email = kwargs["email"]

        # Update other fields
        for field in [
            "full_name",
            "phone",
            "department",
            "enabled",
            "receive_all_notifications",
        ]:
            if field in kwargs:
                setattr(user, field, kwargs[field])

        if "role" in kwargs:
            if isinstance(kwargs["role"], str):
                try:
                    user.role = UserRole(kwargs["role"])
                except ValueError:
                    return False, f"Invalid role: {kwargs['role']}"
            else:
                user.role = kwargs["role"]

        user.last_modified = datetime.now().isoformat()

        if self.user_db.update_user(user):
            self.logger.info(f"Updated user: {user.username} ({user.email})")
            return True, "User updated successfully"
        else:
            return False, "Failed to update user in database"

    def delete_user(self, user_id: str) -> Tuple[bool, str]:
        """Delete a user and remove from all repository associations"""
        user = self.user_db.get_user_by_id(user_id)
        if not user:
            return False, "User not found"

        username = user.username

        # Remove user from all repository assignments
        for repo in self.repo_db.get_all_repositories():
            if user_id in repo.assigned_users:
                repo.assigned_users.remove(user_id)
                self.repo_db.update_repository(repo)

        if self.user_db.delete_user(user_id):
            self.logger.info(f"Deleted user: {username}")
            return True, "User deleted successfully"
        else:
            return False, "Failed to delete user"

    def assign_user_to_repository(self, user_id: str, repo_id: str) -> Tuple[bool, str]:
        """Assign a user to a repository"""
        user = self.user_db.get_user_by_id(user_id)
        if not user:
            return False, "User not found"

        repo = self.repo_db.get_repository_by_id(repo_id)
        if not repo:
            return False, "Repository not found"

        if user_id not in repo.assigned_users:
            repo.assigned_users.append(user_id)
            self.logger.info(f"Assigned user {user.username} to repository {repo.name}")
            return True, f"User assigned to {repo.name}"
        else:
            return False, "User already assigned to this repository"

    def unassign_user_from_repository(
        self, user_id: str, repo_id: str
    ) -> Tuple[bool, str]:
        """Remove user assignment from a repository"""
        user = self.user_db.get_user_by_id(user_id)
        if not user:
            return False, "User not found"

        repo = self.repo_db.get_repository_by_id(repo_id)
        if not repo:
            return False, "Repository not found"

        if user_id in repo.assigned_users:
            repo.assigned_users.remove(user_id)
            self.logger.info(
                f"Unassigned user {user.username} from repository {repo.name}"
            )
            return True, f"User unassigned from {repo.name}"
        else:
            return False, "User not assigned to this repository"

    def subscribe_user_to_repository(
        self, user_id: str, repo_id: str
    ) -> Tuple[bool, str]:
        """Subscribe a user to repository notifications"""
        user = self.user_db.get_user_by_id(user_id)
        if not user:
            return False, "User not found"

        repo = self.repo_db.get_repository_by_id(repo_id)
        if not repo:
            return False, "Repository not found"

        if repo_id not in user.repository_subscriptions:
            user.repository_subscriptions.append(repo_id)
            user.last_modified = datetime.now().isoformat()
            self.logger.info(
                f"Subscribed user {user.username} to repository {repo.name}"
            )
            return True, f"Subscribed to {repo.name} notifications"
        else:
            return False, "User already subscribed to this repository"

    def unsubscribe_user_from_repository(
        self, user_id: str, repo_id: str
    ) -> Tuple[bool, str]:
        """Unsubscribe a user from repository notifications"""
        user = self.user_db.get_user_by_id(user_id)
        if not user:
            return False, "User not found"

        repo = self.repo_db.get_repository_by_id(repo_id)
        if not repo:
            return False, "Repository not found"

        if repo_id in user.repository_subscriptions:
            user.repository_subscriptions.remove(repo_id)
            user.last_modified = datetime.now().isoformat()
            self.logger.info(
                f"Unsubscribed user {user.username} from repository {repo.name}"
            )
            return True, f"Unsubscribed from {repo.name} notifications"
        else:
            return False, "User not subscribed to this repository"

    def get_user_repositories(self, user_id: str) -> Dict[str, List[RepositoryConfig]]:
        """Get repositories associated with a user"""
        user = self.user_db.get_user_by_id(user_id)
        if not user:
            return {"assigned": [], "subscribed": []}

        assigned_repos = []
        subscribed_repos = []

        # Note: This method needs to be updated to use the new database-based user-repository relationships
        # For now, return empty lists since the old config-based system is no longer used
        # TODO: Implement database-based repository-user relationship queries

        return {"assigned": assigned_repos, "subscribed": subscribed_repos}

    def get_repository_users(self, repo_id: str) -> Dict[str, List[User]]:
        """Get users associated with a repository"""
        # Note: This method needs to be updated to use the new database-based user-repository relationships
        # For now, return empty lists since the old config-based system is no longer used
        return {"assigned": [], "subscribed": [], "global": []}

        assigned_users = []
        subscribed_users = []
        global_users = []

        for user in self.user_db.get_enabled_users():
            if user.id in repo.assigned_users:
                assigned_users.append(user)
            if repo_id in user.repository_subscriptions:
                subscribed_users.append(user)
            if user.receive_all_notifications:
                global_users.append(user)

        return {
            "assigned": assigned_users,
            "subscribed": subscribed_users,
            "global": global_users,
        }

    def bulk_assign_users(
        self, user_ids: List[str], repo_id: str
    ) -> Tuple[int, List[str]]:
        """Assign multiple users to a repository"""
        # Note: This method needs to be updated to use the new database-based user-repository relationships
        # For now, return failure since the old config-based system is no longer used
        return 0, ["User-repository assignment not implemented with database backend"]

        success_count = 0
        errors = []

        for user_id in user_ids:
            success, message = self.assign_user_to_repository(user_id, repo_id)
            if success:
                success_count += 1
            else:
                errors.append(f"User {user_id}: {message}")

        return success_count, errors

    def get_users_summary(self) -> Dict:
        """Get summary statistics about users"""
        all_users = self.user_db.get_all_users()
        enabled_users_list = self.user_db.get_enabled_users()

        total_users = len(all_users)
        enabled_users = len(enabled_users_list)

        role_counts = {}
        for role in UserRole:
            role_counts[role.value] = len(
                [u for u in enabled_users_list if u.role == role]
            )

        global_notification_users = len(
            [u for u in enabled_users_list if u.receive_all_notifications]
        )

        return {
            "total_users": total_users,
            "enabled_users": enabled_users,
            "disabled_users": total_users - enabled_users,
            "role_distribution": role_counts,
            "global_notification_users": global_notification_users,
        }
