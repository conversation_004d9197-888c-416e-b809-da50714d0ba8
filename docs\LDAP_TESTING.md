# LDAP Testing Setup

This document describes how to use the included LDAP test server for development and testing of LDAP authentication features in RepoSense AI.

## Overview

The Docker Compose setup includes two LDAP-related containers:

1. **OpenLDAP Server** (`openldap`) - The actual LDAP directory server
2. **phpLDAPadmin** (`ldap-admin`) - Web-based LDAP administration interface

## Quick Start

### 1. Start the LDAP Services

```bash
# Start all services including LDAP
docker-compose up -d

# Or start only LDAP services
docker-compose up -d openldap ldap-admin
```

### 2. Access the Services

- **LDAP Server**: `ldap://localhost:1389`
- **LDAPS Server**: `ldaps://localhost:1636`
- **Web Admin Interface**: http://localhost:8080

### 3. Default Configuration

**LDAP Server Details:**
- **Host**: `openldap` (from containers) or `localhost` (from host)
- **Port**: `1389` (LDAP) / `1636` (LDAPS)
- **Base DN**: `dc=reposense,dc=local`
- **Admin DN**: `cn=admin,dc=reposense,dc=local`
- **Admin Password**: `adminpassword`

**Pre-configured Test Users:**
- `user01` / `password1`
- `user02` / `password2`
- `user03` / `password3`

**User DN Format**: `cn=user01,ou=users,dc=reposense,dc=local`

## Using phpLDAPadmin

1. Open http://localhost:8080 in your browser
2. Click "login" on the left sidebar
3. Enter login credentials:
   - **Login DN**: `cn=admin,dc=reposense,dc=local`
   - **Password**: `adminpassword`
4. Browse and manage the LDAP directory

## Testing LDAP Authentication

### Command Line Testing

```bash
# Test LDAP connection
ldapsearch -x -H ldap://localhost:1389 -b "dc=reposense,dc=local" -D "cn=admin,dc=reposense,dc=local" -w adminpassword

# Test user authentication
ldapsearch -x -H ldap://localhost:1389 -b "dc=reposense,dc=local" -D "cn=user01,ou=users,dc=reposense,dc=local" -w password1

# Search for users
ldapsearch -x -H ldap://localhost:1389 -b "ou=users,dc=reposense,dc=local" -D "cn=admin,dc=reposense,dc=local" -w adminpassword "(objectClass=inetOrgPerson)"
```

### Python Testing

```python
import ldap3

# Connect to LDAP server
server = ldap3.Server('ldap://localhost:1389')
conn = ldap3.Connection(server, 'cn=user01,ou=users,dc=reposense,dc=local', 'password1')

# Test authentication
if conn.bind():
    print("Authentication successful!")
    conn.unbind()
else:
    print("Authentication failed!")
```

## Directory Structure

The LDAP directory is organized as follows:

```
dc=reposense,dc=local
├── cn=admin (admin user)
├── ou=users (organizational unit for users)
│   ├── cn=user01
│   ├── cn=user02
│   └── cn=user03
└── ou=groups (organizational unit for groups)
    └── cn=developers
```

## Configuration for RepoSense AI

When implementing LDAP authentication in RepoSense AI, use these settings:

```json
{
  "ldap": {
    "server": "ldap://openldap:1389",
    "base_dn": "dc=reposense,dc=local",
    "user_dn_template": "cn={username},ou=users,dc=reposense,dc=local",
    "bind_dn": "cn=admin,dc=reposense,dc=local",
    "bind_password": "adminpassword",
    "user_search_base": "ou=users,dc=reposense,dc=local",
    "user_search_filter": "(cn={username})",
    "group_search_base": "ou=groups,dc=reposense,dc=local"
  }
}
```

## Adding More Test Users

### Via phpLDAPadmin

1. Navigate to `ou=users,dc=reposense,dc=local`
2. Click "Create a child entry"
3. Select "Generic: User Account"
4. Fill in the required fields

### Via LDIF File

Create a file `new_users.ldif`:

```ldif
dn: cn=testuser,ou=users,dc=reposense,dc=local
objectClass: inetOrgPerson
objectClass: posixAccount
objectClass: shadowAccount
cn: testuser
sn: User
givenName: Test
uid: testuser
uidNumber: 1004
gidNumber: 1000
homeDirectory: /home/<USER>
loginShell: /bin/bash
userPassword: {SSHA}hashedpassword
```

Then import it:

```bash
docker exec -it reposense-ai-openldap ldapadd -x -D "cn=admin,dc=reposense,dc=local" -w adminpassword -f /path/to/new_users.ldif
```

## Troubleshooting

### Container Issues

```bash
# Check container status
docker-compose ps

# View LDAP server logs
docker-compose logs openldap

# View phpLDAPadmin logs
docker-compose logs ldap-admin

# Restart LDAP services
docker-compose restart openldap ldap-admin
```

### Connection Issues

1. Ensure containers are running: `docker-compose ps`
2. Check port availability: `netstat -an | grep 1389`
3. Test basic connectivity: `telnet localhost 1389`
4. Verify firewall settings

### Authentication Issues

1. Verify user exists in directory
2. Check DN format (case-sensitive)
3. Confirm password is correct
4. Test with admin credentials first

## Security Notes

⚠️ **This is a test setup only!**

- Default passwords are used
- No SSL/TLS encryption by default
- Not suitable for production use
- Data is stored in Docker volumes

For production LDAP integration, use proper security measures:
- Strong passwords
- SSL/TLS encryption
- Proper access controls
- Regular backups
- Security monitoring

## Cleanup

To remove LDAP test data:

```bash
# Stop and remove containers
docker-compose down

# Remove LDAP data volume
docker volume rm reposense_ai_ldap_data
```
