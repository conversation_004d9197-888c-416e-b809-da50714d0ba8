{% extends "base.html" %}

{% block title %}LDAP User Synchronization - RepoSense AI{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users-cog"></i> LDAP User Synchronization</h2>
                <div>
                    <button class="btn btn-outline-primary" onclick="testLDAPConnection()">
                        <i class="fas fa-plug"></i> Test Connection
                    </button>
                    <button class="btn btn-primary" onclick="runLDAPSync()">
                        <i class="fas fa-sync"></i> Run Sync Now
                    </button>
                </div>
            </div>

            <!-- LDAP Status Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-info-circle"></i> LDAP Sync Status</h5>
                </div>
                <div class="card-body">
                    <div class="row" id="ldap-status-content">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Status:</strong>
                                <span id="ldap-enabled-status" class="badge badge-secondary">Loading...</span>
                            </div>
                            <div class="mb-3">
                                <strong>LDAP Server:</strong>
                                <span id="ldap-server">-</span>
                            </div>
                            <div class="mb-3">
                                <strong>Sync Interval:</strong>
                                <span id="ldap-sync-interval">-</span>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <strong>Last Sync:</strong>
                                <span id="ldap-last-sync">-</span>
                            </div>
                            <div class="mb-3">
                                <strong>LDAP Users:</strong>
                                <span id="ldap-users-count">-</span>
                            </div>
                            <div class="mb-3">
                                <strong>Total Users:</strong>
                                <span id="total-users-count">-</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- LDAP Configuration Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-cog"></i> LDAP Configuration</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i>
                        LDAP configuration is managed through the main configuration file.
                        Changes require a service restart to take effect.
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <h6>Connection Settings</h6>
                            <ul class="list-unstyled">
                                <li><strong>Server:</strong> {{ config.ldap_server or 'Not configured' }}</li>
                                <li><strong>Port:</strong> {{ config.ldap_port }}</li>
                                <li><strong>Use SSL:</strong> {{ 'Yes' if config.ldap_use_ssl else 'No' }}</li>
                                <li><strong>Base DN:</strong> {{ config.ldap_user_base_dn or 'Not configured' }}</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>Attribute Mapping</h6>
                            <ul class="list-unstyled">
                                <li><strong>Username:</strong> {{ config.ldap_username_attr }}</li>
                                <li><strong>Email:</strong> {{ config.ldap_email_attr }}</li>
                                <li><strong>Full Name:</strong> {{ config.ldap_fullname_attr }}</li>
                                <li><strong>Phone:</strong> {{ config.ldap_phone_attr }}</li>
                                <li><strong>Groups:</strong> {{ config.ldap_groups_attr }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Group to Role Mapping Card -->
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-users"></i> Group to Role Mapping</h5>
                    <button type="button" class="btn btn-sm btn-success" onclick="addGroupMapping()">
                        <i class="fas fa-plus"></i> Add Mapping
                    </button>
                </div>
                <div class="card-body">
                    <form id="group-mapping-form">
                        <div class="table-responsive">
                            <table class="table table-sm" id="group-mapping-table">
                                <thead>
                                    <tr>
                                        <th>LDAP Group DN</th>
                                        <th>RepoSense Role</th>
                                        <th width="100">Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="group-mapping-tbody">
                                    {% for group_dn, role in config.ldap_group_role_mapping.items() %}
                                    <tr data-group-dn="{{ group_dn }}">
                                        <td>
                                            <input type="text" class="form-control form-control-sm" name="group_dn[]"
                                                value="{{ group_dn }}"
                                                placeholder="CN=Group,OU=Groups,DC=company,DC=com">
                                        </td>
                                        <td>
                                            <select class="form-control form-control-sm" name="group_role[]">
                                                <option value="VIEWER" {% if role=='VIEWER' %}selected{% endif %}>VIEWER
                                                </option>
                                                <option value="DEVELOPER" {% if role=='DEVELOPER' %}selected{% endif %}>
                                                    DEVELOPER</option>
                                                <option value="MANAGER" {% if role=='MANAGER' %}selected{% endif %}>
                                                    MANAGER</option>
                                                <option value="ADMIN" {% if role=='ADMIN' %}selected{% endif %}>ADMIN
                                                </option>
                                            </select>
                                        </td>
                                        <td>
                                            <button type="button" class="btn btn-sm btn-outline-danger"
                                                onclick="removeGroupMapping(this)">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="mb-2">
                                    <small class="text-muted">
                                        <strong>Default Role:</strong>
                                        <span class="badge badge-secondary">{{ config.ldap_default_role }}</span>
                                        (assigned to users with no matching groups)
                                    </small>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <button type="button" class="btn btn-primary" onclick="saveGroupMappings()">
                                    <i class="fas fa-save"></i> Save Mappings
                                </button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Sync Log Card -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-list"></i> Recent Sync Activity</h5>
                </div>
                <div class="card-body">
                    <div id="sync-log" class="bg-light p-3"
                        style="height: 300px; overflow-y: auto; font-family: monospace; font-size: 0.9em;">
                        <div class="text-muted">No sync activity yet. Click "Run Sync Now" to start synchronization.
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div class="modal fade" id="loadingModal" tabindex="-1" role="dialog" aria-hidden="true">
    <div class="modal-dialog modal-sm modal-dialog-centered" role="document">
        <div class="modal-content">
            <div class="modal-body text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="sr-only">Loading...</span>
                </div>
                <div class="mt-2" id="loading-message">Processing...</div>
            </div>
        </div>
    </div>
</div>

<script>
    // LDAP Management JavaScript
    let syncInProgress = false;

    function updateLDAPStatus() {
        fetch('/api/ldap/status')
            .then(response => response.json())
            .then(data => {
                if (data.enabled) {
                    document.getElementById('ldap-enabled-status').textContent = 'Enabled';
                    document.getElementById('ldap-enabled-status').className = 'badge badge-success';
                    document.getElementById('ldap-server').textContent = data.ldap_server || '-';
                    document.getElementById('ldap-sync-interval').textContent =
                        data.sync_interval ? `${data.sync_interval} seconds` : '-';
                    document.getElementById('ldap-last-sync').textContent =
                        data.last_sync ? new Date(data.last_sync).toLocaleString() : 'Never';
                    document.getElementById('ldap-users-count').textContent = data.ldap_users_count || '0';
                    document.getElementById('total-users-count').textContent = data.total_users_count || '0';
                } else {
                    document.getElementById('ldap-enabled-status').textContent = 'Disabled';
                    document.getElementById('ldap-enabled-status').className = 'badge badge-secondary';
                    document.getElementById('ldap-server').textContent = 'Not configured';
                    document.getElementById('ldap-sync-interval').textContent = '-';
                    document.getElementById('ldap-last-sync').textContent = '-';
                    document.getElementById('ldap-users-count').textContent = '-';
                    document.getElementById('total-users-count').textContent = '-';
                }
            })
            .catch(error => {
                console.error('Error fetching LDAP status:', error);
                document.getElementById('ldap-enabled-status').textContent = 'Error';
                document.getElementById('ldap-enabled-status').className = 'badge badge-danger';
            });
    }

    function testLDAPConnection() {
        if (syncInProgress) return;

        showLoadingModal('Testing LDAP connection...');

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 15000);

        fetch('/api/ldap/test-connection', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({}),
            signal: controller.signal
        })
            .then(response => {
                clearTimeout(timeoutId);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                hideLoadingModal();
                if (data.success) {
                    showAlert('success', 'LDAP Connection Test', data.message);
                    addToSyncLog('SUCCESS', 'Connection test passed: ' + data.message);
                } else {
                    showAlert('danger', 'LDAP Connection Test Failed', data.message);
                    addToSyncLog('ERROR', 'Connection test failed: ' + data.message);
                }
            })
            .catch(error => {
                clearTimeout(timeoutId);
                hideLoadingModal();

                let errorMessage = error.message;
                if (error.name === 'AbortError') {
                    errorMessage = 'Request timed out after 15 seconds';
                }

                showAlert('danger', 'Connection Test Error', 'Failed to test LDAP connection: ' + errorMessage);
                addToSyncLog('ERROR', 'Connection test error: ' + errorMessage);
            });
    }

    function runLDAPSync() {
        if (syncInProgress) return;

        syncInProgress = true;
        showLoadingModal('Synchronizing users from LDAP...');

        const syncController = new AbortController();
        const syncTimeoutId = setTimeout(() => syncController.abort(), 30000);

        fetch('/api/ldap/sync', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({}),
            signal: syncController.signal
        })
            .then(response => {
                clearTimeout(syncTimeoutId);
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                syncInProgress = false;
                hideLoadingModal();

                if (data.success) {
                    showAlert('success', 'LDAP Sync Completed', data.message);
                    addToSyncLog('SUCCESS', data.message);
                    if (data.stats) {
                        addToSyncLog('INFO', `Details: ${JSON.stringify(data.stats)}`);
                    }
                    updateLDAPStatus(); // Refresh status
                } else {
                    showAlert('danger', 'LDAP Sync Failed', data.message);
                    addToSyncLog('ERROR', 'Sync failed: ' + data.message);
                }
            })
            .catch(error => {
                clearTimeout(syncTimeoutId);
                syncInProgress = false;
                hideLoadingModal();

                let errorMessage = error.message;
                if (error.name === 'AbortError') {
                    errorMessage = 'Sync request timed out after 30 seconds';
                }

                showAlert('danger', 'Sync Error', 'Failed to run LDAP sync: ' + errorMessage);
                addToSyncLog('ERROR', 'Sync error: ' + errorMessage);
            });
    }

    let loadingModalTimeout = null;
    let currentModal = null;

    function showLoadingModal(message) {
        try {
            document.getElementById('loading-message').textContent = message;
            const modalElement = document.getElementById('loadingModal');

            // Force hide any existing modal first
            hideLoadingModal();

            // Create new modal instance
            currentModal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',
                keyboard: false
            });
            currentModal.show();

            // Set a timeout to automatically hide the modal after 30 seconds
            if (loadingModalTimeout) {
                clearTimeout(loadingModalTimeout);
            }
            loadingModalTimeout = setTimeout(() => {
                hideLoadingModal();
                showAlert('warning', 'Request Timeout', 'The request is taking longer than expected. Please try again.');
            }, 30000);
        } catch (error) {
            console.error('Error showing modal:', error);
        }
    }

    function hideLoadingModal() {
        try {
            if (loadingModalTimeout) {
                clearTimeout(loadingModalTimeout);
                loadingModalTimeout = null;
            }

            // Try to hide using current modal instance
            if (currentModal) {
                currentModal.hide();
                currentModal = null;
            } else {
                // Try to get existing instance
                const modalElement = document.getElementById('loadingModal');
                const existingModal = bootstrap.Modal.getInstance(modalElement);
                if (existingModal) {
                    existingModal.hide();
                }
            }

            // Force cleanup after a short delay to ensure visual removal
            setTimeout(() => {
                const modalElement = document.getElementById('loadingModal');
                if (modalElement) {
                    modalElement.style.display = 'none !important';
                    modalElement.style.visibility = 'hidden';
                    modalElement.style.opacity = '0';
                    modalElement.classList.remove('show', 'fade', 'modal');
                    modalElement.setAttribute('aria-hidden', 'true');
                    modalElement.removeAttribute('aria-modal');
                    modalElement.removeAttribute('role');
                }

                // Clean up body classes
                document.body.classList.remove('modal-open');
                document.body.style.overflow = '';
                document.body.style.paddingRight = '';

                // Remove any lingering backdrops
                const backdrops = document.querySelectorAll('.modal-backdrop');
                backdrops.forEach(backdrop => backdrop.remove());
            }, 100);

        } catch (error) {
            console.error('Error hiding modal:', error);
            // Force cleanup on error
            const modalElement = document.getElementById('loadingModal');
            if (modalElement) {
                modalElement.style.display = 'none !important';
                modalElement.classList.remove('show');
            }
            document.body.classList.remove('modal-open');
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
        }
    }

    function showAlert(type, title, message) {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
        <strong>${title}:</strong> ${message}
        <button type="button" class="close" data-dismiss="alert">
            <span>&times;</span>
        </button>
    `;

        // Insert at top of container
        const container = document.querySelector('.container-fluid');
        container.insertBefore(alertDiv, container.firstChild);

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    function addToSyncLog(level, message) {
        const logContainer = document.getElementById('sync-log');
        const timestamp = new Date().toLocaleTimeString();
        const logEntry = document.createElement('div');

        let levelClass = 'text-muted';
        if (level === 'SUCCESS') levelClass = 'text-success';
        else if (level === 'ERROR') levelClass = 'text-danger';
        else if (level === 'INFO') levelClass = 'text-info';

        logEntry.innerHTML = `<span class="text-muted">[${timestamp}]</span> <span class="${levelClass}">${level}:</span> ${message}`;

        // Clear placeholder text if present
        if (logContainer.querySelector('.text-muted') && logContainer.children.length === 1) {
            logContainer.innerHTML = '';
        }

        logContainer.appendChild(logEntry);
        logContainer.scrollTop = logContainer.scrollHeight;
    }

    // Group Mapping Management Functions
    function addGroupMapping() {
        const tbody = document.getElementById('group-mapping-tbody');
        const newRow = document.createElement('tr');

        newRow.innerHTML = `
            <td>
                <input type="text" class="form-control form-control-sm"
                       name="group_dn[]" value=""
                       placeholder="CN=Group,OU=Groups,DC=company,DC=com">
            </td>
            <td>
                <select class="form-control form-control-sm" name="group_role[]">
                    <option value="VIEWER">VIEWER</option>
                    <option value="DEVELOPER">DEVELOPER</option>
                    <option value="MANAGER">MANAGER</option>
                    <option value="ADMIN">ADMIN</option>
                </select>
            </td>
            <td>
                <button type="button" class="btn btn-sm btn-outline-danger" onclick="removeGroupMapping(this)">
                    <i class="fas fa-trash"></i>
                </button>
            </td>
        `;

        tbody.appendChild(newRow);
    }

    function removeGroupMapping(button) {
        const row = button.closest('tr');
        row.remove();
    }

    async function saveGroupMappings() {
        const form = document.getElementById('group-mapping-form');
        const formData = new FormData(form);

        // Build group mapping object
        const groupDns = formData.getAll('group_dn[]');
        const groupRoles = formData.getAll('group_role[]');

        const mappings = {};
        for (let i = 0; i < groupDns.length; i++) {
            const dn = groupDns[i].trim();
            const role = groupRoles[i];

            if (dn) { // Only add non-empty DNs
                mappings[dn] = role;
            }
        }

        try {
            showLoadingModal('Saving group mappings...');

            const response = await fetch('/api/ldap/group-mappings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ mappings: mappings })
            });

            const data = await response.json();

            hideLoadingModal();

            if (data.success) {
                showAlert('success', 'Group Mappings Saved', 'Group-to-role mappings have been updated successfully.');
                addToSyncLog('SUCCESS', 'Group mappings saved successfully');
            } else {
                showAlert('danger', 'Save Failed', data.message || 'Failed to save group mappings');
                addToSyncLog('ERROR', 'Failed to save group mappings: ' + (data.message || 'Unknown error'));
            }
        } catch (error) {
            hideLoadingModal();
            showAlert('danger', 'Save Error', 'Failed to save group mappings: ' + error.message);
            addToSyncLog('ERROR', 'Save error: ' + error.message);
        }
    }



    // Initialize page
    document.addEventListener('DOMContentLoaded', function () {
        updateLDAPStatus();

        // Refresh status every 30 seconds
        setInterval(updateLDAPStatus, 30000);
    });
</script>
{% endblock %}