#!/usr/bin/env python3
"""
Data models for RepoSense AI application
Contains dataclasses for configuration and commit information
"""

import secrets
import uuid
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional


class UserRole(Enum):
    """User roles for access control and notification preferences"""

    ADMIN = "admin"
    MANAGER = "manager"
    DEVELOPER = "developer"
    VIEWER = "viewer"


class HistoricalScanStatus(Enum):
    """Historical scan status enumeration"""

    NOT_STARTED = "not_started"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class NotificationCategory(Enum):
    """Categories of notifications users can subscribe to"""

    COMMITS = "commits"  # New commits (current functionality)
    SYSTEM_HEALTH = "system_health"  # Service status, failures
    REPOSITORY_MGMT = "repository_mgmt"  # Repo changes, discoveries
    SECURITY_ALERTS = "security_alerts"  # High-risk changes, security issues
    PROCESSING_STATUS = "processing_status"  # Document processing events
    MAINTENANCE = "maintenance"  # Performance, disk space warnings


class RepositoryRelationshipType(Enum):
    """Types of relationships between users and repositories"""

    OWNER = "owner"  # Full administrative control
    MAINTAINER = "maintainer"  # Can modify settings, assign users
    DEVELOPER = "developer"  # Active contributor, gets all commit notifications
    REVIEWER = "reviewer"  # Code review responsibilities, gets high-risk notifications
    OBSERVER = "observer"  # Interested party, gets summary notifications
    SUBSCRIBER = "subscriber"  # Opted-in for notifications


@dataclass
class HistoricalScanConfig:
    """Configuration for historical scanning of a repository"""

    enabled: bool = False

    # Revision range scanning
    scan_by_revision: bool = True
    start_revision: Optional[int] = None
    end_revision: Optional[int] = None

    # Date range scanning
    scan_by_date: bool = False
    start_date: Optional[datetime] = None
    end_date: Optional[datetime] = None

    # Scanning preferences
    batch_size: int = 10  # Number of revisions to process in each batch
    include_merge_commits: bool = True
    skip_large_commits: bool = False  # Skip commits with >100 files changed
    max_files_per_commit: int = 100
    force_rescan: bool = False  # Allow re-scanning of existing revisions

    # Progress tracking
    last_scanned_revision: Optional[int] = None
    scan_status: HistoricalScanStatus = HistoricalScanStatus.NOT_STARTED
    scan_started_at: Optional[datetime] = None
    scan_completed_at: Optional[datetime] = None
    total_revisions: Optional[int] = None
    processed_revisions: int = 0
    failed_revisions: int = 0
    error_message: Optional[str] = None

    # Analysis preferences
    generate_documentation: bool = True
    analyze_code_review: bool = True
    analyze_documentation_impact: bool = True


@dataclass
class RepositoryNotificationPreferences:
    """Notification preferences specific to a repository relationship"""

    # Event categories for this repository
    enabled_categories: List[NotificationCategory] = field(default_factory=list)

    # Commit-specific preferences
    notify_on_own_commits: bool = False  # Get notifications for your own commits
    notify_on_high_risk_only: bool = False  # Only high-risk commits
    notify_on_specific_paths: List[str] = field(
        default_factory=list
    )  # Specific file paths/patterns

    # Processing preferences
    notify_on_processing_failures: bool = True
    notify_on_scan_completion: bool = False

    # Delivery preferences
    immediate_notification: bool = True
    digest_frequency: str = "never"  # "never", "daily", "weekly"

    @classmethod
    def get_defaults_for_relationship(
        cls, relationship_type: RepositoryRelationshipType
    ) -> "RepositoryNotificationPreferences":
        """Get default preferences based on relationship type"""
        if relationship_type == RepositoryRelationshipType.OWNER:
            return cls(
                enabled_categories=[
                    NotificationCategory.COMMITS,
                    NotificationCategory.SECURITY_ALERTS,
                    NotificationCategory.PROCESSING_STATUS,
                    NotificationCategory.REPOSITORY_MGMT,
                ],
                notify_on_processing_failures=True,
                notify_on_scan_completion=True,
            )
        elif relationship_type == RepositoryRelationshipType.MAINTAINER:
            return cls(
                enabled_categories=[
                    NotificationCategory.COMMITS,
                    NotificationCategory.SECURITY_ALERTS,
                    NotificationCategory.PROCESSING_STATUS,
                    NotificationCategory.REPOSITORY_MGMT,
                ],
                notify_on_processing_failures=True,
                notify_on_scan_completion=True,
            )
        elif relationship_type == RepositoryRelationshipType.DEVELOPER:
            return cls(
                enabled_categories=[
                    NotificationCategory.COMMITS,
                    NotificationCategory.SECURITY_ALERTS,
                ],
                notify_on_own_commits=False,
                notify_on_processing_failures=True,
            )
        elif relationship_type == RepositoryRelationshipType.REVIEWER:
            return cls(
                enabled_categories=[NotificationCategory.SECURITY_ALERTS],
                notify_on_high_risk_only=True,
                notify_on_processing_failures=False,
            )
        elif relationship_type == RepositoryRelationshipType.OBSERVER:
            return cls(
                enabled_categories=[NotificationCategory.COMMITS],
                digest_frequency="daily",
                immediate_notification=False,
            )
        else:  # SUBSCRIBER
            return cls(
                enabled_categories=[NotificationCategory.COMMITS],
                notify_on_high_risk_only=False,
            )


@dataclass
class RepositoryUserRelationship:
    """Defines the relationship between a user and repository"""

    user_id: str
    repository_id: str
    relationship_type: RepositoryRelationshipType

    # Notification preferences for this specific relationship
    notification_preferences: RepositoryNotificationPreferences = field(
        default_factory=RepositoryNotificationPreferences
    )

    # Metadata
    assigned_by: Optional[str] = None  # User ID who made the assignment
    assigned_date: Optional[str] = None
    last_modified: Optional[str] = None

    # Access control
    can_modify_settings: bool = False
    can_assign_users: bool = False
    can_view_sensitive_data: bool = True

    def __post_init__(self):
        """Set default notification preferences based on relationship type"""
        if not self.notification_preferences.enabled_categories:
            self.notification_preferences = (
                RepositoryNotificationPreferences.get_defaults_for_relationship(
                    self.relationship_type
                )
            )


@dataclass
class NotificationPreferences:
    """Global notification preferences for users"""

    # Category subscriptions
    enabled_categories: List[NotificationCategory] = field(default_factory=list)

    # Severity filtering
    min_severity: str = "INFO"  # DEBUG, INFO, WARNING, ERROR, CRITICAL

    # Delivery preferences
    email_enabled: bool = True
    email_digest: bool = False  # Send daily digest instead of immediate
    digest_time: str = "09:00"  # Time for daily digest

    @classmethod
    def get_defaults_for_role(cls, role: UserRole) -> "NotificationPreferences":
        """Get default notification preferences based on user role"""
        if role == UserRole.ADMIN:
            return cls(
                enabled_categories=[
                    NotificationCategory.SYSTEM_HEALTH,
                    NotificationCategory.SECURITY_ALERTS,
                    NotificationCategory.MAINTENANCE,
                ],
                min_severity="WARNING",
            )
        elif role == UserRole.MANAGER:
            return cls(
                enabled_categories=[
                    NotificationCategory.COMMITS,
                    NotificationCategory.SECURITY_ALERTS,
                    NotificationCategory.PROCESSING_STATUS,
                ],
                min_severity="INFO",
            )
        elif role == UserRole.DEVELOPER:
            return cls(
                enabled_categories=[NotificationCategory.COMMITS], min_severity="INFO"
            )
        else:  # VIEWER
            return cls(enabled_categories=[], min_severity="ERROR")


@dataclass
class User:
    """User configuration with details and preferences"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    username: str = ""
    email: str = ""
    full_name: str = ""
    role: UserRole = UserRole.DEVELOPER
    enabled: bool = True

    # Enhanced notification preferences
    notification_preferences: NotificationPreferences = field(
        default_factory=NotificationPreferences
    )

    # Legacy notification preferences (for backward compatibility)
    receive_all_notifications: bool = (
        False  # If True, receives notifications for all repositories
    )
    repository_subscriptions: List[str] = field(
        default_factory=list
    )  # Repository IDs user is subscribed to

    # Contact details
    phone: Optional[str] = None
    department: Optional[str] = None

    # Metadata
    created_date: Optional[str] = None
    last_modified: Optional[str] = None

    # LDAP Integration fields
    ldap_synced: bool = False
    ldap_dn: Optional[str] = None
    last_ldap_sync: Optional[str] = None
    ldap_groups: List[str] = field(default_factory=list)

    def __post_init__(self):
        if not self.username and self.email:
            # Generate username from email if not provided
            self.username = self.email.split("@")[0]

        # Set default notification preferences based on role if not already set
        if not self.notification_preferences.enabled_categories:
            self.notification_preferences = (
                NotificationPreferences.get_defaults_for_role(self.role)
            )


@dataclass
class RepositoryConfig:
    """Configuration for a single repository"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    url: str = ""
    type: str = "svn"  # Repository type: 'svn' or 'git'
    username: Optional[str] = None
    password: Optional[str] = None
    last_revision: int = 0
    last_commit_date: Optional[datetime] = (
        None  # When the revision was committed to source control
    )
    last_processed_time: Optional[datetime] = (
        None  # When RepoSense AI processed the revision
    )
    enabled: bool = True

    # Branch/path selection
    branch_path: Optional[str] = (
        None  # Specific branch path to monitor (e.g., "trunk", "branches/feature-x")
    )
    monitor_all_branches: bool = (
        False  # If True, monitor all branches; if False, monitor only branch_path
    )

    # User associations
    assigned_users: List[str] = field(
        default_factory=list
    )  # User IDs assigned to this repository

    # Legacy email support (for backward compatibility)
    email_recipients: List[str] = field(default_factory=list)  # Direct email addresses

    # Historical scanning configuration
    historical_scan: HistoricalScanConfig = field(default_factory=HistoricalScanConfig)

    # Product documentation configuration
    product_documentation_files: List[str] = field(
        default_factory=list
    )  # Specific files to reference for product documentation context

    # Risk Assessment Aggressiveness Settings
    risk_aggressiveness: str = (
        "BALANCED"  # CONSERVATIVE, BALANCED, AGGRESSIVE, VERY_AGGRESSIVE
    )
    risk_description: str = (
        ""  # User description of why this aggressiveness level was chosen
    )

    # Enhanced user relationships (replaces assigned_users)
    user_relationships: List[RepositoryUserRelationship] = field(default_factory=list)

    # Repository-level notification settings
    default_notification_preferences: RepositoryNotificationPreferences = field(
        default_factory=RepositoryNotificationPreferences
    )

    # Team/group assignments
    team_assignments: List[str] = field(default_factory=list)  # Team IDs

    # Path-based notifications
    path_watchers: Dict[str, List[str]] = field(
        default_factory=dict
    )  # path_pattern -> [user_ids]

    def get_users_by_relationship(
        self, relationship_type: RepositoryRelationshipType
    ) -> List[str]:
        """Get user IDs with a specific relationship type"""
        return [
            rel.user_id
            for rel in self.user_relationships
            if rel.relationship_type == relationship_type
        ]

    def get_user_relationship(
        self, user_id: str
    ) -> Optional[RepositoryUserRelationship]:
        """Get the relationship for a specific user"""
        for rel in self.user_relationships:
            if rel.user_id == user_id:
                return rel
        return None

    def add_user_relationship(
        self,
        user_id: str,
        relationship_type: RepositoryRelationshipType,
        assigned_by: Optional[str] = None,
        notification_preferences: Optional[RepositoryNotificationPreferences] = None,
    ) -> RepositoryUserRelationship:
        """Add or update a user relationship"""
        # Remove existing relationship if any
        self.user_relationships = [
            rel for rel in self.user_relationships if rel.user_id != user_id
        ]

        # Create new relationship with custom or default preferences
        if notification_preferences is None:
            notification_preferences = (
                RepositoryNotificationPreferences.get_defaults_for_relationship(
                    relationship_type
                )
            )

        relationship = RepositoryUserRelationship(
            user_id=user_id,
            repository_id=self.id,
            relationship_type=relationship_type,
            notification_preferences=notification_preferences,
            assigned_by=assigned_by,
            assigned_date=datetime.now().isoformat(),
        )

        self.user_relationships.append(relationship)
        return relationship

    def remove_user_relationship(self, user_id: str) -> bool:
        """Remove a user relationship"""
        original_count = len(self.user_relationships)
        self.user_relationships = [
            rel for rel in self.user_relationships if rel.user_id != user_id
        ]
        return len(self.user_relationships) < original_count

    def add_path_watcher(self, path_pattern: str, user_ids: List[str]):
        """Add users to watch specific paths in the repository"""
        if path_pattern not in self.path_watchers:
            self.path_watchers[path_pattern] = []

        for user_id in user_ids:
            if user_id not in self.path_watchers[path_pattern]:
                self.path_watchers[path_pattern].append(user_id)

    def get_path_watchers(self, changed_paths: List[str]) -> List[str]:
        """Get users who should be notified based on changed paths"""
        import fnmatch

        watchers = set()

        for path in changed_paths:
            for pattern, user_ids in self.path_watchers.items():
                if fnmatch.fnmatch(path, pattern):
                    watchers.update(user_ids)

        return list(watchers)

    def __post_init__(self):
        if not self.name and self.url:
            # Generate a default name from URL
            self.name = self.url.split("/")[-1] or self.url.split("/")[-2]

        # Validate risk aggressiveness level
        valid_levels = ["CONSERVATIVE", "BALANCED", "AGGRESSIVE", "VERY_AGGRESSIVE"]
        if self.risk_aggressiveness not in valid_levels:
            self.risk_aggressiveness = "BALANCED"


@dataclass
class ChangeRequestInfo:
    """Change request information from SQL database"""

    id: str
    number: str
    title: str
    description: str
    priority: str  # HIGH, MEDIUM, LOW
    status: str  # OPEN, IN_PROGRESS, CLOSED, etc.
    created_date: Optional[datetime] = None
    assigned_to: Optional[str] = None
    category: Optional[str] = None
    risk_level: Optional[str] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization"""
        return {
            "id": self.id,
            "number": self.number,
            "title": self.title,
            "description": self.description,
            "priority": self.priority,
            "status": self.status,
            "created_date": self.created_date.isoformat()
            if self.created_date
            else None,
            "assigned_to": self.assigned_to,
            "category": self.category,
            "risk_level": self.risk_level,
        }


@dataclass
class CommitInfo:
    """Data class for repository commit information"""

    revision: str
    author: str
    date: str
    message: str
    changed_paths: List[str]
    diff: str
    repository_id: str = ""
    repository_name: str = ""

    # Change request integration fields
    change_requests: List[ChangeRequestInfo] = field(default_factory=list)
    change_request_numbers: List[str] = field(default_factory=list)


@dataclass
class ServerConfig:
    """Configuration for SVN/Git server connection"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = (
        "default"  # User-defined friendly name (e.g., "main-server", "dev-server")
    )
    description: str = ""  # Optional description
    base_url: str = ""  # Base URL for the server (e.g., "http://sundc:81")
    default_username: Optional[str] = (
        None  # Default username for repositories on this server
    )
    default_password: Optional[str] = (
        None  # Default password for repositories on this server
    )
    enabled: bool = True


@dataclass
class Team:
    """Team/group of users for easier repository assignment"""

    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: str = ""
    description: str = ""
    members: List[str] = field(default_factory=list)  # User IDs
    default_relationship_type: RepositoryRelationshipType = (
        RepositoryRelationshipType.DEVELOPER
    )
    enabled: bool = True

    # Team-level notification preferences
    team_notification_preferences: RepositoryNotificationPreferences = field(
        default_factory=RepositoryNotificationPreferences
    )

    # Metadata
    created_date: Optional[str] = None
    last_modified: Optional[str] = None

    def add_member(self, user_id: str) -> bool:
        """Add a user to the team"""
        if user_id not in self.members:
            self.members.append(user_id)
            self.last_modified = datetime.now().isoformat()
            return True
        return False

    def remove_member(self, user_id: str) -> bool:
        """Remove a user from the team"""
        if user_id in self.members:
            self.members.remove(user_id)
            self.last_modified = datetime.now().isoformat()
            return True
        return False


@dataclass
class SqlConfig:
    """SQL database configuration for change request integration"""

    enabled: bool = False
    host: str = "localhost"
    port: int = 3306
    database: str = ""
    username: str = ""
    password: str = ""
    driver: str = "mysql"  # mysql, postgresql, sqlite, mssql
    connection_timeout: int = 30
    query_timeout: int = 60

    # Change request query configuration
    change_request_query: str = """
        SELECT id, number, title, description, priority, status,
               created_date, assigned_to, category, risk_level
        FROM change_requests
        WHERE number = :change_request_number
    """

    # Parameter extraction patterns
    change_request_patterns: List[str] = field(
        default_factory=lambda: [
            r"CR[#\-\s]*(\d+)",  # CR-123, CR#123, CR 123
            r"Change[#\-\s]*(\d+)",  # Change-123, Change#123
            r"Request[#\-\s]*(\d+)",  # Request-123, Request#123
            r"Ticket[#\-\s]*(\d+)",  # Ticket-123, Ticket#123
            r"Issue[#\-\s]*(\d+)",  # Issue-123, Issue#123
            r"#(\d+)",  # #123 (generic)
        ]
    )

    # Field mapping configuration for change request risk analysis
    field_mappings: Dict[str, str] = field(
        default_factory=lambda: {
            "risk_level_field": "risk_level",  # Field name for explicit risk level
            "priority_field": "priority",  # Field name for priority
            "category_field": "category",  # Field name for category/type
            "status_field": "status",  # Field name for status
        }
    )

    # Value mapping configuration for risk analysis
    value_mappings: Dict[str, Dict[str, str]] = field(
        default_factory=lambda: {
            "priority_to_risk": {
                # Map priority values to risk levels
                "CRITICAL": "CRITICAL",
                "URGENT": "CRITICAL",
                "HIGH": "HIGH",
                "IMPORTANT": "HIGH",
                "MEDIUM": "MEDIUM",
                "NORMAL": "MEDIUM",
                "LOW": "LOW",
                "MINOR": "LOW",
            },
            "category_risk_boost": {
                # Categories that increase risk level
                "SECURITY": "CRITICAL",
                "CRITICAL_BUG": "CRITICAL",
                "DATA_LOSS": "CRITICAL",
                "PERFORMANCE": "HIGH",
                "INTEGRATION": "HIGH",
                "API_CHANGE": "HIGH",
            },
        }
    )

    # Risk analysis configuration
    risk_analysis_config: Dict[str, Any] = field(
        default_factory=lambda: {
            "supported_risk_levels": ["CRITICAL", "HIGH", "MEDIUM", "LOW"],
            "default_risk_level": "MEDIUM",
            "confidence_threshold": 0.3,  # Minimum confidence to return risk level
            "base_confidence": 0.6,  # Base confidence for change request risk
            "max_confidence": 0.9,  # Maximum confidence cap
            "category_weight": 0.3,  # Weight for category-based risk boost
        }
    )

    # Change request summary display configuration
    summary_display_config: Dict[str, Any] = field(
        default_factory=lambda: {
            "enabled": True,  # Whether to include CR summary section in documents
            "section_title": "Change Request Summary",  # Title for the summary section
            "display_fields": [  # Fields to display in summary (in order)
                "number",
                "title",
                "priority",
                "status",
                "risk_level",
                "category",
                "assigned_to",
                "created_date",
                "description",
            ],
            "field_labels": {  # Human-readable labels for fields
                "number": "Request Number",
                "title": "Title",
                "priority": "Priority",
                "status": "Status",
                "risk_level": "Risk Level",
                "category": "Category",
                "assigned_to": "Assigned To",
                "created_date": "Created Date",
                "description": "Description",
            },
            "description_max_length": 300,  # Max characters for description field
            "date_format": "%Y-%m-%d",  # Format for date fields
            "show_empty_fields": False,  # Whether to show fields with no value
        }
    )


@dataclass
class Config:
    """Configuration data class"""

    # Server configuration
    server: ServerConfig = field(
        default_factory=ServerConfig
    )  # Single server for now, can expand to list later

    # Team management
    teams: List[Team] = field(default_factory=list)

    # AI and monitoring settings
    ollama_host: str = (
        "http://localhost:11434"  # Default to localhost for local development
    )
    ollama_model: str = "qwen3"  # Default model for technical analysis

    # Specialized AI models for different tasks
    ollama_model_documentation: Optional[str] = (
        None  # Model for product documentation analysis
    )
    ollama_model_code_review: Optional[str] = None  # Model for code review analysis
    ollama_model_risk_assessment: Optional[str] = None  # Model for risk assessment

    # Timeout settings for AI operations
    ollama_timeout_base: int = 300  # Base timeout in seconds (5 minutes)
    ollama_timeout_connection: int = 30  # Connection test timeout
    ollama_timeout_embeddings: int = 60  # Embeddings timeout

    # Enhanced prompt settings
    use_enhanced_prompts: bool = True  # Enable enhanced contextual prompts
    enhanced_prompts_fallback: bool = (
        True  # Fall back to basic prompts if enhanced fail
    )

    check_interval: int = 300  # seconds (5 minutes - reasonable default)

    # Startup behavior settings
    cleanup_orphaned_documents: bool = False  # Clean up orphaned documents on startup (disabled by default to preserve data)

    # SVN server settings (for repository discovery)
    svn_server_url: Optional[str] = None
    svn_server_username: Optional[str] = None
    svn_server_password: Optional[str] = None
    svn_server_type: str = "auto"  # auto, visualsvn, apache, apache_dav, standard

    # Email settings (defaults to MailHog for development)
    smtp_host: str = "mailhog"
    smtp_port: int = 1025
    smtp_username: Optional[str] = None
    smtp_password: Optional[str] = None
    email_from: str = "reposense-ai@localhost"
    email_recipients: List[str] = field(
        default_factory=list
    )  # Global recipients (always get emails)

    # Output settings
    output_dir: str = "/app/data/output"
    generate_docs: bool = True
    send_emails: bool = True

    # Web interface settings
    web_enabled: bool = True
    web_port: int = 5000
    web_host: str = "0.0.0.0"
    web_secret_key: str = ""
    web_log_entries: int = 300  # Number of log entries to display in web interface

    # Log cleanup and rotation settings
    log_cleanup_max_size_mb: int = 50  # Manual cleanup trigger size in MB
    log_cleanup_lines_to_keep: int = (
        1000  # Number of recent lines to keep after cleanup
    )
    log_rotation_max_size_mb: int = 10  # Automatic rotation trigger size in MB
    log_rotation_backup_count: int = 5  # Number of backup files to keep

    # LDAP Sync Configuration
    ldap_sync_enabled: bool = False
    ldap_server: str = ""
    ldap_port: int = 389
    ldap_use_ssl: bool = False
    ldap_bind_dn: str = ""
    ldap_bind_password: str = ""
    ldap_user_base_dn: str = "ou=users,dc=company,dc=com"
    ldap_user_filter: str = "(objectClass=person)"
    ldap_sync_interval: int = 3600  # seconds (1 hour)

    # LDAP Attribute Mapping
    ldap_username_attr: str = "sAMAccountName"  # or "uid" for Unix LDAP
    ldap_email_attr: str = "mail"
    ldap_fullname_attr: str = "displayName"
    ldap_phone_attr: str = "telephoneNumber"  # or "mobile" for mobile numbers
    ldap_groups_attr: str = "memberOf"

    # Group to Role Mapping
    ldap_group_role_mapping: Dict[str, str] = field(
        default_factory=lambda: {
            "CN=RepoSense-Admins,OU=Groups,DC=company,DC=com": "ADMIN",
            "CN=RepoSense-Managers,OU=Groups,DC=company,DC=com": "MANAGER",
            "CN=Developers,OU=Groups,DC=company,DC=com": "DEVELOPER",
            "CN=RepoSense-Viewers,OU=Groups,DC=company,DC=com": "VIEWER",
        }
    )
    ldap_default_role: str = "VIEWER"  # Default role if no groups match

    # SQL Integration Configuration
    sql_config: SqlConfig = field(default_factory=SqlConfig)

    def __post_init__(self):
        if not self.web_secret_key:
            self.web_secret_key = secrets.token_hex(32)

    # Team management methods
    def add_team(self, team: Team) -> bool:
        """Add a team to the configuration"""
        if not any(t.id == team.id for t in self.teams):
            if not team.created_date:
                team.created_date = datetime.now().isoformat()
            self.teams.append(team)
            return True
        return False

    def get_team_by_id(self, team_id: str) -> Optional[Team]:
        """Get team by ID"""
        for team in self.teams:
            if team.id == team_id:
                return team
        return None

    def get_team_by_name(self, name: str) -> Optional[Team]:
        """Get team by name (case-insensitive)"""
        for team in self.teams:
            if team.name.lower() == name.lower():
                return team
        return None

    def remove_team(self, team_id: str) -> bool:
        """Remove a team"""
        for i, team in enumerate(self.teams):
            if team.id == team_id:
                del self.teams[i]
                return True
        return False
