#!/usr/bin/env python3
"""
Test script to verify MySQL test database connection and sample data.
Run this script to test the change request database setup.
"""

import sys
import re
from typing import List, Optional

try:
    import pymysql
except ImportError:
    print("❌ PyMySQL not installed. Install with: pip install PyMySQL")
    sys.exit(1)

# Database configuration
DB_CONFIG = {
    'host': 'localhost',  # Use localhost when running from host machine
    'port': 3306,
    'user': 'reposense',
    'password': 'reposense123',
    'database': 'change_requests',
    'charset': 'utf8mb4'
}

# Change request patterns for testing
CR_PATTERNS = [
    r'CR[#\-\s]*(\d+)',           # CR-123, CR#123, CR 123
    r'Change[#\-\s]*(\d+)',       # Change-123, Change#123
    r'Request[#\-\s]*(\d+)',      # Request-123, Request#123
    r'Ticket[#\-\s]*(\d+)',       # Ticket-123, Ticket#123
    r'Issue[#\-\s]*(\d+)',        # Issue-123, Issue#123
    r'#(\d+)',                    # #123 (generic)
]

def extract_change_request_numbers(commit_message: str) -> List[str]:
    """Extract change request numbers from commit message using patterns"""
    numbers = []
    for pattern in CR_PATTERNS:
        matches = re.findall(pattern, commit_message, re.IGNORECASE)
        numbers.extend(matches)
    return list(set(numbers))  # Remove duplicates

def test_database_connection():
    """Test basic database connection"""
    print("🔍 Testing database connection...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        print("✅ Database connection successful")
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT VERSION()")
            version = cursor.fetchone()
            print(f"📊 MySQL version: {version[0]}")
            
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def test_sample_data():
    """Test sample data retrieval"""
    print("\n🔍 Testing sample data...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        
        with connection.cursor() as cursor:
            # Count total change requests
            cursor.execute("SELECT COUNT(*) FROM change_requests")
            count = cursor.fetchone()[0]
            print(f"📊 Total change requests: {count}")
            
            # Show sample records
            cursor.execute("""
                SELECT number, title, priority, status, category 
                FROM change_requests 
                ORDER BY number 
                LIMIT 5
            """)
            
            print("\n📋 Sample change requests:")
            print("Number | Title | Priority | Status | Category")
            print("-" * 70)
            
            for row in cursor.fetchall():
                number, title, priority, status, category = row
                title_short = title[:30] + "..." if len(title) > 30 else title
                print(f"{number:6} | {title_short:33} | {priority:8} | {status:10} | {category}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ Sample data test failed: {e}")
        return False

def test_change_request_query(cr_number: str):
    """Test change request lookup query"""
    print(f"\n🔍 Testing change request lookup for CR #{cr_number}...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        
        with connection.cursor() as cursor:
            query = """
                SELECT id, number, title, description, priority, status, 
                       created_date, assigned_to, category, risk_level 
                FROM change_requests 
                WHERE number = %s
            """
            
            cursor.execute(query, (cr_number,))
            row = cursor.fetchone()
            
            if row:
                print("✅ Change request found:")
                print(f"   ID: {row[0]}")
                print(f"   Number: {row[1]}")
                print(f"   Title: {row[2]}")
                print(f"   Priority: {row[4]} | Status: {row[5]} | Risk: {row[9] or 'N/A'}")
                print(f"   Assigned to: {row[7] or 'Unassigned'}")
                print(f"   Category: {row[8] or 'N/A'}")
                print(f"   Description: {row[3][:100]}{'...' if len(row[3]) > 100 else ''}")
                return True
            else:
                print(f"❌ No change request found for number: {cr_number}")
                return False
        
        connection.close()
        
    except Exception as e:
        print(f"❌ Change request query failed: {e}")
        return False

def test_commit_message_parsing():
    """Test commit message parsing with various formats"""
    print("\n🔍 Testing commit message parsing...")
    
    test_messages = [
        "Fix authentication bug - CR#123",
        "Implement reporting feature for Change Request 124",
        "Database optimization - addresses Issue #125",
        "Security patch deployment - Ticket-127",
        "UI improvements - resolves CR 128",
        "Payment integration work - Change #129",
        "Multiple issues: CR-123, Issue #125, Ticket 127",
        "No change request mentioned in this commit",
    ]
    
    for message in test_messages:
        numbers = extract_change_request_numbers(message)
        if numbers:
            print(f"✅ '{message[:50]}...' → Found: {numbers}")
        else:
            print(f"ℹ️  '{message[:50]}...' → No CR numbers found")
    
    return True

def main():
    """Run all tests"""
    print("🚀 RepoSense AI - Change Request Database Test")
    print("=" * 50)
    
    # Test database connection
    if not test_database_connection():
        print("\n❌ Database connection failed. Please check:")
        print("   1. MySQL container is running: docker-compose ps mysql-test")
        print("   2. Port 3306 is accessible")
        print("   3. Database credentials are correct")
        sys.exit(1)
    
    # Test sample data
    if not test_sample_data():
        print("\n❌ Sample data test failed.")
        sys.exit(1)
    
    # Test specific change request lookup
    test_change_request_query("123")
    test_change_request_query("999")  # Should not exist
    
    # Test commit message parsing
    test_commit_message_parsing()
    
    print("\n🎉 All tests completed!")
    print("\n📝 Next steps:")
    print("   1. Access Adminer at http://localhost:8080")
    print("   2. Configure RepoSense AI with SQL integration settings")
    print("   3. Test with actual commit processing")

if __name__ == "__main__":
    main()
