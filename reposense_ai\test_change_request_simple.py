#!/usr/bin/env python3
"""
Simple integration test for Change Request LLM Integration

This test validates:
1. Change request pattern extraction from commit messages
2. LLM analysis with change request context
3. Document generation improvements
"""

import sys
import os
from datetime import datetime

# Add the reposense_ai directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'reposense_ai'))

from models import CommitInfo, ChangeRequestInfo
from ollama_client import OllamaClient
from config_manager import ConfigManager

class SimpleChangeRequestTest:
    """Simple test for change request LLM integration"""
    
    def __init__(self):
        self.ollama_client = None
        
    def setup_ollama_client(self):
        """Setup Ollama client for LLM testing"""
        try:
            config_manager = ConfigManager()
            config = config_manager.load_config()
            self.ollama_client = OllamaClient(config)
            
            if self.ollama_client.test_connection():
                print("✅ Ollama client connected successfully")
                return True
            else:
                print("⚠️  Ollama client connection failed - will test without LLM")
                return False
        except Exception as e:
            print(f"⚠️  Could not setup Ollama client: {e}")
            return False
    
    def create_test_commit_with_change_requests(self) -> CommitInfo:
        """Create a test commit with mock change request data"""
        # Create mock change requests
        change_requests = [
            ChangeRequestInfo(
                id="1",
                number="CR-2024-001",
                title="Implement Camera Control Interval Feature",
                description="Add interval setting to camera control to allow automatic picture taking at specified intervals. This will improve automation capabilities for surveillance applications.",
                priority="HIGH",
                status="APPROVED",
                created_date=datetime(2016, 6, 15, 10, 0, 0),
                assigned_to="fvaneijk",
                category="FEATURE",
                risk_level="MEDIUM"
            ),
            ChangeRequestInfo(
                id="2", 
                number="CR-2024-002",
                title="UI Enhancement - Start Button",
                description="Add start/stop button to main form for better user control of camera operations.",
                priority="MEDIUM",
                status="IN_PROGRESS",
                created_date=datetime(2016, 6, 18, 14, 0, 0),
                assigned_to="fvaneijk",
                category="UI_IMPROVEMENT",
                risk_level="LOW"
            )
        ]
        
        # Create commit with change requests
        commit = CommitInfo(
            revision="7",
            author="fvaneijk",
            date="2016-06-21 01:44:41",
            message="Add camera interval control and start button functionality - implements CR-2024-001 and CR-2024-002",
            changed_paths=[
                "/CaptureCam/AutoKams/Controls/CameraControl.Designer.cs",
                "/CaptureCam/AutoKams/MainForm.Designer.cs"
            ],
            diff="""@@ -15,6 +15,12 @@ namespace AutoKams.Controls
             this.btnStart = new System.Windows.Forms.Button();
             this.lblStatus = new System.Windows.Forms.Label();
             this.pnlCamera = new System.Windows.Forms.Panel();
+            this.lblInterval = new System.Windows.Forms.Label();
+            this.numInterval = new System.Windows.Forms.NumericUpDown();
+            // 
+            // lblInterval
+            // 
+            this.lblInterval.Text = "Interval:";
+            // 
+            // numInterval - for automatic picture taking
+            this.numInterval.Maximum = 3600;
+            this.numInterval.Minimum = 1;
+            this.numInterval.Value = 5;""",
            repository_id="visionApi",
            repository_name="visionApi",
            change_requests=change_requests
        )
        
        return commit
    
    def create_test_commit_without_change_requests(self) -> CommitInfo:
        """Create a test commit without change request data"""
        commit = CommitInfo(
            revision="8",
            author="<EMAIL>", 
            date="2016-06-22 10:15:30",
            message="Fix minor bug in camera initialization",
            changed_paths=["/CaptureCam/AutoKams/CameraManager.cs"],
            diff="""@@ -45,7 +45,7 @@ namespace AutoKams
             private void InitializeCamera()
             {
-                if (camera == null)
+                if (camera == null || !camera.IsConnected)
                 {
                     camera = new Camera();
                     camera.Connect();""",
            repository_id="visionApi",
            repository_name="visionApi",
            change_requests=[]
        )
        
        return commit
    
    def test_llm_with_change_requests(self) -> bool:
        """Test LLM analysis with change request context"""
        print("\n🤖 Testing LLM Analysis WITH Change Request Context")
        print("-" * 60)
        
        if not self.ollama_client:
            print("⚠️  Skipping LLM test - Ollama client not available")
            return True
        
        try:
            commit = self.create_test_commit_with_change_requests()
            
            print(f"📊 Commit: {commit.message}")
            print(f"📊 Change Requests: {len(commit.change_requests)}")
            for cr in commit.change_requests:
                print(f"   - {cr.number}: {cr.title} ({cr.priority})")
            
            # Generate documentation with change request context
            print("\n🔄 Generating documentation...")
            documentation = self.ollama_client.generate_documentation(commit)
            
            if documentation:
                print("✅ Documentation generated successfully")
                print(f"📄 Length: {len(documentation)} characters")
                
                # Check for change request context indicators
                context_indicators = [
                    "CR-2024-001",
                    "CR-2024-002", 
                    "change request",
                    "camera interval",
                    "start button",
                    "automation",
                    "surveillance",
                    "HIGH",
                    "MEDIUM"
                ]
                
                found_indicators = []
                for indicator in context_indicators:
                    if indicator.lower() in documentation.lower():
                        found_indicators.append(indicator)
                
                print(f"📊 Context indicators found: {len(found_indicators)}/{len(context_indicators)}")
                for indicator in found_indicators[:5]:  # Show first 5
                    print(f"   ✅ {indicator}")
                
                # Check for placeholder text (should be none)
                placeholders = [
                    "[Brief summary",
                    "[Analysis of how code changes align",
                    "[Detailed technical analysis]",
                    "[Impact assessment",
                    "[Risk evaluation"
                ]
                
                found_placeholders = [p for p in placeholders if p in documentation]
                
                if found_placeholders:
                    print(f"❌ Found {len(found_placeholders)} placeholder texts:")
                    for placeholder in found_placeholders:
                        print(f"   ❌ {placeholder}")
                    return False
                else:
                    print("✅ No placeholder text found")
                
                # Show preview
                print(f"\n📄 Documentation Preview:")
                print("-" * 40)
                preview = documentation[:400] + "..." if len(documentation) > 400 else documentation
                print(preview)
                
                return len(found_indicators) >= 3  # Expect at least 3 context indicators
            else:
                print("❌ No documentation generated")
                return False
                
        except Exception as e:
            print(f"❌ Error in LLM analysis with change requests: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_llm_without_change_requests(self) -> bool:
        """Test LLM analysis without change request context"""
        print("\n🤖 Testing LLM Analysis WITHOUT Change Request Context")
        print("-" * 60)
        
        if not self.ollama_client:
            print("⚠️  Skipping LLM test - Ollama client not available")
            return True
        
        try:
            commit = self.create_test_commit_without_change_requests()
            
            print(f"📊 Commit: {commit.message}")
            print(f"📊 Change Requests: {len(commit.change_requests)} (none)")
            
            # Generate documentation without change request context
            print("\n🔄 Generating documentation...")
            documentation = self.ollama_client.generate_documentation(commit)
            
            if documentation:
                print("✅ Documentation generated successfully")
                print(f"📄 Length: {len(documentation)} characters")
                
                # Should contain standard analysis but no change request context
                standard_indicators = [
                    "bug fix",
                    "camera",
                    "initialization", 
                    "null check",
                    "IsConnected"
                ]
                
                change_request_indicators = [
                    "CR-",
                    "change request",
                    "business impact",
                    "requirements"
                ]
                
                found_standard = [i for i in standard_indicators if i.lower() in documentation.lower()]
                found_cr = [i for i in change_request_indicators if i.lower() in documentation.lower()]
                
                print(f"📊 Standard analysis indicators: {len(found_standard)}/{len(standard_indicators)}")
                print(f"📊 Change request indicators: {len(found_cr)}/{len(change_request_indicators)}")
                
                # Show preview
                print(f"\n📄 Documentation Preview:")
                print("-" * 40)
                preview = documentation[:400] + "..." if len(documentation) > 400 else documentation
                print(preview)
                
                # Should have standard analysis but minimal change request context
                return len(found_standard) >= 2 and len(found_cr) <= 1
            else:
                print("❌ No documentation generated")
                return False
                
        except Exception as e:
            print(f"❌ Error in LLM analysis without change requests: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_all_tests(self) -> bool:
        """Run all tests"""
        print("🚀 Simple Change Request LLM Integration Test")
        print("=" * 70)
        
        try:
            # Setup
            ollama_available = self.setup_ollama_client()
            
            if not ollama_available:
                print("⚠️  Ollama not available - tests will be limited")
            
            # Run tests
            tests = [
                ("LLM with Change Requests", self.test_llm_with_change_requests),
                ("LLM without Change Requests", self.test_llm_without_change_requests)
            ]
            
            results = []
            for test_name, test_func in tests:
                try:
                    result = test_func()
                    results.append((test_name, result))
                except Exception as e:
                    print(f"❌ {test_name} failed with exception: {e}")
                    results.append((test_name, False))
            
            # Summary
            print("\n📊 Test Results Summary")
            print("=" * 70)
            
            passed = 0
            total = len(results)
            
            for test_name, result in results:
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"{status} {test_name}")
                if result:
                    passed += 1
            
            print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
            
            if passed == total:
                print("🎉 All tests passed! Change request LLM integration is working correctly.")
                return True
            else:
                print("⚠️  Some tests failed. Check the output above for details.")
                return False
                
        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            import traceback
            traceback.print_exc()
            return False

def main():
    """Run the integration test"""
    test = SimpleChangeRequestTest()
    success = test.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
