"""
Unit tests for LDAP Web Interface functionality.

This module tests the web interface endpoints for LDAP sync, connection testing,
and status reporting. Tests cover both API endpoints and web page rendering.
"""

import json
from typing import Any, Dict
from unittest.mock import MagicMock, Mock, patch

import pytest

# Import test utilities
from .test_utils import mock_config, mock_ldap_config, skip_if_no_network

# Import the modules under test
try:
    from ldap_sync_service import LDAP_AVAILABLE, LDAPSyncService
    from models import Config
    from user_database import UserDatabase
    from web_interface import WebInterface
except ImportError:
    WebInterface = None
    LDAPSyncService = None
    LDAP_AVAILABLE = False
    Config = None
    UserDatabase = None


@pytest.fixture
def mock_web_interface():
    """Create a mock web interface for testing."""
    if WebInterface is None:
        return None

    config = Mock(spec=Config)
    config.ldap_sync_enabled = True

    web_interface = Mock(spec=WebInterface)
    web_interface.config = config
    web_interface.monitor_service = Mock()
    web_interface.monitor_service.ldap_sync_service = Mock(spec=LDAPSyncService)

    return web_interface


@pytest.fixture
def mock_flask_app():
    """Create a mock Flask app for testing."""
    try:
        from flask import Flask

        app = Flask(__name__)
        app.config["TESTING"] = True
        return app
    except ImportError:
        return None


@pytest.mark.unit
class TestLDAPWebInterface:
    """Test cases for LDAP web interface functionality."""

    def test_ldap_web_interface_imports(self):
        """Test that LDAP web interface modules can be imported."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        assert WebInterface is not None, "WebInterface should be importable"
        assert LDAPSyncService is not None, "LDAPSyncService should be importable"

    def test_ldap_sync_page_route(self, mock_web_interface):
        """Test LDAP sync page route."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        # Mock the route handler
        mock_web_interface.ldap_sync_page = Mock(return_value="LDAP Sync Page")

        result = mock_web_interface.ldap_sync_page()
        assert result == "LDAP Sync Page"
        mock_web_interface.ldap_sync_page.assert_called_once()

    def test_ldap_status_api_endpoint(self, mock_web_interface):
        """Test LDAP status API endpoint."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        # Mock the status response
        mock_status = {
            "ldap_enabled": True,
            "last_sync": "2025-01-01T00:00:00Z",
            "total_users": 5,
            "ldap_users": 2,
            "sync_interval": 3600,
        }

        mock_web_interface.api_ldap_status = Mock(return_value=mock_status)

        result = mock_web_interface.api_ldap_status()
        assert result["ldap_enabled"] is True
        assert result["total_users"] == 5
        assert result["ldap_users"] == 2

    def test_ldap_test_connection_api_endpoint(self, mock_web_interface):
        """Test LDAP test connection API endpoint."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        # Mock successful connection test
        mock_response = {
            "success": True,
            "message": "LDAP connection successful to openldap:1389",
        }

        mock_web_interface.api_test_ldap_connection = Mock(return_value=mock_response)

        result = mock_web_interface.api_test_ldap_connection()
        assert result["success"] is True
        assert "LDAP connection successful" in result["message"]

    def test_ldap_test_connection_api_failure(self, mock_web_interface):
        """Test LDAP test connection API endpoint with failure."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        # Mock failed connection test
        mock_response = {"success": False, "message": "LDAP server unavailable"}

        mock_web_interface.api_test_ldap_connection = Mock(return_value=mock_response)

        result = mock_web_interface.api_test_ldap_connection()
        assert result["success"] is False
        assert "LDAP server unavailable" in result["message"]

    def test_ldap_sync_api_endpoint(self, mock_web_interface):
        """Test LDAP sync API endpoint."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        # Mock successful sync
        mock_response = {
            "success": True,
            "message": "LDAP sync completed: 2 created, 0 updated, 0 errors",
            "stats": {
                "created": 2,
                "updated": 0,
                "errors": 0,
                "skipped": 0,
                "total_ldap_users": 2,
            },
        }

        mock_web_interface.api_ldap_sync = Mock(return_value=mock_response)

        result = mock_web_interface.api_ldap_sync()
        assert result["success"] is True
        assert result["stats"]["created"] == 2
        assert result["stats"]["total_ldap_users"] == 2

    def test_ldap_sync_api_failure(self, mock_web_interface):
        """Test LDAP sync API endpoint with failure."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        # Mock failed sync
        mock_response = {
            "success": False,
            "message": "LDAP sync failed: Connection timeout",
            "stats": {
                "created": 0,
                "updated": 0,
                "errors": 1,
                "skipped": 0,
                "total_ldap_users": 0,
            },
        }

        mock_web_interface.api_ldap_sync = Mock(return_value=mock_response)

        result = mock_web_interface.api_ldap_sync()
        assert result["success"] is False
        assert "Connection timeout" in result["message"]
        assert result["stats"]["errors"] == 1

    def test_ldap_test_connection_with_form_data(self, mock_web_interface):
        """Test LDAP test connection with form data."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        # Mock form data processing
        form_data = {
            "ldap_server": "test-server",
            "ldap_port": "389",
            "ldap_bind_dn": "cn=test,dc=test,dc=com",
        }

        # Mock response with form data
        mock_response = {
            "success": True,
            "message": "LDAP connection successful to test-server:389",
        }

        mock_web_interface.api_test_ldap_connection = Mock(return_value=mock_response)

        result = mock_web_interface.api_test_ldap_connection()
        assert result["success"] is True
        assert "test-server" in result["message"]

        # Test that form data would be processed correctly
        assert form_data["ldap_server"] == "test-server"
        assert form_data["ldap_port"] == "389"

    def test_ldap_configuration_display(self, mock_web_interface, mock_ldap_config):
        """Test LDAP configuration display in web interface."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        # Mock configuration data for display
        config_display = {
            "server": mock_ldap_config.ldap_server,
            "port": mock_ldap_config.ldap_port,
            "use_ssl": mock_ldap_config.ldap_use_ssl,
            "base_dn": mock_ldap_config.ldap_user_base_dn,
            "username_attr": mock_ldap_config.ldap_username_attr,
            "email_attr": mock_ldap_config.ldap_email_attr,
        }

        assert config_display["server"] == "openldap"
        assert config_display["port"] == 1389
        assert config_display["use_ssl"] is False
        assert config_display["username_attr"] == "cn"
        assert config_display["email_attr"] == "mail"


@pytest.mark.unit
class TestLDAPWebInterfaceErrorHandling:
    """Test cases for LDAP web interface error handling."""

    def test_ldap_disabled_handling(self, mock_web_interface):
        """Test handling when LDAP is disabled."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        # Mock LDAP disabled configuration
        mock_web_interface.config.ldap_sync_enabled = False

        mock_response = {
            "success": False,
            "message": "LDAP sync is disabled in configuration",
        }

        mock_web_interface.api_ldap_status = Mock(return_value=mock_response)

        result = mock_web_interface.api_ldap_status()
        assert result["success"] is False
        assert "disabled" in result["message"]

    def test_ldap_library_missing_handling(self, mock_web_interface):
        """Test handling when LDAP library is missing."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        mock_response = {
            "success": False,
            "message": "LDAP library (ldap3) not available",
        }

        mock_web_interface.api_test_ldap_connection = Mock(return_value=mock_response)

        result = mock_web_interface.api_test_ldap_connection()
        assert result["success"] is False
        assert "ldap3" in result["message"]

    def test_ldap_service_exception_handling(self, mock_web_interface):
        """Test handling of LDAP service exceptions."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        # Mock service exception
        mock_web_interface.monitor_service.ldap_sync_service.test_connection.side_effect = Exception(
            "Service error"
        )

        mock_response = {
            "success": False,
            "message": "Error testing LDAP connection: Service error",
        }

        mock_web_interface.api_test_ldap_connection = Mock(return_value=mock_response)

        result = mock_web_interface.api_test_ldap_connection()
        assert result["success"] is False
        assert "Service error" in result["message"]

    def test_invalid_json_request_handling(self, mock_web_interface):
        """Test handling of invalid JSON requests."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        mock_response = {"success": False, "message": "Invalid JSON in request"}

        mock_web_interface.api_test_ldap_connection = Mock(return_value=mock_response)

        result = mock_web_interface.api_test_ldap_connection()
        assert result["success"] is False
        assert "Invalid JSON" in result["message"]


@pytest.mark.unit
class TestLDAPWebInterfaceTemplates:
    """Test cases for LDAP web interface templates and rendering."""

    def test_ldap_sync_template_context(self, mock_web_interface, mock_ldap_config):
        """Test LDAP sync template context data."""
        if WebInterface is None:
            pytest.skip("WebInterface not available")

        # Mock template context
        template_context = {
            "ldap_enabled": True,
            "ldap_server": mock_ldap_config.ldap_server,
            "ldap_port": mock_ldap_config.ldap_port,
            "ldap_use_ssl": mock_ldap_config.ldap_use_ssl,
            "ldap_user_base_dn": mock_ldap_config.ldap_user_base_dn,
            "ldap_username_attr": mock_ldap_config.ldap_username_attr,
            "ldap_email_attr": mock_ldap_config.ldap_email_attr,
            "ldap_fullname_attr": mock_ldap_config.ldap_fullname_attr,
            "ldap_phone_attr": mock_ldap_config.ldap_phone_attr,
            "ldap_groups_attr": mock_ldap_config.ldap_groups_attr,
        }

        assert template_context["ldap_enabled"] is True
        assert template_context["ldap_server"] == "openldap"
        assert template_context["ldap_port"] == 1389
        assert template_context["ldap_username_attr"] == "cn"

    def test_ldap_sync_template_status_display(self):
        """Test LDAP sync template status display."""
        # Mock status data for template
        status_data = {
            "last_sync": "2025-01-01T12:00:00Z",
            "total_users": 10,
            "ldap_users": 3,
            "sync_interval": 3600,
            "next_sync": "2025-01-01T13:00:00Z",
        }

        assert status_data["total_users"] == 10
        assert status_data["ldap_users"] == 3
        assert status_data["sync_interval"] == 3600

    def test_ldap_configuration_template_display(self, mock_ldap_config):
        """Test LDAP configuration template display."""
        # Mock configuration display data
        config_display = {
            "connection_settings": {
                "server": mock_ldap_config.ldap_server,
                "port": mock_ldap_config.ldap_port,
                "use_ssl": mock_ldap_config.ldap_use_ssl,
                "base_dn": mock_ldap_config.ldap_user_base_dn,
            },
            "attribute_mapping": {
                "username": mock_ldap_config.ldap_username_attr,
                "email": mock_ldap_config.ldap_email_attr,
                "fullname": mock_ldap_config.ldap_fullname_attr,
                "phone": mock_ldap_config.ldap_phone_attr,
                "groups": mock_ldap_config.ldap_groups_attr,
            },
        }

        assert config_display["connection_settings"]["server"] == "openldap"
        assert config_display["attribute_mapping"]["username"] == "cn"
        assert config_display["attribute_mapping"]["email"] == "mail"


@pytest.mark.integration
class TestLDAPWebInterfaceIntegration:
    """Integration tests for LDAP web interface."""

    @skip_if_no_network
    @pytest.mark.skipif(not LDAP_AVAILABLE, reason="LDAP library not available")
    def test_ldap_web_interface_full_workflow(self):
        """Test complete LDAP web interface workflow."""
        # This test would require a running web server and LDAP server
        pytest.skip("Integration test - requires running web server and LDAP server")

    @skip_if_no_network
    @pytest.mark.skipif(not LDAP_AVAILABLE, reason="LDAP library not available")
    def test_ldap_web_interface_user_session(self):
        """Test LDAP web interface with user sessions."""
        # This test would require session management testing
        pytest.skip("Integration test - requires session management setup")
