"""
Core unit tests for the DocumentService class.

This module tests the essential document service functionality:
- Document dataclass and conversion methods
- Service initialization and basic operations
- Document retrieval and management
- Model specialties and context calculations
- Error handling
"""

from datetime import datetime
from pathlib import Path
from unittest.mock import Mock, mock_open, patch

import pytest

# Import the modules under test
try:
    from document_database import DocumentRecord
    from document_service import Document, DocumentService

    IMPORTS_AVAILABLE = True
except ImportError:
    DocumentService = None
    Document = None
    DocumentRecord = None
    IMPORTS_AVAILABLE = False


def create_mock_service():
    """Helper function to create a DocumentService with mocked dependencies."""
    mock_unified_processor = Mock()
    mock_unified_processor.start = Mock()
    mock_unified_processor.stop = Mock()
    mock_unified_processor.scan_file_system = Mock()
    mock_unified_processor.get_stats = Mock(
        return_value={"processed": 10, "pending": 2, "errors": 1}
    )

    with (
        patch("document_service.DocumentDatabase") as mock_db,
        patch("document_service.CacheManager") as mock_cache,
    ):
        mock_db_instance = Mock()
        mock_db.return_value = mock_db_instance

        mock_cache_instance = Mock()
        mock_cache.return_value = mock_cache_instance

        service = DocumentService(
            output_dir="/test/output",
            db_path="/test/db.db",
            unified_processor=mock_unified_processor,
        )

        return service, mock_db_instance, mock_cache_instance, mock_unified_processor


@pytest.mark.unit
@pytest.mark.document_service
class TestDocumentCore:
    """Core test cases for Document dataclass."""

    def test_document_initialization(self):
        """Test Document initialization with required fields."""
        if Document is None:
            pytest.skip("Document not available")

        doc = Document(
            id="test_doc_1",
            repository_id="repo_123",
            repository_name="test_repo",
            revision=456,
            date=datetime(2025, 8, 24, 10, 30, 45),
            filename="test_doc.md",
            filepath="/app/data/output/test_doc.md",
            size=1024,
            author="test_author",
            commit_message="Test commit message",
        )

        assert doc.id == "test_doc_1"
        assert doc.repository_id == "repo_123"
        assert doc.repository_name == "test_repo"
        assert doc.revision == 456
        assert doc.filename == "test_doc.md"
        assert doc.size == 1024
        assert doc.author == "test_author"
        assert doc.commit_message == "Test commit message"

    def test_document_display_name(self):
        """Test Document display_name property."""
        if Document is None:
            pytest.skip("Document not available")

        doc = Document(
            id="test_doc_1",
            repository_id="repo_123",
            repository_name="test_repo",
            revision=456,
            date=datetime(2025, 8, 24, 10, 30, 45),
            filename="test_doc.md",
            filepath="/app/data/output/test_doc.md",
            size=1024,
            author="test_author",
            commit_message="Test commit message",
        )

        display_name = doc.display_name
        assert "Revision 456" in display_name
        assert "2025-08-24 10:30" in display_name

    def test_document_from_record(self):
        """Test Document.from_record class method."""
        if Document is None or DocumentRecord is None:
            pytest.skip("Required classes not available")

        # Create a mock DocumentRecord
        record = Mock(spec=DocumentRecord)
        record.id = "test_doc_1"
        record.repository_id = "repo_123"
        record.repository_name = "test_repo"
        record.revision = 456
        record.date = datetime(2025, 8, 24, 10, 30, 45)
        record.filename = "test_doc.md"
        record.filepath = "/app/data/output/test_doc.md"
        record.size = 1024
        record.author = "test_author"
        record.commit_message = "Test commit message"
        record.changed_paths = ["file1.py", "file2.py"]
        record.code_review_recommended = True
        record.code_review_priority = "HIGH"
        record.documentation_impact = True
        record.risk_level = "MEDIUM"
        record.file_modified_time = 1234567890.0
        record.processed_time = datetime(2025, 8, 24, 11, 0, 0)
        record.ai_model_used = "qwen3"
        record.risk_aggressiveness_used = "BALANCED"
        record.repository_url = "https://github.com/test/repo"
        record.repository_type = "git"
        # User feedback fields
        record.user_code_review_status = "approved"
        record.user_code_review_comments = "Looks good"
        record.user_code_review_reviewer = "reviewer1"
        record.user_code_review_date = datetime(2025, 8, 24, 12, 0, 0)
        record.user_documentation_rating = 4
        record.user_documentation_comments = "Good docs"
        record.user_documentation_updated_by = "user1"
        record.user_documentation_updated_date = datetime(2025, 8, 24, 12, 30, 0)
        record.user_risk_assessment_override = "LOW"
        record.user_risk_assessment_comments = "Override to low"
        record.user_risk_assessment_updated_by = "user2"
        record.user_risk_assessment_updated_date = datetime(2025, 8, 24, 13, 0, 0)
        record.user_documentation_input = "Additional docs"
        record.user_documentation_suggestions = "Improve examples"
        record.user_documentation_input_by = "user3"
        record.user_documentation_input_date = datetime(2025, 8, 24, 13, 30, 0)
        record.heuristic_context = {"test": "context"}

        doc = Document.from_record(record)

        assert doc.id == "test_doc_1"
        assert doc.repository_id == "repo_123"
        assert doc.repository_name == "test_repo"
        assert doc.revision == 456
        assert doc.changed_paths == ["file1.py", "file2.py"]
        assert doc.code_review_recommended is True
        assert doc.code_review_priority == "HIGH"
        assert doc.documentation_impact is True
        assert doc.risk_level == "MEDIUM"
        assert doc.ai_model_used == "qwen3"
        assert doc.risk_aggressiveness_used == "BALANCED"
        assert doc.user_code_review_status == "approved"
        assert doc.user_documentation_rating == 4
        assert doc.user_risk_assessment_override == "LOW"
        assert doc.user_documentation_input == "Additional docs"
        assert doc.heuristic_context == {"test": "context"}


@pytest.mark.unit
@pytest.mark.document_service
class TestDocumentServiceCore:
    """Core test cases for DocumentService class."""

    def test_service_initialization(self):
        """Test DocumentService initialization."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        assert service.output_dir == Path("/test/output")
        assert service.ollama_client is None
        assert service.config_manager is None
        assert service.unified_processor == mock_processor

    def test_get_documents_basic(self):
        """Test basic document retrieval."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        # Mock database response
        mock_record = Mock(spec=DocumentRecord)
        mock_record.id = "doc1"
        mock_record.repository_name = "test_repo"
        mock_db.get_documents.return_value = [mock_record]

        # Mock cache miss
        mock_cache.get.return_value = None

        documents = service.get_documents(limit=10)
        assert len(documents) == 1
        assert documents[0] == mock_record

        # Verify database was called
        mock_db.get_documents.assert_called_once()

    def test_get_document_count(self):
        """Test document count retrieval."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        mock_db.get_document_count.return_value = 42

        count = service.get_document_count()
        assert count == 42
        mock_db.get_document_count.assert_called_once()

    def test_get_processing_stats(self):
        """Test processing statistics retrieval."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        stats = service.get_processing_stats()

        assert stats["processed"] == 10
        assert stats["pending"] == 2
        assert stats["errors"] == 1
        mock_processor.get_stats.assert_called_once()

    def test_model_specialties(self):
        """Test model specialties functionality."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        # Test specific models
        specialties = service.get_model_specialties("qwen3")
        assert "document-processing" in specialties
        assert "multilingual" in specialties

        specialties = service.get_model_specialties("codellama")
        assert "code" in specialties
        assert "programming" in specialties

        # Test document processing capability
        assert service.is_model_good_at_document_processing("qwen3") is True
        assert service.is_model_good_at_document_processing("tinyllama") is False

        # Test processing scores
        score = service.get_document_processing_score("qwen3")
        assert score >= 85  # Should be high for document processing models

        score = service.get_document_processing_score("nomic-embed")
        assert score < 50  # Should be low for embedding models

    def test_force_rescan(self):
        """Test force rescan functionality."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        # Reset the mock to clear any calls from initialization
        mock_processor.scan_file_system.reset_mock()

        service.force_rescan()

        mock_processor.scan_file_system.assert_called_once_with(force_rescan=True)
        mock_cache.invalidate_namespace.assert_called_with("documents")

    def test_get_document_content_success(self):
        """Test successful document content retrieval."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        # Mock document record
        mock_record = Mock(spec=DocumentRecord)
        mock_record.filepath = "/test/path/doc.md"
        mock_db.get_document_by_id.return_value = mock_record

        with patch("builtins.open", mock_open(read_data="Test document content")):
            content = service.get_document_content("doc1")
            assert content == "Test document content"

    def test_get_document_content_not_found(self):
        """Test document content retrieval when document doesn't exist."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        mock_db.get_document_by_id.return_value = None

        content = service.get_document_content("nonexistent")
        assert content is None

    def test_get_available_authors(self):
        """Test getting available authors."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        mock_db.get_available_authors.return_value = ["author1", "author2", "author3"]

        authors = service.get_available_authors()
        assert authors == ["author1", "author2", "author3"]
        mock_db.get_available_authors.assert_called_with(None)

        # Test with repository filter
        authors = service.get_available_authors(repository_id="repo_123")
        mock_db.get_available_authors.assert_called_with("repo_123")

    def test_get_available_repositories(self):
        """Test getting available repositories."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        mock_db.get_available_repositories.return_value = [
            {"id": "repo1", "name": "Repository 1", "count": 10},
            {"id": "repo2", "name": "Repository 2", "count": 5},
        ]

        repositories = service.get_available_repositories()
        assert len(repositories) == 2
        assert repositories[0]["name"] == "Repository 1"
        assert repositories[1]["count"] == 5

    def test_get_document_record_by_id(self):
        """Test getting document record by ID."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        mock_record = Mock(spec=DocumentRecord)
        mock_record.id = "doc1"
        mock_db.get_document_by_id.return_value = mock_record

        record = service.get_document_record_by_id("doc1")
        assert record == mock_record
        mock_db.get_document_by_id.assert_called_once_with("doc1")

    def test_get_document_by_id_legacy(self):
        """Test legacy get_document_by_id method."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        mock_record = Mock(spec=DocumentRecord)
        mock_record.id = "doc1"
        mock_db.get_document_by_id.return_value = mock_record

        with patch.object(Document, "from_record") as mock_from_record:
            mock_doc = Mock()
            mock_from_record.return_value = mock_doc

            document = service.get_document_by_id("doc1")
            assert document == mock_doc
            mock_from_record.assert_called_once_with(mock_record)

            # Test with non-existent document
            mock_db.get_document_by_id.return_value = None
            document = service.get_document_by_id("nonexistent")
            assert document is None

    def test_scan_documents_legacy(self):
        """Test legacy scan_documents method."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        # Mock document records
        mock_record1 = Mock(spec=DocumentRecord)
        mock_record1.id = "doc1"
        mock_record2 = Mock(spec=DocumentRecord)
        mock_record2.id = "doc2"

        # Mock the get_documents method on the service itself
        with patch.object(service, "get_documents") as mock_get_documents:
            mock_get_documents.return_value = [mock_record1, mock_record2]

            with patch.object(Document, "from_record") as mock_from_record:
                mock_doc1 = Mock()
                mock_doc2 = Mock()
                mock_from_record.side_effect = [mock_doc1, mock_doc2]

                documents = service.scan_documents()

                assert len(documents) == 2
                assert documents[0] == mock_doc1
                assert documents[1] == mock_doc2

                # Should call from_record for each record
                assert mock_from_record.call_count == 2

                # Should call get_documents with no parameters
                mock_get_documents.assert_called_once_with()

    def test_get_cache_stats(self):
        """Test getting cache statistics."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        mock_cache.get_stats.return_value = {
            "hits": 150,
            "misses": 25,
            "hit_rate": 0.857,
        }

        stats = service.get_cache_stats()

        assert stats["hits"] == 150
        assert stats["misses"] == 25
        assert stats["hit_rate"] == 0.857
        mock_cache.get_stats.assert_called_once()

    def test_get_migration_status(self):
        """Test getting database migration status."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        mock_db.get_migration_status.return_value = {
            "current_version": "1.2.3",
            "migrations_applied": 5,
            "pending_migrations": 0,
        }

        status = service.get_migration_status()

        assert status["current_version"] == "1.2.3"
        assert status["migrations_applied"] == 5
        assert status["pending_migrations"] == 0
        mock_db.get_migration_status.assert_called_once()

    def test_clear_all_documents(self):
        """Test clearing all documents from database."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        service, mock_db, mock_cache, mock_processor = create_mock_service()

        mock_db.get_document_count.return_value = 15
        mock_db.clear_all_documents.return_value = True

        count = service.clear_all_documents()

        assert count == 15
        mock_db.get_document_count.assert_called_once()
        mock_db.clear_all_documents.assert_called_once()
        mock_cache.invalidate_namespace.assert_called_with("documents")

    def test_context_limit_calculations(self):
        """Test context limit calculations with various models."""
        if DocumentService is None:
            pytest.skip("DocumentService not available")

        mock_ollama = Mock()
        mock_config = Mock()
        mock_ollama.config = mock_config

        service, mock_db, mock_cache, mock_processor = create_mock_service()
        service.ollama_client = mock_ollama

        # Test various model context limits
        test_cases = [
            ("qwen3", 131072),
            ("llama3.1", 128000),
            ("mixtral", 32768),
            ("llama2", 4096),
        ]

        for model_name, expected_raw_limit in test_cases:
            mock_config.ollama_model = model_name

            # Test raw limit calculation
            raw_limits = service._get_model_raw_limits_dict()
            assert model_name in raw_limits
            assert raw_limits[model_name] == expected_raw_limit

            # Test usable context calculation
            usable = service._calculate_usable_context(expected_raw_limit)
            assert usable > 0
            assert usable < expected_raw_limit  # Should be less than raw limit

            # Test context limit retrieval
            limit = service._get_model_context_limit()
            assert limit > 0  # Should return a reasonable limit
