{% extends "base.html" %}

{% block title %}Repositories - RepoSense AI{% endblock %}

{% block extra_css %}
<style>
    /* Resizable modal styles */
    .modal-dialog-resizable {
        resize: both;
        overflow: auto;
        min-width: 600px;
        min-height: 400px;
        max-width: 95vw;
        max-height: 95vh;
    }

    .modal-dialog-resizable .modal-content {
        height: 100%;
        min-height: 400px;
        display: flex;
        flex-direction: column;
    }

    .modal-dialog-resizable .modal-body {
        flex: 1;
        overflow: hidden;
        display: flex;
        flex-direction: column;
    }

    .modal-dialog-resizable .modal-body .row {
        flex: 1;
        margin: 0;
    }

    .modal-dialog-resizable .modal-body .col-md-8,
    .modal-dialog-resizable .modal-body .col-md-4 {
        display: flex;
        flex-direction: column;
        padding: 0 15px;
    }

    .modal-dialog-resizable .card {
        flex: 1;
        display: flex;
        flex-direction: column;
        margin-bottom: 0;
    }

    .modal-dialog-resizable .card-body {
        flex: 1;
        overflow: hidden;
        padding: 0;
    }

    /* Resize handle indicator */
    .modal-dialog-resizable::after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 20px;
        height: 20px;
        background: linear-gradient(-45deg, transparent 30%, #999 30%, #999 40%, transparent 40%, transparent 60%, #999 60%, #999 70%, transparent 70%);
        cursor: nw-resize;
        pointer-events: none;
        opacity: 0.7;
        transition: opacity 0.2s;
    }

    .modal-dialog-resizable:hover::after {
        opacity: 1;
    }

    /* Scrollable areas */
    #file_browser,
    #selected_files {
        overflow-y: auto;
        flex: 1;
    }

    /* Remove fixed heights to allow flexible sizing */
    .modal-dialog-resizable #file_browser {
        max-height: none;
    }

    .modal-dialog-resizable #selected_files {
        max-height: none;
    }
</style>
{% endblock %}

{% block content %}
<style>
    /* Custom styles for repository management */
    .repo-card {
        transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    }

    .repo-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    }

    .status-group .card-header {
        cursor: pointer;
    }

    .status-group .card-header:hover {
        background-color: rgba(13, 202, 240, 0.9) !important;
    }

    .bulk-actions-bar {
        background: linear-gradient(135deg, #007bff, #0056b3);
        border-radius: 8px;
        padding: 15px;
        margin-bottom: 20px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .bulk-actions-bar.hidden {
        display: none;
    }

    .repo-checkbox {
        transform: scale(1.2);
        margin-right: 8px;
    }

    /* Repository status indicators */
    .repo-status-enabled {
        color: #28a745;
    }

    .repo-status-disabled {
        color: #dc3545;
    }

    .scan-status-completed {
        color: #28a745;
    }

    .scan-status-active {
        color: #007bff;
    }

    .scan-status-failed {
        color: #dc3545;
    }

    /* Enhanced spinner animations */
    .scan-status-in_progress .fa-spinner {
        animation: smooth-spin 1.5s linear infinite;
    }

    /* Custom smooth spinner animation */
    @keyframes smooth-spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    /* Enhanced progress spinner with pulsing effect */
    .scan-status-in_progress {
        position: relative;
    }

    .scan-status-in_progress::before {
        content: '';
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border-radius: 50%;
        background: linear-gradient(45deg, #007bff, #0056b3);
        opacity: 0.1;
        animation: pulse-glow 2s ease-in-out infinite;
        pointer-events: none;
    }

    @keyframes pulse-glow {

        0%,
        100% {
            opacity: 0.1;
            transform: scale(1);
        }

        50% {
            opacity: 0.3;
            transform: scale(1.05);
        }
    }

    /* Modern circular progress indicator */
    .progress-spinner {
        display: inline-block;
        width: 16px;
        height: 16px;
        border: 2px solid #e3e3e3;
        border-top: 2px solid #007bff;
        border-radius: 50%;
        animation: modern-spin 1s linear infinite;
        margin-right: 6px;
    }

    /* Large spinner for cards view */
    .progress-spinner-large {
        display: inline-block;
        width: 24px;
        height: 24px;
        border: 3px solid #e3e3e3;
        border-top: 3px solid #007bff;
        border-right: 3px solid #0056b3;
        border-radius: 50%;
        animation: modern-spin 1.2s linear infinite;
        margin-bottom: 8px;
        position: relative;
    }

    /* Add a subtle shadow effect to spinners */
    .progress-spinner,
    .progress-spinner-large {
        box-shadow: 0 2px 4px rgba(0, 123, 255, 0.1);
    }

    /* Smooth color transitions for progress indicators */
    .scan-status-in_progress {
        transition: all 0.3s ease-in-out;
    }

    /* Enhanced hover effects for progress elements */
    .scan-status-in_progress:hover .progress-spinner,
    .scan-status-in_progress:hover .progress-spinner-large {
        border-top-color: #0056b3;
        border-right-color: #007bff;
        animation-duration: 0.8s;
    }

    @keyframes modern-spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    /* Gradient spinner for cards view */
    .scan-status-in_progress .fa-lg {
        background: linear-gradient(45deg, #007bff, #0056b3, #007bff);
        background-size: 200% 200%;
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        animation: gradient-shift 2s ease-in-out infinite, smooth-spin 1.5s linear infinite;
    }

    @keyframes gradient-shift {

        0%,
        100% {
            background-position: 0% 50%;
        }

        50% {
            background-position: 100% 50%;
        }
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .bulk-actions-bar {
            padding: 10px;
        }

        .bulk-actions-bar .btn {
            margin-bottom: 5px;
        }
    }
</style>
<!-- Enhanced Filters and Controls -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow-sm border-info" style="border-width: 2px;">
            <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                <h6 class="mb-0"><i class="fas fa-filter me-2"></i> Repository Filters & Management</h6>
                <button class="btn btn-outline-light btn-sm" type="button" data-bs-toggle="collapse"
                    data-bs-target="#filtersCollapse" aria-expanded="false">
                    <i class="fas fa-chevron-down"></i> Toggle Filters
                </button>
            </div>
            <div class="collapse show" id="filtersCollapse">
                <div class="card-body">
                    <form method="GET" action="{{ url_for('repositories_page') }}" id="filtersForm">

                        <!-- Search & Filters Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="border rounded p-3 bg-light">
                                    <h6 class="text-muted mb-3">
                                        <i class="fas fa-search me-2"></i>Search & Filters
                                    </h6>
                                    <div class="row g-3">
                                        <!-- Search -->
                                        <div class="col-md-6">
                                            <label for="search" class="form-label fw-semibold">Search</label>
                                            <div class="input-group">
                                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                                <input type="text" class="form-control" id="search" name="search"
                                                    value="{{ search_query or '' }}"
                                                    placeholder="Search repositories, URLs, usernames...">
                                            </div>
                                        </div>

                                        <!-- Status Filter -->
                                        <div class="col-md-2">
                                            <label for="status" class="form-label fw-semibold">Status</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="">All</option>
                                                <option value="enabled" {% if status_filter=='enabled' %}selected{%
                                                    endif %}>Enabled</option>
                                                <option value="disabled" {% if status_filter=='disabled' %}selected{%
                                                    endif %}>Disabled</option>
                                            </select>
                                        </div>

                                        <!-- Type Filter -->
                                        <div class="col-md-2">
                                            <label for="type" class="form-label fw-semibold">Type</label>
                                            <select class="form-select" id="type" name="type">
                                                <option value="">All Types</option>
                                                {% for repo_type in available_types %}
                                                <option value="{{ repo_type }}" {% if type_filter==repo_type
                                                    %}selected{% endif %}>
                                                    {{ repo_type.upper() }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>

                                        <!-- Scan Status Filter -->
                                        <div class="col-md-2">
                                            <label for="scan_status" class="form-label fw-semibold">Scan Status</label>
                                            <select class="form-select" id="scan_status" name="scan_status">
                                                <option value="">All</option>
                                                {% for status in available_scan_statuses %}
                                                <option value="{{ status.value if status.value else status }}" {% if
                                                    scan_status_filter==(status.value if status.value else status)
                                                    %}selected{% endif %}>
                                                    {{ (status.value if status.value else status).replace('_', '
                                                    ').title() }}
                                                </option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Display Options Section -->
                        <div class="row mb-4">
                            <div class="col-12">
                                <div class="border rounded p-3 bg-light">
                                    <h6 class="text-muted mb-3">
                                        <i class="fas fa-cog me-2"></i>Display Options
                                    </h6>
                                    <div class="row g-3">
                                        <!-- Sort By -->
                                        <div class="col-md-3">
                                            <label for="sort_by" class="form-label fw-semibold">Sort By</label>
                                            <select class="form-select" id="sort_by" name="sort_by">
                                                <option value="name" {% if sort_by=='name' %}selected{% endif %}>Name
                                                </option>
                                                <option value="type" {% if sort_by=='type' %}selected{% endif %}>Type
                                                </option>
                                                <option value="status" {% if sort_by=='status' %}selected{% endif %}>
                                                    Status</option>
                                                <option value="last_revision" {% if sort_by=='last_revision'
                                                    %}selected{% endif %}>Last Revision</option>
                                                <option value="last_commit_date" {% if sort_by=='last_commit_date'
                                                    %}selected{% endif %}>Last Commit</option>
                                            </select>
                                        </div>

                                        <!-- Sort Order -->
                                        <div class="col-md-3">
                                            <label for="sort_order" class="form-label fw-semibold">Order</label>
                                            <select class="form-select" id="sort_order" name="sort_order">
                                                <option value="asc" {% if sort_order=='asc' %}selected{% endif %}>
                                                    Ascending</option>
                                                <option value="desc" {% if sort_order=='desc' %}selected{% endif %}>
                                                    Descending</option>
                                            </select>
                                        </div>

                                        <!-- View Mode -->
                                        <div class="col-md-3">
                                            <label for="view_mode" class="form-label fw-semibold">View Mode</label>
                                            <select class="form-select" id="view_mode" name="view_mode">
                                                <option value="table" {% if view_mode=='table' %}selected{% endif %}>
                                                    Table</option>
                                                <option value="cards" {% if view_mode=='cards' %}selected{% endif %}>
                                                    Cards</option>
                                                <option value="status_groups" {% if view_mode=='status_groups'
                                                    %}selected{% endif %}>By Status</option>
                                            </select>
                                        </div>

                                        <!-- Filter Actions -->
                                        <div class="col-md-3 d-flex align-items-end">
                                            <div class="btn-group w-100" role="group">
                                                <button type="submit" class="btn btn-primary">
                                                    <i class="fas fa-filter"></i> Apply
                                                </button>
                                                <a href="{{ url_for('repositories_page') }}"
                                                    class="btn btn-outline-secondary">
                                                    <i class="fas fa-times"></i> Clear
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Management Actions Section -->
                        <div class="row">
                            <div class="col-12">
                                <div class="border rounded p-3 bg-light">
                                    <h6 class="text-muted mb-3">
                                        <i class="fas fa-tools me-2"></i>Management Actions
                                    </h6>
                                    <div class="row g-2">
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-success w-100"
                                                onclick="toggleBulkMode()">
                                                <i class="fas fa-check-square"></i> Bulk Actions
                                            </button>
                                        </div>
                                        <div class="col-md-2">
                                            <a href="{{ url_for('repository_discovery_page') }}"
                                                class="btn btn-info w-100">
                                                <i class="fas fa-search"></i> Discover
                                            </a>
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-warning w-100"
                                                onclick="refreshRepositoryStatus()">
                                                <i class="fas fa-sync-alt"></i> Refresh
                                            </button>
                                        </div>
                                        <div class="col-md-2">
                                            <button type="button" class="btn btn-primary w-100" data-bs-toggle="modal"
                                                data-bs-target="#addRepositoryModal">
                                                <i class="fas fa-plus"></i> Add Repository
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Actions Bar (Hidden by default) -->
<div class="bulk-actions-bar hidden" id="bulkActionsBar">
    <form method="POST" action="{{ url_for('bulk_repository_action') }}" id="bulkActionsForm">
        <div class="row align-items-center">
            <div class="col-md-3">
                <div class="text-white">
                    <i class="fas fa-check-square me-2"></i>
                    <span id="selectedCount">0</span> repositories selected
                </div>
            </div>
            <div class="col-md-6">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-light btn-sm" onclick="submitBulkAction('enable')">
                        <i class="fas fa-play"></i> Enable
                    </button>
                    <button type="button" class="btn btn-light btn-sm" onclick="submitBulkAction('disable')">
                        <i class="fas fa-pause"></i> Disable
                    </button>
                    <button type="button" class="btn btn-light btn-sm" onclick="submitBulkAction('start_scan')">
                        <i class="fas fa-search"></i> Start Scan
                    </button>
                    <button type="button" class="btn btn-light btn-sm" onclick="submitBulkAction('stop_scan')">
                        <i class="fas fa-stop"></i> Stop Scan
                    </button>
                    <button type="button" class="btn btn-warning btn-sm" onclick="submitBulkAction('reset_status')">
                        <i class="fas fa-undo"></i> Reset Status
                    </button>
                    <button type="button" class="btn btn-danger btn-sm" onclick="submitBulkAction('delete')">
                        <i class="fas fa-trash"></i> Delete
                    </button>
                </div>
            </div>
            <div class="col-md-3 text-end">
                <button type="button" class="btn btn-outline-light btn-sm" onclick="selectAll()">
                    <i class="fas fa-check-double"></i> Select All
                </button>
                <button type="button" class="btn btn-outline-light btn-sm" onclick="clearSelection()">
                    <i class="fas fa-times"></i> Clear
                </button>
                <button type="button" class="btn btn-outline-light btn-sm" onclick="toggleBulkMode()">
                    <i class="fas fa-times"></i> Exit
                </button>
            </div>
        </div>
    </form>
</div>

<div class="page-header">
    <div class="d-flex justify-content-between align-items-center">
        <div>
            <h1 class="page-title">
                Repository Management
                {% if total_count > 0 %}
                <span class="badge bg-primary ms-2">{{ total_count }} found</span>
                {% if total_count != total_repositories %}
                <span class="badge bg-secondary ms-1">of {{ total_repositories }} total</span>
                {% endif %}
                {% endif %}
                <!-- View Mode Indicator -->
                <span class="badge bg-info ms-2">
                    {% if view_mode == 'cards' %}
                    <i class="fas fa-th-large me-1"></i>Cards View
                    {% elif view_mode == 'status_groups' %}
                    <i class="fas fa-layer-group me-1"></i>By Status
                    {% else %}
                    <i class="fas fa-table me-1"></i>Table View
                    {% endif %}
                </span>
                <!-- Real-time Updates Indicator -->
                <span class="badge bg-success ms-2" id="realTimeIndicator" style="display: none;">
                    <i class="fas fa-sync fa-spin me-1"></i>Live Updates
                </span>
            </h1>
            <p class="page-subtitle">Configure and manage your repositories</p>
        </div>
    </div>
</div>

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="fas fa-code-branch"></i>Repositories</h5>
            </div>
            <div class="card-body">
                {% if repositories %}
                <!-- Table View -->
                {% if view_mode == 'table' or not view_mode %}
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" class="repo-checkbox" id="selectAllCheckbox"
                                        onchange="toggleSelectAll()" style="display: none;">
                                    Name
                                </th>
                                <th>URL</th>
                                <th>Type</th>
                                <th>Branch Monitoring</th>
                                <th>Status</th>
                                <th>Scan Status</th>
                                <th>Last Revision</th>
                                <th>Commit Date</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for repo in repositories %}
                            <tr>
                                <td>
                                    <input type="checkbox" class="repo-checkbox" name="repo_select"
                                        value="{{ repo.id }}" onchange="updateSelectedCount()" style="display: none;">
                                    <strong>{{ repo.name }}</strong>
                                    <span class="badge bg-{{ 'primary' if repo.type == 'svn' else 'info' }} ms-2">{{
                                        repo.type.upper() }}</span>
                                    {% if not repo.enabled %}
                                    <span class="badge bg-secondary ms-1">Disabled</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <small class="text-muted">{{ repo.url }}</small>
                                </td>
                                <td>
                                    <span class="badge bg-{{ 'primary' if repo.type == 'svn' else 'info' }}">{{
                                        repo.type.upper() }}</span>
                                </td>

                                <td>
                                    <span class="repo-status-{{ 'enabled' if repo.enabled else 'disabled' }}">
                                        <i class="fas fa-{{ 'play' if repo.enabled else 'pause' }} me-1"></i>
                                        {{ 'Enabled' if repo.enabled else 'Disabled' }}
                                    </span>
                                </td>
                                <td>
                                    {% if repo.historical_scan and repo.historical_scan.scan_status is not none %}
                                    <span class="scan-status scan-status-{{ repo.historical_scan.scan_status.value }}"
                                        data-repo-id="{{ repo.id }}">
                                        {% if repo.historical_scan.scan_status.value == 'in_progress' %}
                                        <span class="progress-spinner"></span>
                                        {% else %}
                                        <i
                                            class="fas fa-{{ 'check' if repo.historical_scan.scan_status.value == 'completed' else 'exclamation-triangle' }} me-1"></i>
                                        {% endif %}
                                        {{ repo.historical_scan.scan_status.value.replace('_', ' ').title() }}
                                        {% if repo.historical_scan.scan_status.value == 'in_progress' and
                                        repo.historical_scan.processed_revisions is not none and
                                        repo.historical_scan.total_revisions %}
                                        <br><small class="scan-progress text-muted">{{
                                            repo.historical_scan.processed_revisions }}/{{
                                            repo.historical_scan.total_revisions }}</small>
                                        {% endif %}
                                    </span>
                                    {% else %}
                                    <span class="text-muted scan-status" data-repo-id="{{ repo.id }}">Not
                                        configured</span>
                                    {% endif %}
                                </td>
                                <td>
                                    <span class="badge bg-info">{{ repo.last_revision or 0 }}</span>
                                </td>
                                <td>
                                    {% if repo.last_commit_date %}
                                    <small class="text-muted">
                                        <i class="fas fa-code-branch me-1"></i>
                                        {{ repo.last_commit_date.strftime('%Y-%m-%d %H:%M') }}
                                    </small>
                                    {% else %}
                                    <small class="text-muted">Unknown</small>
                                    {% endif %}
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button class="btn btn-sm btn-outline-primary" data-repo-id="{{ repo.id }}"
                                            data-repo-name="{{ repo.name }}" data-repo-url="{{ repo.url }}"
                                            data-repo-username="{{ repo.username or '' }}"
                                            data-repo-enabled="{{ repo.enabled|lower }}"
                                            data-repo-email="{{ (repo.email_recipients or [])|join(', ') }}"
                                            data-repo-docs="{{ (repo.product_documentation_files or [])|join('\n') }}"
                                            data-repo-risk-aggressiveness="{{ repo.risk_aggressiveness or 'BALANCED' }}"
                                            data-repo-risk-description="{{ repo.risk_description or '' }}"
                                            onclick="editRepositoryFromData(this)" title="Edit Repository">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <a href="{{ url_for('historical_scan_page', repo_id=repo.id) }}"
                                            class="btn btn-sm btn-outline-info" title="Historical Scan">
                                            <i class="fas fa-history"></i>
                                        </a>
                                        <button class="btn btn-sm btn-outline-warning"
                                            onclick="manageRepositoryNotifications('{{ repo.id }}', '{{ repo.name }}')"
                                            title="Manage User Notifications">
                                            <i class="fas fa-users"></i>
                                        </button>
                                        <form method="POST" action="{{ url_for('delete_repository', repo_id=repo.id) }}"
                                            style="display: inline;"
                                            onsubmit="return confirm('Are you sure you want to delete repository \'{{ repo.name }}\'?')">
                                            <button type="submit" class="btn btn-sm btn-outline-danger"
                                                title="Delete Repository">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% endif %}

                <!-- Cards View -->
                {% if view_mode == 'cards' %}
                <div class="row">
                    {% for repo in repositories %}
                    <div class="col-md-6 col-lg-4 mb-4">
                        <div class="card h-100 shadow-sm repo-card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <div>
                                    <input type="checkbox" class="repo-checkbox" name="repo_select"
                                        value="{{ repo.id }}" onchange="updateSelectedCount()" style="display: none;">
                                    <strong>{{ repo.name }}</strong>
                                </div>
                                <span class="badge bg-{{ 'primary' if repo.type == 'svn' else 'info' }}">{{
                                    repo.type.upper() }}</span>
                            </div>
                            <div class="card-body">
                                <p class="card-text">
                                    <small class="text-muted">{{ repo.url }}</small>
                                </p>
                                <div class="row text-center mb-3">
                                    <div class="col-6">
                                        <div class="repo-status-{{ 'enabled' if repo.enabled else 'disabled' }}">
                                            <i class="fas fa-{{ 'play' if repo.enabled else 'pause' }} fa-lg"></i>
                                            <br><small>{{ 'Enabled' if repo.enabled else 'Disabled' }}</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        {% if repo.historical_scan and repo.historical_scan.scan_status is not none %}
                                        <div class="scan-status scan-status-{{ repo.historical_scan.scan_status.value }}"
                                            data-repo-id="{{ repo.id }}">
                                            {% if repo.historical_scan.scan_status.value == 'in_progress' %}
                                            <div class="progress-spinner-large"></div>
                                            {% else %}
                                            <i
                                                class="fas fa-{{ 'check' if repo.historical_scan.scan_status.value == 'completed' else 'exclamation-triangle' }} fa-lg"></i>
                                            {% endif %}
                                            <br><small>{{ repo.historical_scan.scan_status.value.replace('_', '
                                                ').title() }}</small>
                                        </div>
                                        {% else %}
                                        <div class="text-muted scan-status" data-repo-id="{{ repo.id }}">
                                            <i class="fas fa-question fa-lg"></i>
                                            <br><small>No Scan</small>
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                                <div class="text-center mb-3">
                                    <span class="badge bg-info me-2">Rev {{ repo.last_revision or 0 }}</span>
                                    {% if repo.last_commit_date %}
                                    <small class="text-muted">{{ repo.last_commit_date.strftime('%Y-%m-%d') }}</small>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="card-footer d-flex justify-content-between">
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary" data-repo-id="{{ repo.id }}"
                                        data-repo-name="{{ repo.name }}" data-repo-url="{{ repo.url }}"
                                        data-repo-username="{{ repo.username or '' }}"
                                        data-repo-enabled="{{ repo.enabled|lower }}"
                                        data-repo-email="{{ (repo.email_recipients or [])|join(', ') }}"
                                        data-repo-docs="{{ (repo.product_documentation_files or [])|join('\n') }}"
                                        data-repo-risk-aggressiveness="{{ repo.risk_aggressiveness or 'BALANCED' }}"
                                        data-repo-risk-description="{{ repo.risk_description or '' }}"
                                        onclick="editRepositoryFromData(this)" title="Edit Repository">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <a href="{{ url_for('historical_scan_page', repo_id=repo.id) }}"
                                        class="btn btn-outline-info" title="Historical Scan">
                                        <i class="fas fa-history"></i>
                                    </a>
                                    <form method="POST" action="{{ url_for('delete_repository', repo_id=repo.id) }}"
                                        style="display: inline;"
                                        onsubmit="return confirm('Are you sure you want to delete repository \'{{ repo.name }}\'?')">
                                        <button type="submit" class="btn btn-outline-danger" title="Delete Repository">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
                {% endif %}

                <!-- Status Groups View -->
                {% if view_mode == 'status_groups' %}
                {% set enabled_repos = repositories | selectattr('enabled') | list %}
                {% set disabled_repos = repositories | rejectattr('enabled') | list %}

                <!-- Enabled Repositories -->
                {% if enabled_repos %}
                <div class="mb-4 status-group">
                    <div class="card shadow-sm">
                        <div class="card-header bg-success text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-play me-2"></i>Enabled Repositories
                                <span class="badge bg-light text-success ms-2">{{ enabled_repos | length }}</span>
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>
                                                <input type="checkbox" class="repo-checkbox"
                                                    onchange="toggleGroupSelection('enabled', this.checked)"
                                                    style="display: none;">
                                                Name
                                            </th>
                                            <th>Type</th>
                                            <th>Branch Monitoring</th>
                                            <th>Scan Status</th>
                                            <th>Last Revision</th>
                                            <th>Last Commit</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for repo in enabled_repos %}
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="repo-checkbox" name="repo_select"
                                                    value="{{ repo.id }}" onchange="updateSelectedCount()"
                                                    style="display: none;">
                                                <strong>{{ repo.name }}</strong>
                                            </td>
                                            <td><span
                                                    class="badge bg-{{ 'primary' if repo.type == 'svn' else 'info' }}">{{
                                                    repo.type.upper() }}</span></td>

                                            <td>
                                                {% if repo.historical_scan and repo.historical_scan.scan_status is not
                                                none %}
                                                <span
                                                    class="scan-status scan-status-{{ repo.historical_scan.scan_status.value }}"
                                                    data-repo-id="{{ repo.id }}">
                                                    {% if repo.historical_scan.scan_status.value == 'in_progress' %}
                                                    <span class="progress-spinner"></span>
                                                    {% else %}
                                                    <i
                                                        class="fas fa-{{ 'check' if repo.historical_scan.scan_status.value == 'completed' else 'exclamation-triangle' }} me-1"></i>
                                                    {% endif %}
                                                    {{ repo.historical_scan.scan_status.value.replace('_', ' ').title()
                                                    }}
                                                </span>
                                                {% else %}
                                                <span class="text-muted scan-status" data-repo-id="{{ repo.id }}">Not
                                                    configured</span>
                                                {% endif %}
                                            </td>
                                            <td><span class="badge bg-info">{{ repo.last_revision or 0 }}</span></td>
                                            <td>
                                                {% if repo.last_commit_date %}
                                                <small class="text-muted">{{ repo.last_commit_date.strftime('%Y-%m-%d
                                                    %H:%M') }}</small>
                                                {% else %}
                                                <small class="text-muted">Unknown</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" data-repo-id="{{ repo.id }}"
                                                        data-repo-name="{{ repo.name }}" data-repo-url="{{ repo.url }}"
                                                        data-repo-username="{{ repo.username or '' }}"
                                                        data-repo-enabled="{{ repo.enabled|lower }}"
                                                        data-repo-email="{{ (repo.email_recipients or [])|join(', ') }}"
                                                        data-repo-docs="{{ (repo.product_documentation_files or [])|join('\n') }}"
                                                        data-repo-risk-aggressiveness="{{ repo.risk_aggressiveness or 'BALANCED' }}"
                                                        data-repo-risk-description="{{ repo.risk_description or '' }}"
                                                        onclick="editRepositoryFromData(this)" title="Edit Repository">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <a href="{{ url_for('historical_scan_page', repo_id=repo.id) }}"
                                                        class="btn btn-outline-info" title="Historical Scan">
                                                        <i class="fas fa-history"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <!-- Disabled Repositories -->
                {% if disabled_repos %}
                <div class="mb-4 status-group">
                    <div class="card shadow-sm">
                        <div class="card-header bg-secondary text-white">
                            <h5 class="mb-0">
                                <i class="fas fa-pause me-2"></i>Disabled Repositories
                                <span class="badge bg-light text-secondary ms-2">{{ disabled_repos | length }}</span>
                            </h5>
                        </div>
                        <div class="card-body p-0">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead class="table-light">
                                        <tr>
                                            <th>
                                                <input type="checkbox" class="repo-checkbox"
                                                    onchange="toggleGroupSelection('disabled', this.checked)"
                                                    style="display: none;">
                                                Name
                                            </th>
                                            <th>Type</th>
                                            <th>Branch Monitoring</th>
                                            <th>Last Revision</th>
                                            <th>Last Commit</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for repo in disabled_repos %}
                                        <tr class="table-secondary">
                                            <td>
                                                <input type="checkbox" class="repo-checkbox" name="repo_select"
                                                    value="{{ repo.id }}" onchange="updateSelectedCount()"
                                                    style="display: none;">
                                                <strong>{{ repo.name }}</strong>
                                            </td>
                                            <td><span
                                                    class="badge bg-{{ 'primary' if repo.type == 'svn' else 'info' }}">{{
                                                    repo.type.upper() }}</span></td>

                                            <td><span class="badge bg-info">{{ repo.last_revision or 0 }}</span></td>
                                            <td>
                                                {% if repo.last_commit_date %}
                                                <small class="text-muted">{{ repo.last_commit_date.strftime('%Y-%m-%d
                                                    %H:%M') }}</small>
                                                {% else %}
                                                <small class="text-muted">Unknown</small>
                                                {% endif %}
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" data-repo-id="{{ repo.id }}"
                                                        data-repo-name="{{ repo.name }}" data-repo-url="{{ repo.url }}"
                                                        data-repo-username="{{ repo.username or '' }}"
                                                        data-repo-enabled="{{ repo.enabled|lower }}"
                                                        data-repo-email="{{ (repo.email_recipients or [])|join(', ') }}"
                                                        data-repo-docs="{{ (repo.product_documentation_files or [])|join('\n') }}"
                                                        data-repo-risk-aggressiveness="{{ repo.risk_aggressiveness or 'BALANCED' }}"
                                                        data-repo-risk-description="{{ repo.risk_description or '' }}"
                                                        onclick="editRepositoryFromData(this)" title="Edit Repository">
                                                        <i class="fas fa-edit"></i>
                                                    </button>
                                                    <a href="{{ url_for('historical_scan_page', repo_id=repo.id) }}"
                                                        class="btn btn-outline-info" title="Historical Scan">
                                                        <i class="fas fa-history"></i>
                                                    </a>
                                                </div>
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}
                {% endif %}

                {% else %}
                <div class="text-center py-5">
                    <i class="fas fa-code-branch fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">No repositories found</h5>
                    <p class="text-muted">
                        {% if search_query or status_filter or type_filter or scan_status_filter %}
                        Try adjusting your filters or <a href="{{ url_for('repositories_page') }}">clear all
                            filters</a>.
                        {% else %}
                        Add your first repository to start monitoring.
                        {% endif %}
                    </p>
                    <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addRepositoryModal">
                        <i class="fas fa-plus"></i> Add Repository
                    </button>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Add Repository Modal -->
<div class="modal fade" id="addRepositoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Add Repository</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ url_for('add_repository') }}">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="add_name" class="form-label">Repository Name *</label>
                        <input type="text" class="form-control" id="add_name" name="name" required>
                        <div class="form-text">Choose a unique name for this repository</div>
                    </div>
                    <div class="mb-3">
                        <label for="add_url" class="form-label">Repository URL *</label>
                        <input type="url" class="form-control" id="add_url" name="url" required>
                        <div class="form-text">Example: https://svn.example.com/repo (must be unique)</div>
                    </div>
                    <div class="mb-3">
                        <label for="add_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="add_username" name="username"
                            autocomplete="username">
                        <div class="form-text">Leave empty for anonymous access</div>
                    </div>
                    <div class="mb-3">
                        <label for="add_password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="add_password" name="password"
                            autocomplete="current-password">
                    </div>


                    <div class="mb-3">
                        <label for="add_email_recipients" class="form-label">Email Recipients</label>
                        <textarea class="form-control" id="add_email_recipients" name="email_recipients" rows="3"
                            placeholder="<EMAIL>, <EMAIL>"></textarea>
                        <div class="form-text">Repository-specific email recipients (comma-separated). These will be
                            added to the global recipients.</div>
                    </div>
                    <div class="mb-3">
                        <label for="add_risk_aggressiveness" class="form-label">Risk Assessment Aggressiveness</label>
                        <select class="form-select" id="add_risk_aggressiveness" name="risk_aggressiveness">
                            <option value="CONSERVATIVE">Conservative - High scrutiny for stable/legacy systems
                                (stricter thresholds)</option>
                            <option value="BALANCED" selected>Balanced - Standard risk assessment for active development
                            </option>
                            <option value="AGGRESSIVE">Aggressive - Tolerant of changes in fast-moving development
                            </option>
                            <option value="VERY_AGGRESSIVE">Very Aggressive - Most tolerant, suitable for
                                experimental/prototype code</option>
                        </select>
                        <div class="form-text">
                            <i class="fas fa-info-circle text-info"></i>
                            Choose based on your development context - Conservative is strictest, Very Aggressive is
                            most tolerant.
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="add_risk_description" class="form-label">Risk Assessment Context</label>
                        <textarea class="form-control" id="add_risk_description" name="risk_description" rows="2"
                            placeholder="e.g., 'Legacy billing system - Conservative mode for careful change review' or 'Dev sandbox - Very Aggressive to reduce noise'"></textarea>
                        <div class="form-text">Document why this sensitivity level fits this repository's importance and
                            development pace.</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="add_enabled" name="enabled" checked>
                        <label class="form-check-label" for="add_enabled">
                            Enable monitoring for this repository
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Add Repository</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Repository Modal -->
<div class="modal fade" id="editRepositoryModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Edit Repository</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" id="editRepositoryForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_name" class="form-label">Repository Name *</label>
                        <input type="text" class="form-control" id="edit_name" name="name" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_url" class="form-label">Repository URL *</label>
                        <input type="url" class="form-control" id="edit_url" name="url" required>
                    </div>
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Username</label>
                        <input type="text" class="form-control" id="edit_username" name="username"
                            autocomplete="username">
                    </div>
                    <div class="mb-3">
                        <label for="edit_password" class="form-label">Password</label>
                        <input type="password" class="form-control" id="edit_password" name="password"
                            autocomplete="current-password">
                        <div class="form-text">Leave empty to keep current password</div>
                    </div>


                    <div class="mb-3">
                        <label for="edit_email_recipients" class="form-label">Email Recipients</label>
                        <textarea class="form-control" id="edit_email_recipients" name="email_recipients" rows="3"
                            placeholder="<EMAIL>, <EMAIL>"></textarea>
                        <div class="form-text">Repository-specific email recipients (comma-separated). These will be
                            added to the global recipients.</div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_product_documentation_files" class="form-label">Product Documentation
                            Files</label>
                        <div class="input-group">
                            <textarea class="form-control" id="edit_product_documentation_files"
                                name="product_documentation_files" rows="4"
                                placeholder="README.md&#10;docs/user-guide.md&#10;docs/api-reference.md&#10;CHANGELOG.md"></textarea>
                            <button class="btn btn-outline-secondary" type="button" id="browse_docs_btn"
                                onclick="openDocumentBrowser()">
                                <i class="fas fa-folder-open"></i> Browse
                            </button>
                        </div>
                        <div class="form-text">
                            <i class="fas fa-info-circle text-info"></i>
                            Specify which files contain user-facing product documentation (one per line).
                            These files will be referenced by AI when analyzing documentation impact.
                            <br><strong>Supported formats:</strong> Markdown (.md), Text (.txt), HTML (.html), PDF
                            (.pdf), Microsoft Word (.doc, .docx), RTF (.rtf), OpenDocument (.odt)
                            <br><strong>Examples:</strong> README.md, docs/user-guide.md, API.md, CHANGELOG.md,
                            ProductGuide.docx
                            <br><strong>Tip:</strong> Use the Browse button to explore the repository and select files
                            visually.
                            <br><small class="text-success"><i class="fas fa-check-circle"></i> Microsoft Word documents
                                (.doc, .docx) are fully supported with automatic text extraction</small>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_risk_aggressiveness" class="form-label">Risk Assessment Aggressiveness</label>
                        <select class="form-select" id="edit_risk_aggressiveness" name="risk_aggressiveness">
                            <option value="CONSERVATIVE">Conservative - High scrutiny for stable/legacy systems
                                (stricter thresholds)</option>
                            <option value="BALANCED" selected>Balanced - Standard risk assessment for active development
                            </option>
                            <option value="AGGRESSIVE">Aggressive - Tolerant of changes in fast-moving development
                            </option>
                            <option value="VERY_AGGRESSIVE">Very Aggressive - Most tolerant, suitable for
                                experimental/prototype code</option>
                        </select>
                        <div class="form-text">
                            <i class="fas fa-info-circle text-info"></i>
                            Controls risk assessment sensitivity based on your development context:
                            <br><strong>Conservative:</strong> Strict thresholds - any significant change to stable
                            systems gets high attention
                            <br><strong>Balanced:</strong> Standard thresholds - good default for most active
                            development
                            <br><strong>Aggressive:</strong> Relaxed thresholds - allows more changes without triggering
                            high-risk alerts
                            <br><strong>Very Aggressive:</strong> Most relaxed - suitable for rapid prototyping or
                            experimental code
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="edit_risk_description" class="form-label">Risk Assessment Context</label>
                        <textarea class="form-control" id="edit_risk_description" name="risk_description" rows="2"
                            placeholder="e.g., 'Production payment system - using Conservative for maximum scrutiny' or 'Experimental ML features - using Very Aggressive to avoid alert fatigue'"></textarea>
                        <div class="form-text">Document why this sensitivity level is appropriate for this codebase's
                            role and development stage.</div>
                    </div>
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="edit_enabled" name="enabled">
                        <label class="form-check-label" for="edit_enabled">
                            Enable monitoring for this repository
                        </label>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Update Repository</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Document Browser Modal -->
<div class="modal fade" id="documentBrowserModal" tabindex="-1" aria-labelledby="documentBrowserModalLabel"
    aria-hidden="true">
    <div class="modal-dialog modal-lg modal-dialog-resizable">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="documentBrowserModalLabel">
                    <i class="fas fa-folder-open"></i> Browse Repository Documentation
                    <small class="text-muted ms-2"
                        title="Drag the bottom-right corner to resize this dialog. Use Ctrl+1/2/3/0 for quick sizing.">
                        <i class="fas fa-expand-arrows-alt"></i> Resizable
                    </small>
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header">
                                <div class="d-flex justify-content-between align-items-center">
                                    <h6 class="mb-0">
                                        <i class="fas fa-folder"></i> Repository Files
                                        <span id="current_path" class="text-muted ms-2">/</span>
                                    </h6>
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="filter_docs" checked>
                                        <label class="form-check-label" for="filter_docs"
                                            title="Show only documentation files">
                                            <i class="fas fa-filter"></i> Docs Only
                                        </label>
                                    </div>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div id="loading_indicator" class="text-center p-4">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <div class="mt-2">Loading repository files...</div>
                                </div>
                                <div id="file_browser" class="list-group list-group-flush" style="display: none;">
                                    <!-- File list will be populated here -->
                                </div>
                                <div id="error_message" class="alert alert-danger m-3" style="display: none;">
                                    <!-- Error messages will be shown here -->
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-alt"></i> Selected Files
                                    <span id="selected_count" class="badge bg-primary ms-2">0</span>
                                </h6>
                            </div>
                            <div class="card-body p-0">
                                <div id="selected_files" class="list-group list-group-flush">
                                    <div class="list-group-item text-muted text-center">
                                        <i class="fas fa-info-circle"></i>
                                        <br>Select documentation files from the repository browser
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="applySelectedFiles()">
                    <i class="fas fa-check"></i> Apply Selection
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Bulk Scan Configuration Modal -->
<div class="modal fade" id="bulkScanModal" tabindex="-1" aria-labelledby="bulkScanModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="bulkScanModalLabel">
                    <i class="fas fa-search me-2"></i>Bulk Historical Scan Configuration
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>
                    Configure scan parameters for <span id="selectedRepoCount">0</span> selected repositories.
                    These settings will be applied to all selected repositories.
                </div>

                <form id="bulkScanForm">
                    <!-- Scan Method -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Scan Method</h6>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="scan_method" id="scan_by_revision"
                                value="revision" checked>
                            <label class="form-check-label" for="scan_by_revision">
                                <strong>Scan by Revision Range</strong>
                                <small class="text-muted d-block">Specify start and end revision numbers</small>
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="radio" name="scan_method" id="scan_by_date"
                                value="date">
                            <label class="form-check-label" for="scan_by_date">
                                <strong>Scan by Date Range</strong>
                                <small class="text-muted d-block">Specify start and end dates</small>
                            </label>
                        </div>
                    </div>

                    <!-- Revision Range Settings -->
                    <div id="revision_settings" class="mb-4">
                        <h6 class="fw-bold mb-3">Revision Range</h6>

                        <!-- Quick Selection Options -->
                        <div class="mb-3">
                            <div class="row align-items-end">
                                <div class="col-md-6">
                                    <div class="btn-group-sm" role="group" aria-label="Quick revision selection">
                                        <button type="button" class="btn btn-outline-secondary btn-sm"
                                            id="bulk-select-all-revisions" title="Select all available revisions">
                                            <i class="fas fa-list"></i> All
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm"
                                            id="bulk-select-recent-revisions" title="Select last 10 revisions">
                                            <i class="fas fa-clock"></i> Last 10
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm"
                                            id="bulk-select-last-25" title="Select last 25 revisions">
                                            <i class="fas fa-history"></i> Last 25
                                        </button>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="input-group input-group-sm">
                                        <span class="input-group-text">Last</span>
                                        <input type="number" class="form-control" id="bulk-custom-revision-count"
                                            min="1" max="1000" placeholder="50"
                                            title="Enter number of recent revisions to scan">
                                        <span class="input-group-text">revisions</span>
                                        <button type="button" class="btn btn-outline-primary btn-sm"
                                            id="bulk-select-custom-count"
                                            title="Select custom number of recent revisions">
                                            <i class="fas fa-check"></i> Apply
                                        </button>
                                    </div>
                                    <small class="text-muted">Enter 1-1000 revisions, or press Enter</small>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <label for="bulk_start_revision" class="form-label">Start Revision</label>
                                <input type="number" class="form-control" id="bulk_start_revision" name="start_revision"
                                    min="1" placeholder="1">
                                <small class="text-muted">Leave empty to start from revision 1</small>
                            </div>
                            <div class="col-md-6">
                                <label for="bulk_end_revision" class="form-label">End Revision</label>
                                <input type="number" class="form-control" id="bulk_end_revision" name="end_revision"
                                    min="1" placeholder="Latest">
                                <small class="text-muted">Leave empty to scan to latest revision</small>
                            </div>
                        </div>
                    </div>

                    <!-- Date Range Settings -->
                    <div id="date_settings" class="mb-4" style="display: none;">
                        <h6 class="fw-bold mb-3">Date Range</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="bulk_start_date" class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="bulk_start_date" name="start_date">
                            </div>
                            <div class="col-md-6">
                                <label for="bulk_end_date" class="form-label">End Date</label>
                                <input type="date" class="form-control" id="bulk_end_date" name="end_date">
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Options -->
                    <div class="mb-4">
                        <h6 class="fw-bold mb-3">Advanced Options</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <label for="bulk_batch_size" class="form-label">Batch Size</label>
                                <input type="number" class="form-control" id="bulk_batch_size" name="batch_size"
                                    value="10" min="1" max="50">
                                <small class="text-muted">Number of revisions to process at once</small>
                            </div>
                            <div class="col-md-6">
                                <label for="bulk_max_files" class="form-label">Max Files per Commit</label>
                                <input type="number" class="form-control" id="bulk_max_files"
                                    name="max_files_per_commit" value="100" min="1">
                                <small class="text-muted">Skip commits with more files than this</small>
                            </div>
                        </div>
                    </div>

                    <!-- Checkboxes -->
                    <div class="mb-4">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="bulk_include_merge"
                                name="include_merge_commits" checked>
                            <label class="form-check-label" for="bulk_include_merge">
                                Include merge commits
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="bulk_skip_large"
                                name="skip_large_commits">
                            <label class="form-check-label" for="bulk_skip_large">
                                Skip large commits (more than max files)
                            </label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="bulk_force_rescan" name="force_rescan">
                            <label class="form-check-label" for="bulk_force_rescan">
                                Force rescan (re-process existing documents)
                            </label>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkScan()">
                    <i class="fas fa-search me-2"></i>Start Bulk Scan
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>


    function editRepository(id, name, url, username, enabled, emailRecipients, productDocFiles, riskAggressiveness, riskDescription) {
        document.getElementById('edit_name').value = name;
        document.getElementById('edit_url').value = url;
        document.getElementById('edit_username').value = username;
        document.getElementById('edit_enabled').checked = enabled;
        document.getElementById('edit_email_recipients').value = emailRecipients || '';
        document.getElementById('edit_product_documentation_files').value = productDocFiles || '';
        document.getElementById('edit_risk_aggressiveness').value = riskAggressiveness || 'BALANCED';
        document.getElementById('edit_risk_description').value = riskDescription || '';


        // Update form action
        document.getElementById('editRepositoryForm').action = '/repositories/' + id + '/edit';

        // Show modal
        new bootstrap.Modal(document.getElementById('editRepositoryModal')).show();
    }

    function editRepositoryFromData(button) {
        // Get data from button attributes
        const id = button.getAttribute('data-repo-id');
        const name = button.getAttribute('data-repo-name');
        const url = button.getAttribute('data-repo-url');
        const username = button.getAttribute('data-repo-username');
        const enabled = button.getAttribute('data-repo-enabled') === 'true';
        const emailRecipients = button.getAttribute('data-repo-email');
        const productDocFiles = button.getAttribute('data-repo-docs');
        const riskAggressiveness = button.getAttribute('data-repo-risk-aggressiveness');
        const riskDescription = button.getAttribute('data-repo-risk-description');
        // Call the original function
        editRepository(id, name, url, username, enabled, emailRecipients, productDocFiles, riskAggressiveness, riskDescription);
    }

    // Document browser functionality
    let currentRepositoryId = null;
    let currentPath = '/';
    let selectedFiles = new Set();

    function openDocumentBrowser() {
        // Get current repository info from the edit form
        const repoUrl = document.getElementById('edit_url').value;
        const repoUsername = document.getElementById('edit_username').value;

        if (!repoUrl) {
            alert('Please save the repository configuration first before browsing files.');
            return;
        }

        // Get repository ID from the form action
        const formAction = document.getElementById('editRepositoryForm').action;
        currentRepositoryId = formAction.split('/').slice(-2, -1)[0];

        // Reset browser state
        currentPath = '/';
        selectedFiles.clear();
        updateSelectedFilesDisplay();

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('documentBrowserModal'));
        modal.show();

        // Initialize resizable functionality
        initializeResizableModal();

        // Add event listener for filter toggle
        document.getElementById('filter_docs').addEventListener('change', function () {
            // Reload current directory with new filter setting
            loadRepositoryFiles(currentPath);
        });

        // Load the root directory
        loadRepositoryFiles('/');
    }

    function loadRepositoryFiles(path) {
        currentPath = path;
        document.getElementById('current_path').textContent = path || '/';

        // Show loading indicator
        document.getElementById('loading_indicator').style.display = 'block';
        document.getElementById('file_browser').style.display = 'none';
        document.getElementById('error_message').style.display = 'none';

        // Get filter setting
        const filterDocs = document.getElementById('filter_docs').checked;

        // Make API call to browse repository
        const params = new URLSearchParams({
            path: path || '/',
            filter_docs: filterDocs ? 'true' : 'false'
        });

        fetch(`/api/repositories/${currentRepositoryId}/browse?${params}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayRepositoryFiles(data.files, path);
                } else {
                    showError(data.error || 'Failed to load repository files');
                }
            })
            .catch(error => {
                console.error('Error loading repository files:', error);
                showError('Failed to connect to repository. Please check the repository configuration.');
            })
            .finally(() => {
                document.getElementById('loading_indicator').style.display = 'none';
            });
    }

    function displayRepositoryFiles(files, currentPath) {
        const fileBrowser = document.getElementById('file_browser');
        fileBrowser.innerHTML = '';
        fileBrowser.style.display = 'block';

        // Add parent directory link if not at root
        if (currentPath !== '/' && currentPath !== '') {
            const parentPath = currentPath.split('/').slice(0, -1).join('/') || '/';
            const parentItem = createFileItem('..', 'directory', parentPath, true);
            fileBrowser.appendChild(parentItem);
        }

        // Sort files: directories first, then files
        files.sort((a, b) => {
            if (a.type !== b.type) {
                return a.type === 'directory' ? -1 : 1;
            }
            return a.name.localeCompare(b.name);
        });

        // Add files and directories
        files.forEach(file => {
            const fullPath = currentPath === '/' ? file.name : `${currentPath}/${file.name}`;
            const fileItem = createFileItem(file.name, file.type, fullPath, false);
            fileBrowser.appendChild(fileItem);
        });
    }

    function createFileItem(name, type, fullPath, isParent) {
        const item = document.createElement('div');
        item.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';

        const leftContent = document.createElement('div');
        leftContent.className = 'd-flex align-items-center';

        const icon = document.createElement('i');
        if (isParent) {
            icon.className = 'fas fa-level-up-alt text-muted me-2';
        } else if (type === 'directory') {
            icon.className = 'fas fa-folder text-warning me-2';
        } else {
            icon.className = getFileIcon(name);
        }

        const nameSpan = document.createElement('span');
        nameSpan.textContent = name;
        if (isParent) {
            nameSpan.className = 'text-muted';
        }

        leftContent.appendChild(icon);
        leftContent.appendChild(nameSpan);
        item.appendChild(leftContent);

        // Add click handler
        if (type === 'directory' || isParent) {
            item.style.cursor = 'pointer';
            item.onclick = () => loadRepositoryFiles(fullPath);
        } else {
            // Add selection checkbox for files
            const rightContent = document.createElement('div');
            rightContent.className = 'd-flex align-items-center';

            // Add file type indicator for special formats
            const ext = name.split('.').pop().toLowerCase();
            if (['doc', 'docx', 'rtf', 'odt'].includes(ext)) {
                const typeIndicator = document.createElement('span');
                typeIndicator.className = 'badge bg-info me-2';
                typeIndicator.textContent = ext.toUpperCase();
                typeIndicator.title = 'Document file - content will be extracted for AI analysis';
                rightContent.appendChild(typeIndicator);
            }

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = 'form-check-input';
            checkbox.checked = selectedFiles.has(fullPath);
            checkbox.onchange = (e) => toggleFileSelection(fullPath, e.target.checked);

            rightContent.appendChild(checkbox);
            item.appendChild(rightContent);

            // Make the whole item clickable to toggle selection
            item.style.cursor = 'pointer';
            item.onclick = (e) => {
                if (e.target !== checkbox) {
                    checkbox.checked = !checkbox.checked;
                    toggleFileSelection(fullPath, checkbox.checked);
                }
            };
        }

        return item;
    }

    function getFileIcon(filename) {
        const ext = filename.split('.').pop().toLowerCase();
        const iconMap = {
            // Text and Markdown files
            'md': 'fas fa-file-alt text-info me-2',
            'txt': 'fas fa-file-alt text-secondary me-2',
            'rst': 'fas fa-file-alt text-info me-2',

            // Web files
            'html': 'fas fa-file-code text-danger me-2',
            'htm': 'fas fa-file-code text-danger me-2',

            // Configuration files
            'json': 'fas fa-file-code text-warning me-2',
            'xml': 'fas fa-file-code text-success me-2',
            'yml': 'fas fa-file-code text-primary me-2',
            'yaml': 'fas fa-file-code text-primary me-2',

            // Document files
            'pdf': 'fas fa-file-pdf text-danger me-2',
            'doc': 'fas fa-file-word text-primary me-2',
            'docx': 'fas fa-file-word text-primary me-2',
            'rtf': 'fas fa-file-word text-info me-2',
            'odt': 'fas fa-file-alt text-success me-2',
            'pages': 'fas fa-file-alt text-warning me-2'
        };

        return iconMap[ext] || 'fas fa-file text-secondary me-2';
    }

    function toggleFileSelection(filePath, selected) {
        if (selected) {
            selectedFiles.add(filePath);
        } else {
            selectedFiles.delete(filePath);
        }
        updateSelectedFilesDisplay();
    }

    function updateSelectedFilesDisplay() {
        const selectedContainer = document.getElementById('selected_files');
        const countBadge = document.getElementById('selected_count');

        countBadge.textContent = selectedFiles.size;

        if (selectedFiles.size === 0) {
            selectedContainer.innerHTML = `
            <div class="list-group-item text-muted text-center">
                <i class="fas fa-info-circle"></i>
                <br>Select documentation files from the repository browser
            </div>
        `;
        } else {
            selectedContainer.innerHTML = '';
            Array.from(selectedFiles).sort().forEach(filePath => {
                const item = document.createElement('div');
                item.className = 'list-group-item d-flex justify-content-between align-items-center';

                const fileName = filePath.split('/').pop();
                const pathSpan = document.createElement('div');
                pathSpan.innerHTML = `
                <div class="fw-bold">${fileName}</div>
                <small class="text-muted">${filePath}</small>
            `;

                const removeBtn = document.createElement('button');
                removeBtn.className = 'btn btn-sm btn-outline-danger';
                removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                removeBtn.onclick = () => {
                    selectedFiles.delete(filePath);
                    updateSelectedFilesDisplay();
                    // Update checkboxes in the file browser
                    const checkboxes = document.querySelectorAll('#file_browser input[type="checkbox"]');
                    checkboxes.forEach(cb => {
                        const item = cb.closest('.list-group-item');
                        const itemPath = getItemPath(item);
                        if (itemPath === filePath) {
                            cb.checked = false;
                        }
                    });
                };

                item.appendChild(pathSpan);
                item.appendChild(removeBtn);
                selectedContainer.appendChild(item);
            });
        }
    }

    function getItemPath(item) {
        // This is a helper function to get the path from a file item
        // Implementation depends on how we store the path in the item
        return item.dataset.path || '';
    }

    function applySelectedFiles() {
        const textarea = document.getElementById('edit_product_documentation_files');
        const filesArray = Array.from(selectedFiles).sort();
        textarea.value = filesArray.join('\n');

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('documentBrowserModal'));
        modal.hide();

        // Show success message
        if (filesArray.length > 0) {
            // You could add a toast notification here
            console.log(`Applied ${filesArray.length} selected files to configuration`);
        }
    }

    function showError(message) {
        const errorDiv = document.getElementById('error_message');
        errorDiv.textContent = message;
        errorDiv.style.display = 'block';
        document.getElementById('file_browser').style.display = 'none';
    }

    function initializeResizableModal() {
        const modalDialog = document.querySelector('#documentBrowserModal .modal-dialog-resizable');

        if (!modalDialog) return;

        // Set initial size (80% of viewport)
        const initialWidth = Math.min(1200, window.innerWidth * 0.8);
        const initialHeight = Math.min(800, window.innerHeight * 0.8);

        modalDialog.style.width = initialWidth + 'px';
        modalDialog.style.height = initialHeight + 'px';

        // Center the modal
        modalDialog.style.margin = 'auto';
        modalDialog.style.position = 'relative';

        // Add resize event listener to maintain proper layout
        const resizeObserver = new ResizeObserver(entries => {
            for (let entry of entries) {
                // Trigger a layout recalculation when the modal is resized
                const fileBrowser = document.getElementById('file_browser');
                const selectedFiles = document.getElementById('selected_files');

                if (fileBrowser && selectedFiles) {
                    // Force a repaint to ensure scrollbars appear correctly
                    fileBrowser.style.display = 'none';
                    selectedFiles.style.display = 'none';

                    setTimeout(() => {
                        fileBrowser.style.display = '';
                        selectedFiles.style.display = '';
                    }, 1);
                }
            }
        });

        resizeObserver.observe(modalDialog);

        // Store the observer for cleanup
        modalDialog._resizeObserver = resizeObserver;

        // Add keyboard shortcuts for common sizes
        document.addEventListener('keydown', function (e) {
            if (document.getElementById('documentBrowserModal').classList.contains('show')) {
                if (e.ctrlKey || e.metaKey) {
                    switch (e.key) {
                        case '1':
                            // Small size
                            e.preventDefault();
                            resizeModalTo(800, 600);
                            break;
                        case '2':
                            // Medium size
                            e.preventDefault();
                            resizeModalTo(1000, 700);
                            break;
                        case '3':
                            // Large size
                            e.preventDefault();
                            resizeModalTo(1200, 800);
                            break;
                        case '0':
                            // Maximize
                            e.preventDefault();
                            resizeModalTo(window.innerWidth * 0.95, window.innerHeight * 0.95);
                            break;
                    }
                }
            }
        });
    }

    function resizeModalTo(width, height) {
        const modalDialog = document.querySelector('#documentBrowserModal .modal-dialog-resizable');
        if (modalDialog) {
            modalDialog.style.width = Math.min(width, window.innerWidth * 0.95) + 'px';
            modalDialog.style.height = Math.min(height, window.innerHeight * 0.95) + 'px';
        }
    }

    // Cleanup when modal is hidden
    document.getElementById('documentBrowserModal').addEventListener('hidden.bs.modal', function () {
        const modalDialog = document.querySelector('#documentBrowserModal .modal-dialog-resizable');
        if (modalDialog && modalDialog._resizeObserver) {
            modalDialog._resizeObserver.disconnect();
            delete modalDialog._resizeObserver;
        }
    });

    function refreshRepositoryStatus() {
        const refreshBtn = document.querySelector('button[onclick="refreshRepositoryStatus()"]');
        const originalHTML = refreshBtn.innerHTML;

        // Show loading state
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Refreshing...';
        refreshBtn.disabled = true;

        // Force reload the page to get fresh data
        setTimeout(() => {
            location.reload();
        }, 500);
    }

    // Real-time scan status updates
    let statusUpdateInterval = null;
    let isUpdatingStatus = false;

    function startStatusUpdates() {
        // Only start if not already running
        if (statusUpdateInterval) return;

        statusUpdateInterval = setInterval(updateScanStatuses, 5000); // Update every 5 seconds

        // Show real-time indicator
        const indicator = document.getElementById('realTimeIndicator');
        if (indicator) {
            indicator.style.display = 'inline-block';
        }

        console.log('Started real-time scan status updates');
    }

    function stopStatusUpdates() {
        if (statusUpdateInterval) {
            clearInterval(statusUpdateInterval);
            statusUpdateInterval = null;

            // Hide real-time indicator
            const indicator = document.getElementById('realTimeIndicator');
            if (indicator) {
                indicator.style.display = 'none';
            }

            console.log('Stopped real-time scan status updates');
        }
    }

    async function updateScanStatuses() {
        if (isUpdatingStatus) return; // Prevent overlapping requests

        try {
            isUpdatingStatus = true;

            // Fetch current repository status
            const response = await fetch('/api/repositories/status');
            if (!response.ok) return;

            const repositories = await response.json();

            // Update scan status indicators for each repository
            repositories.forEach(repo => {
                updateRepositoryScanStatus(repo);
            });

        } catch (error) {
            console.error('Error updating scan statuses:', error);
        } finally {
            isUpdatingStatus = false;
        }
    }

    function updateRepositoryScanStatus(repo) {
        // Find all scan status elements for this repository
        const statusElements = document.querySelectorAll(`[data-repo-id="${repo.id}"] .scan-status, .scan-status[data-repo-id="${repo.id}"]`);

        statusElements.forEach(element => {
            if (!repo.historical_scan || !repo.historical_scan.scan_status) {
                element.innerHTML = '<span class="text-muted">Not configured</span>';
                return;
            }

            const status = repo.historical_scan.scan_status;
            const statusValue = status.value || status;

            // Update CSS class
            element.className = element.className.replace(/scan-status-\w+/, `scan-status-${statusValue}`);

            // Update icon and text
            let icon = 'exclamation-triangle';
            if (statusValue === 'completed') {
                icon = 'check';
            } else if (statusValue === 'in_progress') {
                icon = 'spinner fa-spin';
            } else if (statusValue === 'failed') {
                icon = 'exclamation-triangle';
            }

            const displayText = statusValue.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());

            // Update the content based on the element type
            if (element.querySelector('small')) {
                // Cards view format
                let iconHtml = '';
                if (statusValue === 'in_progress') {
                    iconHtml = '<div class="progress-spinner-large"></div>';
                } else {
                    iconHtml = `<i class="fas fa-${icon} fa-lg"></i>`;
                }

                let content = `${iconHtml}<br><small>${displayText}</small>`;
                if (statusValue === 'in_progress' && repo.historical_scan.processed_revisions !== undefined && repo.historical_scan.total_revisions) {
                    const processed = repo.historical_scan.processed_revisions;
                    const total = repo.historical_scan.total_revisions;
                    const percentage = Math.round((processed / total) * 100);
                    content += `<br><small class="text-muted">${processed}/${total} (${percentage}%)</small>`;
                }
                element.innerHTML = content;
            } else {
                // Table view format
                let iconHtml = '';
                if (statusValue === 'in_progress') {
                    iconHtml = '<span class="progress-spinner"></span>';
                } else {
                    iconHtml = `<i class="fas fa-${icon} me-1"></i>`;
                }

                let content = `${iconHtml}${displayText}`;
                if (statusValue === 'in_progress' && repo.historical_scan.processed_revisions !== undefined && repo.historical_scan.total_revisions) {
                    const processed = repo.historical_scan.processed_revisions;
                    const total = repo.historical_scan.total_revisions;
                    content += `<br><small class="scan-progress text-muted">${processed}/${total}</small>`;
                }
                element.innerHTML = content;
            }
        });

        // Update progress information if available
        if (repo.historical_scan && repo.historical_scan.processed_revisions !== undefined) {
            const progressElements = document.querySelectorAll(`[data-repo-id="${repo.id}"] .scan-progress`);
            progressElements.forEach(element => {
                const processed = repo.historical_scan.processed_revisions || 0;
                const total = repo.historical_scan.total_revisions || 0;
                if (total > 0) {
                    const percentage = Math.round((processed / total) * 100);
                    element.textContent = `${processed}/${total} (${percentage}%)`;
                }
            });
        }
    }

    // Auto-start status updates when page loads
    document.addEventListener('DOMContentLoaded', function () {
        // Check if there are any active scans
        const activeScans = document.querySelectorAll('.scan-status-in_progress');
        if (activeScans.length > 0) {
            startStatusUpdates();
        }

        // Stop updates when page is hidden (browser tab not active)
        document.addEventListener('visibilitychange', function () {
            if (document.hidden) {
                stopStatusUpdates();
            } else {
                // Restart if there are active scans
                const activeScans = document.querySelectorAll('.scan-status-in_progress');
                if (activeScans.length > 0) {
                    startStatusUpdates();
                }
            }
        });
    });

    // Enhanced Repository Management Functions
    let bulkModeActive = false;

    function toggleBulkMode() {
        bulkModeActive = !bulkModeActive;
        const bulkBar = document.getElementById('bulkActionsBar');
        const checkboxes = document.querySelectorAll('.repo-checkbox');
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');

        if (bulkModeActive) {
            bulkBar.classList.remove('hidden');
            checkboxes.forEach(cb => cb.style.display = 'inline-block');
            if (selectAllCheckbox) selectAllCheckbox.style.display = 'inline-block';
        } else {
            bulkBar.classList.add('hidden');
            checkboxes.forEach(cb => {
                cb.style.display = 'none';
                cb.checked = false;
            });
            if (selectAllCheckbox) {
                selectAllCheckbox.style.display = 'none';
                selectAllCheckbox.checked = false;
            }
            updateSelectedCount();
        }
    }

    function updateSelectedCount() {
        const selected = document.querySelectorAll('input[name="repo_select"]:checked');
        const countSpan = document.getElementById('selectedCount');
        if (countSpan) {
            countSpan.textContent = selected.length;
        }
    }

    function selectAll() {
        const checkboxes = document.querySelectorAll('input[name="repo_select"]');
        checkboxes.forEach(cb => cb.checked = true);
        updateSelectedCount();
    }

    function clearSelection() {
        const checkboxes = document.querySelectorAll('input[name="repo_select"]');
        checkboxes.forEach(cb => cb.checked = false);
        updateSelectedCount();
    }

    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAllCheckbox');
        const checkboxes = document.querySelectorAll('input[name="repo_select"]');
        checkboxes.forEach(cb => cb.checked = selectAllCheckbox.checked);
        updateSelectedCount();
    }

    function toggleGroupSelection(group, checked) {
        const checkboxes = document.querySelectorAll(`input[name="repo_select"]`);
        // This is a simplified version - in a real implementation, you'd filter by group
        checkboxes.forEach(cb => cb.checked = checked);
        updateSelectedCount();
    }

    function submitBulkAction(action) {
        const selected = document.querySelectorAll('input[name="repo_select"]:checked');

        if (selected.length === 0) {
            alert('Please select at least one repository');
            return;
        }

        let confirmMessage = '';
        switch (action) {
            case 'enable':
                confirmMessage = `Enable ${selected.length} repositories?`;
                break;
            case 'disable':
                confirmMessage = `Disable ${selected.length} repositories?`;
                break;
            case 'delete':
                confirmMessage = `Delete ${selected.length} repositories? This action cannot be undone.`;
                break;
            case 'start_scan':
                // Show bulk scan configuration modal instead of simple confirm
                showBulkScanModal(selected);
                return;
            case 'stop_scan':
                confirmMessage = `Stop historical scan for ${selected.length} repositories?`;
                break;
            case 'reset_status':
                confirmMessage = `Reset scan status for ${selected.length} repositories? This will clear scan progress and allow re-scanning.`;
                break;
            case 'reset_status':
                confirmMessage = `Reset scan status for ${selected.length} repositories? This will clear scan progress and allow re-scanning.`;
                break;
        }

        if (confirm(confirmMessage)) {
            // Add selected repository IDs to the form
            const form = document.getElementById('bulkActionsForm');

            // Remove existing hidden inputs
            const existingInputs = form.querySelectorAll('input[name="selected_repos"]');
            existingInputs.forEach(input => input.remove());

            // Add current selections
            selected.forEach(checkbox => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'selected_repos';
                input.value = checkbox.value;
                form.appendChild(input);
            });

            // Add action
            const actionInput = document.createElement('input');
            actionInput.type = 'hidden';
            actionInput.name = 'action';
            actionInput.value = action;
            form.appendChild(actionInput);

            // Submit form
            form.submit();
        }
    }

    // Auto-submit functionality for filters
    document.addEventListener('DOMContentLoaded', function () {
        const autoSubmitElements = ['status', 'type', 'scan_status', 'sort_by', 'sort_order', 'view_mode'];

        autoSubmitElements.forEach(function (elementId) {
            const element = document.getElementById(elementId);
            if (element) {
                element.addEventListener('change', function () {
                    document.getElementById('filtersForm').submit();
                });
            }
        });

        // Add search functionality with debounce
        const searchInput = document.getElementById('search');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', function () {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(function () {
                    if (searchInput.value.length >= 3 || searchInput.value.length === 0) {
                        document.getElementById('filtersForm').submit();
                    }
                }, 500); // 500ms debounce
            });
        }

        // Add keyboard shortcuts
        document.addEventListener('keydown', function (e) {
            // Ctrl+F to focus search
            if (e.ctrlKey && e.key === 'f') {
                e.preventDefault();
                document.getElementById('search').focus();
            }

            // Escape to clear search
            if (e.key === 'Escape' && document.activeElement === document.getElementById('search')) {
                document.getElementById('search').value = '';
                document.getElementById('filtersForm').submit();
            }

            // Ctrl+A to toggle bulk mode
            if (e.ctrlKey && e.key === 'a' && !e.target.matches('input, textarea')) {
                e.preventDefault();
                toggleBulkMode();
            }
        });

        // Client-side validation for add repository form
        document.getElementById('addRepositoryModal').addEventListener('shown.bs.modal', function () {
            // Clear any previous validation states
            const form = this.querySelector('form');
            form.querySelectorAll('.is-invalid').forEach(el => el.classList.remove('is-invalid'));
            form.querySelectorAll('.invalid-feedback').forEach(el => el.remove());
        });

        // Add repository form validation
        document.querySelector('#addRepositoryModal form').addEventListener('submit', function (e) {
            const nameInput = document.getElementById('add_name');
            const urlInput = document.getElementById('add_url');

            // Clear previous validation states
            nameInput.classList.remove('is-invalid');
            urlInput.classList.remove('is-invalid');
            this.querySelectorAll('.invalid-feedback').forEach(el => el.remove());

            let hasErrors = false;

            // Validate name
            const name = nameInput.value.trim();
            if (!name) {
                showFieldError(nameInput, 'Repository name is required');
                hasErrors = true;
            } else {
                // Check for duplicate name
                const existingNames = [{% for repo in repositories %} '{{ repo.name|replace("'", "\\'") }}'{% if not loop.last %}, {% endif %} {% endfor %}];
            if (existingNames.includes(name)) {
                showFieldError(nameInput, 'A repository with this name already exists');
                hasErrors = true;
            }
        }

    // Validate URL
    const url = urlInput.value.trim();
        if (!url) {
            showFieldError(urlInput, 'Repository URL is required');
            hasErrors = true;
        } else {
            // Check for duplicate URL
            const existingUrls = [{% for repo in repositories %} '{{ repo.url|replace("'", "\\'") }}'{% if not loop.last %}, {% endif %} {% endfor %}];
        if (existingUrls.includes(url)) {
            showFieldError(urlInput, 'A repository with this URL already exists');
            hasErrors = true;
        }
    }

    if (hasErrors) {
        e.preventDefault();
    }
});

    function showFieldError(field, message) {
        field.classList.add('is-invalid');
        const feedback = document.createElement('div');
        feedback.className = 'invalid-feedback';
        feedback.textContent = message;
        field.parentNode.appendChild(feedback);
    }
});

    // Panel Collapse State Management (Unified System)
    function savePanelState(panelId, isExpanded) {
        try {
            const panelStates = JSON.parse(localStorage.getItem('reposense_panel_states') || '{}');
            panelStates[panelId] = isExpanded;
            localStorage.setItem('reposense_panel_states', JSON.stringify(panelStates));
            console.log(`Panel state saved: ${panelId} = ${isExpanded}`);
        } catch (error) {
            console.warn('Failed to save panel state:', error);
        }
    }

    function loadPanelState(panelId, defaultExpanded = true) {
        try {
            const panelStates = JSON.parse(localStorage.getItem('reposense_panel_states') || '{}');
            return panelStates.hasOwnProperty(panelId) ? panelStates[panelId] : defaultExpanded;
        } catch (error) {
            console.warn('Failed to load panel state:', error);
            return defaultExpanded;
        }
    }

    function initializePanelStates() {
        // Initialize Repositories page filters panel
        const filtersPanel = document.getElementById('filtersCollapse');
        if (filtersPanel) {
            const shouldExpand = loadPanelState('repositories_filters', true);

            // Set the initial state properly
            if (shouldExpand) {
                filtersPanel.classList.add('show');
            } else {
                filtersPanel.classList.remove('show');
            }

            // Initialize Bootstrap collapse without forcing show/hide
            const bsCollapse = new bootstrap.Collapse(filtersPanel, { toggle: false });

            // Save state when panel is toggled
            filtersPanel.addEventListener('shown.bs.collapse', () => savePanelState('repositories_filters', true));
            filtersPanel.addEventListener('hidden.bs.collapse', () => savePanelState('repositories_filters', false));

            // Update button icon based on state
            const toggleButton = document.querySelector('[data-bs-target="#filtersCollapse"]');
            if (toggleButton) {
                const updateIcon = () => {
                    const icon = toggleButton.querySelector('i');
                    if (filtersPanel.classList.contains('show')) {
                        icon.className = 'fas fa-chevron-up';
                    } else {
                        icon.className = 'fas fa-chevron-down';
                    }
                };

                filtersPanel.addEventListener('shown.bs.collapse', updateIcon);
                filtersPanel.addEventListener('hidden.bs.collapse', updateIcon);
                updateIcon(); // Set initial state
            }
        }
    }

    // Repository Filters & Management Local Storage
    const STORAGE_KEY = 'reposense_repository_filters';

    // Settings that should be persisted
    const PERSISTENT_SETTINGS = [
        'search',
        'status',
        'type',
        'scan_status',
        'sort_by',
        'sort_order',
        'view_mode'
    ];

    // Save current filter settings to localStorage
    function saveFilterSettings() {
        const settings = {};

        PERSISTENT_SETTINGS.forEach(setting => {
            const element = document.getElementById(setting);
            if (element) {
                settings[setting] = element.value;
            }
        });

        // Panel state is now managed separately by the unified panel system

        localStorage.setItem(STORAGE_KEY, JSON.stringify(settings));
        console.log('🔄 Filter settings saved:', settings);
    }

    // Load filter settings from localStorage
    function loadFilterSettings() {
        try {
            const savedSettings = localStorage.getItem(STORAGE_KEY);
            if (!savedSettings) {
                console.log('📭 No saved filter settings found');
                return;
            }

            const settings = JSON.parse(savedSettings);
            console.log('📥 Loading filter settings:', settings);

            // Apply saved settings to form elements
            PERSISTENT_SETTINGS.forEach(setting => {
                const element = document.getElementById(setting);
                if (element && settings[setting] !== undefined) {
                    element.value = settings[setting];
                    console.log(`✅ Restored ${setting}: ${settings[setting]}`);
                }
            });

            // Panel state is now managed separately by the unified panel system

        } catch (error) {
            console.error('❌ Error loading filter settings:', error);
            // Clear corrupted data
            localStorage.removeItem(STORAGE_KEY);
        }
    }

    // Clear saved filter settings
    function clearFilterSettings() {
        localStorage.removeItem(STORAGE_KEY);
        console.log('🗑️ Filter settings cleared');

        // Reset form to defaults
        PERSISTENT_SETTINGS.forEach(setting => {
            const element = document.getElementById(setting);
            if (element) {
                if (element.tagName === 'SELECT') {
                    element.selectedIndex = 0;
                } else {
                    element.value = '';
                }
            }
        });

        // Show a brief notification
        showFilterNotification('Filter settings cleared', 'info');
    }

    // Show a brief notification for filter actions
    function showFilterNotification(message, type = 'success') {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
        <i class="fas fa-${type === 'success' ? 'check' : type === 'info' ? 'info' : 'exclamation'}-circle me-2"></i>
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

        document.body.appendChild(notification);

        // Auto-remove after 3 seconds
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 3000);
    }

    // Add event listeners to save settings when they change
    function initializeFilterPersistence() {
        PERSISTENT_SETTINGS.forEach(setting => {
            const element = document.getElementById(setting);
            if (element) {
                element.addEventListener('change', saveFilterSettings);
                element.addEventListener('input', debounce(saveFilterSettings, 500)); // Debounce for search input
            }
        });

        // Save collapse state when toggled
        const toggleButton = document.querySelector('[data-bs-target="#filtersCollapse"]');
        if (toggleButton) {
            toggleButton.addEventListener('click', () => {
                // Wait for collapse animation to complete
                setTimeout(saveFilterSettings, 350);
            });
        }

        console.log('🔧 Filter persistence initialized');
    }

    // Debounce function to limit how often we save during typing
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // Add reset settings button functionality
    function addResetSettingsButton() {
        // Find the Clear button and add a reset settings option
        const clearButton = document.querySelector('a[href*="repositories_page"]');
        if (clearButton && clearButton.textContent.includes('Clear')) {
            // Add a small reset settings button next to the clear button
            const resetButton = document.createElement('button');
            resetButton.type = 'button';
            resetButton.className = 'btn btn-outline-warning btn-sm ms-1';
            resetButton.innerHTML = '<i class="fas fa-undo"></i>';
            resetButton.title = 'Reset saved filter preferences';
            resetButton.onclick = function (e) {
                e.preventDefault();
                if (confirm('Reset all saved filter preferences? This will clear your saved settings.')) {
                    clearFilterSettings();
                }
            };

            clearButton.parentNode.appendChild(resetButton);
        }
    }

    // Initialize filter persistence on page load
    document.addEventListener('DOMContentLoaded', function () {

        // Initialize panel collapse states
        initializePanelStates();

        // Initialize filter persistence
        loadFilterSettings();
        initializeFilterPersistence();
        addResetSettingsButton();


    });

    // Bulk Scan Modal Functions
    function showBulkScanModal(selectedCheckboxes) {
        // Store selected repositories for later use
        window.selectedRepositoriesForBulkScan = selectedCheckboxes;

        // Update the count in the modal
        document.getElementById('selectedRepoCount').textContent = selectedCheckboxes.length;

        // Show the modal
        const modal = new bootstrap.Modal(document.getElementById('bulkScanModal'));
        modal.show();
    }

    function executeBulkScan() {
        const selected = window.selectedRepositoriesForBulkScan;
        if (!selected || selected.length === 0) {
            alert('No repositories selected');
            return;
        }

        // Get form data
        const form = document.getElementById('bulkScanForm');
        const formData = new FormData(form);

        // Validate required fields based on scan method
        const scanMethod = formData.get('scan_method');
        if (scanMethod === 'date') {
            const startDate = formData.get('start_date');
            const endDate = formData.get('end_date');
            if (!startDate || !endDate) {
                alert('Please specify both start and end dates for date-based scanning');
                return;
            }
        }

        // Close the modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('bulkScanModal'));
        modal.hide();

        // Prepare the bulk action form
        const bulkForm = document.getElementById('bulkActionsForm');

        // Remove existing hidden inputs
        const existingInputs = bulkForm.querySelectorAll('input[name="selected_repos"], input[name="action"], input[name^="scan_"]');
        existingInputs.forEach(input => input.remove());

        // Add selected repository IDs
        selected.forEach(checkbox => {
            const input = document.createElement('input');
            input.type = 'hidden';
            input.name = 'selected_repos';
            input.value = checkbox.value;
            bulkForm.appendChild(input);
        });

        // Add action
        const actionInput = document.createElement('input');
        actionInput.type = 'hidden';
        actionInput.name = 'action';
        actionInput.value = 'start_scan';
        bulkForm.appendChild(actionInput);

        // Add scan configuration parameters
        for (const [key, value] of formData.entries()) {
            if (value && value.trim() !== '') {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = `scan_${key}`;
                input.value = value;
                bulkForm.appendChild(input);
            }
        }

        // Submit the form
        bulkForm.submit();
    }

    // Bulk scan quick selection functions
    function bulkSelectAllRevisions() {
        // Clear both fields to indicate "all revisions"
        document.getElementById('bulk_start_revision').value = '';
        document.getElementById('bulk_end_revision').value = '';

        // Visual feedback
        clearBulkButtonHighlights();
        document.getElementById('bulk-select-all-revisions').classList.remove('btn-outline-secondary');
        document.getElementById('bulk-select-all-revisions').classList.add('btn-primary');
    }

    function bulkSelectLastNRevisions(count) {
        // For bulk scan, we'll use a simple approach:
        // Leave start_revision empty and set end_revision empty (latest)
        // The backend will handle the "last N" logic based on the count
        // For now, we'll estimate by setting a reasonable range

        // Clear start revision (will default to appropriate start based on count)
        document.getElementById('bulk_start_revision').value = '';
        document.getElementById('bulk_end_revision').value = '';

        // Store the count for the backend to use
        // We'll add a hidden field to track this
        let countField = document.getElementById('bulk_revision_count');
        if (!countField) {
            countField = document.createElement('input');
            countField.type = 'hidden';
            countField.id = 'bulk_revision_count';
            countField.name = 'revision_count';
            document.getElementById('bulkScanForm').appendChild(countField);
        }
        countField.value = count;

        // Visual feedback
        clearBulkButtonHighlights();
        const buttonId = count === 10 ? 'bulk-select-recent-revisions' :
            count === 25 ? 'bulk-select-last-25' : null;

        if (buttonId) {
            const button = document.getElementById(buttonId);
            button.classList.remove('btn-outline-secondary');
            button.classList.add('btn-primary');
        }
    }

    function bulkSelectCustomCount() {
        const countInput = document.getElementById('bulk-custom-revision-count');
        const count = parseInt(countInput.value);

        if (!count || count < 1 || count > 1000) {
            alert('Please enter a valid number between 1 and 1000');
            countInput.focus();
            return;
        }

        // Use the same logic as bulkSelectLastNRevisions
        bulkSelectLastNRevisions(count);

        // Update visual feedback for custom input
        clearBulkButtonHighlights();
        document.getElementById('bulk-select-custom-count').classList.remove('btn-outline-primary');
        document.getElementById('bulk-select-custom-count').classList.add('btn-primary');
    }

    function clearBulkButtonHighlights() {
        const buttons = [
            'bulk-select-all-revisions',
            'bulk-select-recent-revisions',
            'bulk-select-last-25',
            'bulk-select-custom-count'
        ];

        buttons.forEach(buttonId => {
            const button = document.getElementById(buttonId);
            if (button) {
                button.classList.remove('btn-primary');
                button.classList.add('btn-outline-secondary');
            }
        });

        // Reset custom count button to primary outline
        const customButton = document.getElementById('bulk-select-custom-count');
        if (customButton) {
            customButton.classList.remove('btn-primary');
            customButton.classList.add('btn-outline-primary');
        }
    }

    // Handle scan method radio button changes
    document.addEventListener('DOMContentLoaded', function () {
        const revisionRadio = document.getElementById('scan_by_revision');
        const dateRadio = document.getElementById('scan_by_date');
        const revisionSettings = document.getElementById('revision_settings');
        const dateSettings = document.getElementById('date_settings');

        function toggleScanSettings() {
            if (revisionRadio.checked) {
                revisionSettings.style.display = 'block';
                dateSettings.style.display = 'none';
            } else {
                revisionSettings.style.display = 'none';
                dateSettings.style.display = 'block';
            }
        }

        revisionRadio.addEventListener('change', toggleScanSettings);
        dateRadio.addEventListener('change', toggleScanSettings);

        // Add event listeners for quick selection buttons
        document.getElementById('bulk-select-all-revisions').addEventListener('click', bulkSelectAllRevisions);
        document.getElementById('bulk-select-recent-revisions').addEventListener('click', () => bulkSelectLastNRevisions(10));
        document.getElementById('bulk-select-last-25').addEventListener('click', () => bulkSelectLastNRevisions(25));
        document.getElementById('bulk-select-custom-count').addEventListener('click', bulkSelectCustomCount);

        // Allow Enter key in custom count input to trigger apply
        document.getElementById('bulk-custom-revision-count').addEventListener('keypress', function (e) {
            if (e.key === 'Enter') {
                bulkSelectCustomCount();
            }
        });

        // Clear button highlights when manual input is used
        const startRevisionInput = document.getElementById('bulk_start_revision');
        const endRevisionInput = document.getElementById('bulk_end_revision');
        const customCountInput = document.getElementById('bulk-custom-revision-count');

        startRevisionInput.addEventListener('input', clearBulkButtonHighlights);
        endRevisionInput.addEventListener('input', clearBulkButtonHighlights);
        customCountInput.addEventListener('input', clearBulkButtonHighlights);

        // Initialize
        toggleScanSettings();
    });

    // Repository notification management
    function manageRepositoryNotifications(repoId, repoName) {
        // Load user relationships for this repository
        fetch(`/api/repositories/${repoId}/user-relationships`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showRepositoryNotificationModal(repoId, repoName, data.relationships);
                } else {
                    alert('Error loading repository relationships: ' + data.message);
                }
            })
            .catch(error => {
                alert('Error loading repository relationships: ' + error);
            });
    }

    function showRepositoryNotificationModal(repoId, repoName, relationships) {
        // Create modal HTML
        const modalHtml = `
            <div class="modal fade" id="repoNotificationModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">User Notifications - ${repoName}</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>User</th>
                                            <th>Email</th>
                                            <th>Relationship</th>
                                            <th>Notification Settings</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody id="repoUserRelationships">
                                        ${relationships.map(rel => `
                                            <tr>
                                                <td><strong>${rel.username}</strong></td>
                                                <td><a href="mailto:${rel.email}">${rel.email}</a></td>
                                                <td>
                                                    <span class="badge bg-primary">${rel.relationship_type.charAt(0).toUpperCase() + rel.relationship_type.slice(1)}</span>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        Categories: ${rel.notification_preferences.enabled_categories.join(', ') || 'None'}<br>
                                                        ${rel.notification_preferences.notify_on_high_risk_only ? 'High-risk only' : 'All commits'}<br>
                                                        ${rel.notification_preferences.immediate_notification ? 'Immediate' : 'Digest'}
                                                    </small>
                                                </td>
                                                <td>
                                                    <a href="/users/${rel.user_id}/notifications" class="btn btn-sm btn-outline-primary">
                                                        <i class="fas fa-cog"></i> Configure
                                                    </a>
                                                </td>
                                            </tr>
                                        `).join('')}
                                    </tbody>
                                </table>
                            </div>
                            ${relationships.length === 0 ? '<div class="text-center py-4"><i class="fas fa-users fa-3x text-muted mb-3"></i><h6>No User Relationships</h6><p class="text-muted">No users are currently associated with this repository.</p></div>' : ''}
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                            <a href="/users" class="btn btn-primary">
                                <i class="fas fa-users"></i> Manage Users
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Remove existing modal if present
        const existingModal = document.getElementById('repoNotificationModal');
        if (existingModal) {
            existingModal.remove();
        }

        // Add modal to page
        document.body.insertAdjacentHTML('beforeend', modalHtml);

        // Show modal
        const modal = new bootstrap.Modal(document.getElementById('repoNotificationModal'));
        modal.show();

        // Clean up when modal is hidden
        document.getElementById('repoNotificationModal').addEventListener('hidden.bs.modal', function () {
            this.remove();
        });
    }
</script>
{% endblock %}