#!/usr/bin/env python3
"""
Database storage for notification events and audit trail
Provides persistent storage for notification history and statistics
"""

import json
import logging
import sqlite3
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List, Optional

from notification_system import (
    NotificationCategory,
    NotificationEvent,
    NotificationSeverity,
)


class NotificationDatabase:
    """Database manager for notification events and audit trail"""

    def __init__(self, db_path: str = "/app/data/reposense.db"):
        self.db_path = db_path
        self.logger = logging.getLogger(__name__)

        # Ensure database directory exists
        Path(db_path).parent.mkdir(parents=True, exist_ok=True)

        # Note: Database tables are now created by the migration system

    def store_event(self, event: NotificationEvent) -> bool:
        """Store a notification event in the database"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Convert complex fields to JSON
                commit_info_json = (
                    json.dumps(event.commit_info) if event.commit_info else None
                )
                metadata_json = json.dumps(event.metadata) if event.metadata else None

                cursor.execute(
                    """
                    INSERT OR REPLACE INTO notification_events 
                    (event_id, category, severity, title, message, repository_id, user_id, 
                     commit_info, metadata, timestamp, processed, email_sent)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """,
                    (
                        event.event_id,
                        event.category.value,
                        event.severity.value,
                        event.title,
                        event.message,
                        event.repository_id,
                        event.user_id,
                        commit_info_json,
                        metadata_json,
                        event.timestamp.isoformat(),
                        event.processed,
                        event.email_sent,
                    ),
                )

                # Update statistics
                self._update_statistics(event, conn)

                conn.commit()
                return True

        except Exception as e:
            self.logger.error(f"Error storing notification event {event.event_id}: {e}")
            return False

    def _update_statistics(self, event: NotificationEvent, conn: sqlite3.Connection):
        """Update daily statistics for the event"""
        cursor = conn.cursor()
        date_str = event.timestamp.date().isoformat()

        cursor.execute(
            """
            INSERT INTO notification_statistics (date, category, severity, count, emails_sent)
            VALUES (?, ?, ?, 1, ?)
            ON CONFLICT(date, category, severity) DO UPDATE SET
                count = count + 1,
                emails_sent = emails_sent + ?
        """,
            (
                date_str,
                event.category.value,
                event.severity.value,
                1 if event.email_sent else 0,
                1 if event.email_sent else 0,
            ),
        )

    def get_events(
        self,
        limit: int = 100,
        category: Optional[NotificationCategory] = None,
        repository_id: Optional[str] = None,
        since: Optional[datetime] = None,
    ) -> List[NotificationEvent]:
        """Retrieve notification events with optional filtering"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Build query with filters
                query = "SELECT * FROM notification_events WHERE 1=1"
                params = []

                if category:
                    query += " AND category = ?"
                    params.append(category.value)

                if repository_id:
                    query += " AND repository_id = ?"
                    params.append(repository_id)

                if since:
                    query += " AND timestamp >= ?"
                    params.append(since.isoformat())

                query += " ORDER BY timestamp DESC LIMIT ?"
                params.append(limit)

                cursor.execute(query, params)
                rows = cursor.fetchall()

                # Convert rows to NotificationEvent objects
                events = []
                for row in rows:
                    event_data = {
                        "event_id": row[1],
                        "category": row[2],
                        "severity": row[3],
                        "title": row[4],
                        "message": row[5],
                        "repository_id": row[6],
                        "user_id": row[7],
                        "commit_info": json.loads(row[8]) if row[8] else None,
                        "metadata": json.loads(row[9]) if row[9] else {},
                        "timestamp": row[10],
                        "processed": bool(row[11]),
                        "email_sent": bool(row[12]),
                    }
                    events.append(NotificationEvent.from_dict(event_data))

                return events

        except Exception as e:
            self.logger.error(f"Error retrieving notification events: {e}")
            return []

    def get_statistics(self, days: int = 30) -> Dict[str, Any]:
        """Get notification statistics for the specified number of days"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                # Calculate date range
                end_date = datetime.now().date()
                start_date = end_date - timedelta(days=days)

                # Get overall statistics
                cursor.execute(
                    """
                    SELECT 
                        SUM(count) as total_events,
                        SUM(emails_sent) as total_emails,
                        COUNT(DISTINCT date) as active_days
                    FROM notification_statistics 
                    WHERE date >= ? AND date <= ?
                """,
                    (start_date.isoformat(), end_date.isoformat()),
                )

                overall = cursor.fetchone()

                # Get statistics by category
                cursor.execute(
                    """
                    SELECT category, SUM(count) as count, SUM(emails_sent) as emails
                    FROM notification_statistics 
                    WHERE date >= ? AND date <= ?
                    GROUP BY category
                    ORDER BY count DESC
                """,
                    (start_date.isoformat(), end_date.isoformat()),
                )

                by_category = {
                    row[0]: {"count": row[1], "emails": row[2]}
                    for row in cursor.fetchall()
                }

                # Get statistics by severity
                cursor.execute(
                    """
                    SELECT severity, SUM(count) as count, SUM(emails_sent) as emails
                    FROM notification_statistics 
                    WHERE date >= ? AND date <= ?
                    GROUP BY severity
                    ORDER BY count DESC
                """,
                    (start_date.isoformat(), end_date.isoformat()),
                )

                by_severity = {
                    row[0]: {"count": row[1], "emails": row[2]}
                    for row in cursor.fetchall()
                }

                return {
                    "period_days": days,
                    "total_events": overall[0] or 0,
                    "total_emails": overall[1] or 0,
                    "active_days": overall[2] or 0,
                    "by_category": by_category,
                    "by_severity": by_severity,
                }

        except Exception as e:
            self.logger.error(f"Error retrieving notification statistics: {e}")
            return {}

    def cleanup_old_events(self, days_to_keep: int = 90) -> int:
        """Clean up old notification events to manage database size"""
        try:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()

                cutoff_date = datetime.now() - timedelta(days=days_to_keep)

                # Delete old events
                cursor.execute(
                    """
                    DELETE FROM notification_events 
                    WHERE timestamp < ?
                """,
                    (cutoff_date.isoformat(),),
                )

                deleted_events = cursor.rowcount

                # Delete old statistics
                cursor.execute(
                    """
                    DELETE FROM notification_statistics 
                    WHERE date < ?
                """,
                    (cutoff_date.date().isoformat(),),
                )

                deleted_stats = cursor.rowcount

                conn.commit()

                self.logger.info(
                    f"Cleaned up {deleted_events} old events and {deleted_stats} old statistics"
                )
                return deleted_events

        except Exception as e:
            self.logger.error(f"Error cleaning up old events: {e}")
            return 0
