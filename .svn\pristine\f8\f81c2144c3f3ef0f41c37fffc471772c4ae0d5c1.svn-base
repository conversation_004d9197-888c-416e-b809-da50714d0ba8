#!/usr/bin/env python3
"""
Reprocess Document with Enhanced Prompts
Reprocesses an existing document to show the difference between basic and enhanced prompts
"""

import sys
import logging
from pathlib import Path
from datetime import datetime

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from document_database import DocumentDatabase, DocumentRecord
from metadata_extractor import MetadataExtractor
from config_manager import ConfigManager
from ollama_client import OllamaClient


def setup_logging():
    """Setup logging for tests"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )


def reprocess_revision_21():
    """Reprocess revision 21 with enhanced prompts"""
    print("🔄 Reprocessing Revision 21 with Enhanced Prompts")
    print("=" * 60)
    
    # Initialize components
    config_manager = ConfigManager()
    config = config_manager.load_config()
    
    print(f"🔧 Enhanced prompts enabled: {config.use_enhanced_prompts}")
    print(f"🔧 Enhanced prompts fallback: {config.enhanced_prompts_fallback}")
    
    # Initialize database
    db = DocumentDatabase("/app/data/reposense.db")
    
    # Get revision 21 document
    documents = db.get_documents(limit=100, repository_id="28910d6e-fbab-4474-a1ed-7c8fcc41b9a7")
    revision_21_doc = None

    for doc in documents:
        if doc.revision == 21:
            revision_21_doc = doc
            break
    
    if not revision_21_doc:
        print("❌ Could not find revision 21 document")
        return False
    
    print(f"📄 Found revision 21 document: {revision_21_doc.id}")
    print(f"📄 Repository: {revision_21_doc.repository_name}")
    print(f"📄 Author: {revision_21_doc.author}")
    print(f"📄 Files changed: {len(revision_21_doc.changed_paths) if revision_21_doc.changed_paths else 'Unknown'}")
    
    # Read the document content
    doc_path = Path(revision_21_doc.filepath)
    if not doc_path.exists():
        print(f"❌ Document file not found: {doc_path}")
        return False
    
    content = doc_path.read_text(encoding='utf-8')
    print(f"📝 Document content length: {len(content)} characters")
    
    # Initialize Ollama client and metadata extractor
    try:
        ollama_client = OllamaClient(config)
        metadata_extractor = MetadataExtractor(ollama_client, config_manager)
        
        print(f"\n🤖 Using AI model: {config.ollama_model}")
        print(f"🌐 Ollama host: {config.ollama_host}")
        
        # Test Ollama connection
        if not ollama_client.test_connection():
            print("❌ Cannot connect to Ollama - enhanced prompts will not work")
            return False
        
        print("✅ Ollama connection successful")
        
    except Exception as e:
        print(f"❌ Error initializing Ollama client: {e}")
        return False
    
    # Extract metadata with enhanced prompts
    print(f"\n🔍 Analyzing document with enhanced prompts...")
    
    try:
        # Use the enhanced metadata extraction
        metadata = metadata_extractor.extract_all_metadata(content, revision_21_doc)
        
        print(f"\n📊 Enhanced Analysis Results:")
        print(f"  Code Review Recommended: {metadata.get('code_review_recommended', 'Not determined')}")
        print(f"  Code Review Priority: {metadata.get('code_review_priority', 'Not determined')}")
        print(f"  Risk Level: {metadata.get('risk_level', 'Not determined')}")
        print(f"  Documentation Impact: {metadata.get('documentation_impact', 'Not determined')}")
        
        # Compare with original results
        print(f"\n📊 Original Analysis Results:")
        print(f"  Code Review Recommended: {revision_21_doc.code_review_recommended}")
        print(f"  Code Review Priority: {revision_21_doc.code_review_priority}")
        print(f"  Risk Level: {revision_21_doc.risk_level}")
        print(f"  Documentation Impact: {revision_21_doc.documentation_impact}")
        
        # Check if results are different
        changes = []
        if metadata.get('code_review_recommended') != revision_21_doc.code_review_recommended:
            changes.append("Code Review Recommendation")
        if metadata.get('code_review_priority') != revision_21_doc.code_review_priority:
            changes.append("Code Review Priority")
        if metadata.get('risk_level') != revision_21_doc.risk_level:
            changes.append("Risk Level")
        if metadata.get('documentation_impact') != revision_21_doc.documentation_impact:
            changes.append("Documentation Impact")
        
        if changes:
            print(f"\n🎯 Enhanced prompts produced different results for: {', '.join(changes)}")
        else:
            print(f"\n✅ Enhanced prompts produced the same results (analysis was already accurate)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during enhanced analysis: {e}")
        import traceback
        traceback.print_exc()
        return False


def show_enhanced_prompt_example():
    """Show what an enhanced prompt looks like for revision 21"""
    print(f"\n🎨 Enhanced Prompt Example for Revision 21")
    print("=" * 60)
    
    try:
        from prompt_templates import PromptTemplateManager, ContextAnalyzer, ChangeContext, ChangeType, RiskContext
        from models import Config
        
        # Create test context for revision 21
        config = Config()
        config.use_enhanced_prompts = True
        
        # Create a mock document record for revision 21
        revision_21_context = DocumentRecord(
            id="reposense_ai_21",
            repository_id="28910d6e-fbab-4474-a1ed-7c8fcc41b9a7",
            repository_name="reposense_ai",
            revision=21,
            date=datetime.now(),
            filename="revision_21.md",
            filepath="/app/data/output/reposense_ai/revision_21.md",
            size=5000,
            author="fvaneijk",
            commit_message="Add historical scan reset and progress endpoints",
            changed_paths=[
                "web_interface.py",
                "historical_scanner.py", 
                "document_service.py",
                "templates/repositories.html"
            ],
            repository_type="svn"
        )
        
        # Initialize enhanced prompt system
        prompt_manager = PromptTemplateManager(config)
        context_analyzer = ContextAnalyzer(config)
        
        # Analyze context
        context = context_analyzer.analyze_change_context(revision_21_context)
        
        print(f"🔍 Detected Change Type: {context.change_type}")
        print(f"🔍 Risk Context: {context.risk_context}")
        print(f"🔍 Programming Languages: {context.programming_languages}")
        print(f"🔍 Project Type: {context.project_type}")
        print(f"🔍 Affects API: {context.affects_api}")
        print(f"🔍 Affects Configuration: {context.affects_configuration}")
        
        # Generate enhanced prompts
        test_content = "Sample documentation content for revision 21..."
        system_prompt, user_prompt = prompt_manager.get_metadata_extraction_prompt(test_content, context)
        
        print(f"\n📝 Enhanced System Prompt (first 500 chars):")
        print(f"```\n{system_prompt[:500]}...\n```")
        
        print(f"\n📝 Enhanced User Prompt (first 500 chars):")
        print(f"```\n{user_prompt[:500]}...\n```")
        
        print(f"\n📊 Prompt Statistics:")
        print(f"  System prompt length: {len(system_prompt)} characters")
        print(f"  User prompt length: {len(user_prompt)} characters")
        print(f"  Total prompt length: {len(system_prompt) + len(user_prompt)} characters")
        
        return True
        
    except Exception as e:
        print(f"❌ Error generating enhanced prompt example: {e}")
        return False


def main():
    """Run document reprocessing"""
    setup_logging()
    
    try:
        # Show enhanced prompt example
        show_enhanced_prompt_example()
        
        # Reprocess revision 21
        success = reprocess_revision_21()
        
        print("\n" + "=" * 60)
        if success:
            print("🎉 Document reprocessing completed!")
            print("✅ Enhanced prompts are working correctly")
            print("📈 Future revisions will automatically use enhanced analysis")
        else:
            print("❌ Document reprocessing failed")
            print("🔧 Check Ollama connection and configuration")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Reprocessing failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
