#!/usr/bin/env python3
"""
Main monitoring service that orchestrates all components
Coordinates repository monitoring, content generation, and notifications
"""

import logging
import sys
import threading
import time
from datetime import datetime
from pathlib import Path
from typing import Optional

from config_manager import ConfigManager
from document_database import DocumentDatabase
from email_service import EmailService
from file_manager import FileManager
from metadata_extractor import MetadataExtractor
from models import Config, NotificationCategory, RepositoryConfig
from notification_database import NotificationDatabase
from notification_system import (
    EventFactory,
    NotificationManager,
    NotificationSeverity,
    emit_system_shutdown,
    emit_system_startup,
)
from ollama_client import OllamaClient
from repository_backends import get_backend_manager
from unified_document_processor import UnifiedDocumentProcessor


class MonitorService:
    """Main service that orchestrates repository monitoring and processing"""

    unified_processor: Optional[UnifiedDocumentProcessor]
    notification_manager: Optional[NotificationManager]
    notification_db: Optional[NotificationDatabase]

    def __init__(self, config_path: str = "/app/data/config.json"):
        self.config_path = config_path
        self.db_path = "/app/data/reposense.db"  # Single database path for all data
        self.config_manager = ConfigManager(config_path)
        self.config = self.config_manager.load_config()

        self.setup_logging()

        # Initialize components
        self.backend_manager = get_backend_manager()
        self.ollama_client = OllamaClient(self.config)

        # Initialize database-dependent components
        from repository_database import RepositoryDatabase
        from user_database import UserDatabase

        self.repo_db = RepositoryDatabase(self.db_path)
        self.user_db = UserDatabase(self.db_path)
        self.file_manager = FileManager(self.config, self.repo_db)

        # Initialize LDAP sync service if enabled
        self.ldap_sync_service = None
        self.last_ldap_sync = None
        if self.config.ldap_sync_enabled:
            try:
                from ldap_sync_service import LDAPSyncService

                self.ldap_sync_service = LDAPSyncService(self.config, self.user_db)
                self.logger.info("✅ LDAP sync service initialized")
            except ImportError as e:
                self.logger.error(f"❌ LDAP sync service unavailable: {e}")
                self.ldap_sync_service = None
            except Exception as e:
                self.logger.error(f"❌ Failed to initialize LDAP sync service: {e}")
                self.ldap_sync_service = None

        # Initialize email service with database access
        self.email_service = EmailService(self.config, self.user_db, self.repo_db)

        # Initialize notification system
        try:
            self.notification_db = NotificationDatabase()
            self.notification_manager = NotificationManager(
                self.config, self.email_service
            )

            # Register event handlers
            self.notification_manager.register_handler(
                NotificationCategory.COMMITS, self._handle_commit_notification
            )

            self.logger.info("✅ Notification system initialized")
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize notification system: {e}")
            self.notification_manager = None
            self.notification_db = None

        # Initialize unified document processor for database integration
        try:
            self.unified_processor = UnifiedDocumentProcessor(
                output_dir=self.config.output_dir,
                db_path=self.db_path,
                ollama_client=self.ollama_client,
                config_manager=self.config_manager,
                max_concurrent_tasks=2,  # Lower concurrency for monitor service
                notification_manager=self.notification_manager,
            )
            self.unified_processor.start()
            self.logger.info(
                "✅ Unified document processor initialized for monitor service"
            )
        except Exception as e:
            self.logger.error(
                f"❌ Failed to initialize unified document processor: {e}"
            )
            self.unified_processor = None

        # Setup directories
        self.file_manager.setup_directories()

        # Monitoring state
        self.running = False
        self.monitor_thread = None
        self.last_check_time = None

        # Cached status to avoid slow dashboard loads
        self.ollama_connected_cache = None
        self.ollama_cache_time = None
        self.ollama_cache_duration = (
            10  # Cache for 10 seconds (reduced for better responsiveness)
        )

        # Cached models list
        self.ollama_models_cache = None
        self.ollama_models_cache_time = None
        self.ollama_models_cache_duration = (
            60  # Cache models for 1 minute (reduced for better responsiveness)
        )

        self.logger.info("Monitor service initialized")

    def setup_logging(self):
        """Setup logging configuration with rotation"""
        from logging.handlers import RotatingFileHandler

        handlers = [logging.StreamHandler(sys.stdout)]

        # Get rotation settings from configuration
        max_bytes = (
            self.config.log_rotation_max_size_mb * 1024 * 1024
        )  # Convert MB to bytes
        backup_count = self.config.log_rotation_backup_count

        # Try to add rotating file handler, but don't fail if directory doesn't exist
        try:
            log_dir = Path("/app/data")
            log_dir.mkdir(parents=True, exist_ok=True)

            # Use RotatingFileHandler with configurable size and backup count
            rotating_handler = RotatingFileHandler(
                "/app/data/reposense_ai.log",
                maxBytes=max_bytes,
                backupCount=backup_count,
            )
            handlers.append(rotating_handler)
        except (OSError, PermissionError):
            # Fallback to local directory or temp directory
            try:
                log_dir = Path("./data")
                log_dir.mkdir(parents=True, exist_ok=True)

                rotating_handler = RotatingFileHandler(
                    "./data/reposense_ai.log",
                    maxBytes=max_bytes,
                    backupCount=backup_count,
                )
                handlers.append(rotating_handler)
            except (OSError, PermissionError):
                # Just use console logging if file logging fails
                pass

        logging.basicConfig(
            level=logging.DEBUG,
            format="%(asctime)s - %(levelname)s - %(message)s",
            handlers=handlers,
        )
        self.logger = logging.getLogger(__name__)

    def reload_config(self):
        """Reload configuration and update components"""
        self.config = self.config_manager.load_config()

        # Update components with new config
        self.ollama_client.config = self.config
        self.email_service.config = self.config
        self.file_manager.config = self.config

        # Clear cached status to force refresh with new config
        self.ollama_connected_cache = None
        self.ollama_cache_time = None
        self.ollama_models_cache = None
        self.ollama_models_cache_time = None

        self.logger.info("Configuration reloaded")

    def save_config(self):
        """Save current configuration"""
        self.config_manager.save_config(self.config)

    def update_config(self, new_config: Config):
        """Update configuration and save it"""
        self.config = new_config
        self.save_config()
        self.reload_config()

    def process_commit(self, repo: "RepositoryConfig", revision: str) -> bool:
        """Process a single commit for a specific repository

        Returns:
            bool: True if processing completed successfully, False otherwise
        """
        backend = self.backend_manager.get_backend_for_repository(repo, self.config)
        if not backend:
            self.logger.error(f"No backend available for repository {repo.name}")
            return False

        commit = backend.get_commit_info(repo, revision)
        if not commit:
            self.logger.error(
                f"Could not get commit info for revision {revision} in {repo.name}"
            )
            return False

        self.logger.info(
            f"Processing commit {revision} by {commit.author} in {repo.name}"
        )

        # Extract and lookup change request information BEFORE documentation generation
        self.logger.debug(
            f"Change request integration check for commit {commit.revision}:"
        )
        self.logger.debug(
            f"  - unified_processor: {self.unified_processor is not None}"
        )
        self.logger.debug(
            f"  - change_request_service: {self.unified_processor.change_request_service is not None if self.unified_processor else 'N/A'}"
        )
        self.logger.debug(f"  - commit.message: {bool(commit.message)}")

        if (
            self.unified_processor
            and self.unified_processor.change_request_service
            and commit.message
        ):
            try:
                cr_numbers = self.unified_processor.change_request_service.extract_change_request_numbers(
                    commit.message
                )
                if cr_numbers:
                    self.logger.info(
                        f"Found change request numbers in commit {commit.revision}: {cr_numbers}"
                    )

                    # Retrieve change request information
                    change_requests = self.unified_processor.change_request_service.get_multiple_change_requests(
                        cr_numbers
                    )

                    # Update commit info with change request data
                    commit.change_request_numbers = cr_numbers
                    commit.change_requests = change_requests

                    if change_requests:
                        self.logger.info(
                            f"Retrieved {len(change_requests)} change request records for commit {commit.revision}"
                        )
                        for cr in change_requests:
                            self.logger.debug(
                                f"  CR #{cr.number}: {cr.title} (Priority: {cr.priority}, Status: {cr.status})"
                            )
                    else:
                        self.logger.debug(
                            f"No change request records found for numbers: {cr_numbers}"
                        )
            except Exception as e:
                self.logger.warning(
                    f"Error processing change requests for commit {commit.revision}: {e}"
                )
                # Continue processing even if change request lookup fails

        # Generate documentation
        documentation_success = True
        if self.config.generate_docs:
            try:
                documentation = self.ollama_client.generate_documentation(commit)
                if documentation:
                    if documentation.startswith("Error:"):
                        self.logger.error(
                            f"Documentation generation failed for revision {revision} in {repo.name}: {documentation}"
                        )
                        documentation_success = False
                    else:
                        saved_path = self.file_manager.save_documentation(
                            commit, documentation
                        )
                        if saved_path:
                            self.logger.info(
                                f"Documentation successfully generated and saved for revision {revision}"
                            )

                            # Verify the file was actually created and is readable
                            import os

                            if (
                                os.path.exists(saved_path)
                                and os.path.getsize(saved_path) > 0
                            ):
                                self.logger.debug(
                                    f"✅ Verified document file exists: {saved_path}"
                                )

                                # Use unified processor to handle database insertion and metadata extraction
                                # This ensures consistency with historical scanner processing
                                if (
                                    hasattr(self, "unified_processor")
                                    and self.unified_processor
                                ):
                                    try:
                                        success = self.unified_processor.process_commit(
                                            commit_info=commit,
                                            repository_config=repo,
                                            documentation=documentation,
                                            priority=5,  # Normal priority for real-time commits
                                        )
                                        if not success:
                                            self.logger.error(
                                                f"Failed to process commit {revision} through unified processor"
                                            )
                                            documentation_success = False
                                    except Exception as e:
                                        self.logger.error(
                                            f"Exception during unified processor handling for revision {revision}: {e}"
                                        )
                                        documentation_success = False
                                else:
                                    self.logger.warning(
                                        f"Unified processor not available - document {revision} saved to file but not processed for database"
                                    )
                            else:
                                self.logger.error(
                                    f"❌ Document file verification failed: {saved_path}"
                                )
                                documentation_success = False
                        else:
                            self.logger.error(
                                f"Documentation generated but failed to save for revision {revision} - check file permissions and disk space"
                            )
                            documentation_success = False
                else:
                    self.logger.warning(
                        f"No documentation generated for revision {revision} - this may indicate an Ollama service issue or model unavailability"
                    )
                    documentation_success = False
            except Exception as e:
                self.logger.error(
                    f"Critical error during documentation generation for revision {revision} in {repo.name}: {e}"
                )
                documentation_success = False
        else:
            self.logger.debug("Documentation generation disabled")

        # Generate and send email
        email_success = True
        if self.config.send_emails and self.email_service.is_configured():
            try:
                subject, body = self.ollama_client.generate_email_content(commit)
                if subject and body:
                    if self.email_service.send_email(subject, body, commit):
                        self.file_manager.save_email_copy(subject, body, commit)
                    else:
                        email_success = False
            except Exception as e:
                self.logger.error(f"Error sending email for revision {revision}: {e}")
                email_success = False

        # Return success only if all enabled features completed successfully
        overall_success = documentation_success and email_success
        if overall_success:
            self.logger.info(f"✅ Commit {revision} processing completed successfully")
        else:
            self.logger.warning(
                f"⚠️ Commit {revision} processing completed with some failures"
            )

        # Emit notification event for the commit
        if self.notification_manager:
            try:
                # Create commit info dictionary for the event
                commit_info = {
                    "revision": commit.revision,
                    "author": commit.author,
                    "repository_name": repo.name,
                    "message": commit.message,
                    "date": commit.date,
                    "changed_paths": commit.changed_paths,
                }

                # Determine risk level (you can enhance this logic)
                risk_level = "LOW"  # Default risk level
                # TODO: Add risk assessment logic here

                # Create and emit the event
                event = EventFactory.create_commit_event(
                    commit_info, repo.id, risk_level
                )
                self.notification_manager.emit_event(event)

            except Exception as e:
                self.logger.error(f"Error emitting commit notification: {e}")

        return overall_success

    def check_for_new_commits(self):
        """Check for new commits and process them across all enabled repositories"""
        # Get enabled repositories and expand any with monitor_all_branches=True
        enabled_repos = self.repo_db.get_enabled_repositories()

        if not enabled_repos:
            self.logger.warning("No enabled repositories to monitor")
            return

        # Use enabled repositories directly
        effective_repos = enabled_repos

        self.logger.info(
            f"Monitoring {len(effective_repos)} effective repositories (expanded from {len(enabled_repos)} configured repositories)"
        )

        for repo in effective_repos:
            if not self.running:  # Check if we should stop
                break

            self.check_repository_for_new_commits(repo)

    def check_repository_for_new_commits(self, repo: RepositoryConfig):
        """Check for new commits in a specific repository"""
        try:
            backend = self.backend_manager.get_backend_for_repository(repo, self.config)
            if not backend:
                self.logger.error(f"No backend available for repository {repo.name}")
                return

            latest_revision_str = backend.get_latest_revision(repo)
            if not latest_revision_str:
                self.logger.warning(f"Could not get latest revision for {repo.name}")
                return

            # Convert to int for SVN (other backends may need different handling)
            try:
                latest_revision = int(latest_revision_str)
            except ValueError:
                self.logger.error(
                    f"Invalid revision format for {repo.name}: {latest_revision_str}"
                )
                return

            if repo.last_revision == 0:
                # First run for this repository, just set the current revision
                repo.last_revision = latest_revision
                repo.last_processed_time = datetime.now()

                # Get the commit date for the latest revision
                commit_info = backend.get_commit_info(repo, latest_revision_str)
                if commit_info and commit_info.date:
                    try:
                        # Parse the commit date (SVN format: 2025-08-02T20:14:16.000000Z)
                        commit_date_str = commit_info.date.replace("Z", "+00:00")
                        repo.last_commit_date = datetime.fromisoformat(
                            commit_date_str.replace("T", " ").split("+")[0]
                        )
                    except (ValueError, AttributeError) as e:
                        self.logger.warning(
                            f"Could not parse commit date for {repo.name}: {e}"
                        )

                # Save repository to database after initial setup
                if self.repo_db.update_repository(repo):
                    self.logger.info(
                        f"Initial setup for {repo.name}: setting last revision to {latest_revision}"
                    )
                else:
                    self.logger.error(
                        f"❌ Failed to update repository {repo.name} in database during initial setup"
                    )
                return

            if latest_revision > repo.last_revision:
                new_commits = latest_revision - repo.last_revision
                self.logger.info(f"Found {new_commits} new commits in {repo.name}")

                # Process each new commit individually and update last_revision after each success
                for revision in range(repo.last_revision + 1, latest_revision + 1):
                    if not self.running:  # Check if we should stop
                        break

                    try:
                        # Process the commit and wait for completion
                        processing_success = self.process_commit(repo, str(revision))

                        if processing_success:
                            # Only update last_revision AFTER successful processing
                            repo.last_revision = revision
                            repo.last_processed_time = datetime.now()

                            # Get the commit date for this revision
                            commit_info = backend.get_commit_info(repo, str(revision))
                            if commit_info and commit_info.date:
                                try:
                                    # Parse the commit date (SVN format: 2025-08-02T20:14:16.000000Z)
                                    commit_date_str = commit_info.date.replace(
                                        "Z", "+00:00"
                                    )
                                    repo.last_commit_date = datetime.fromisoformat(
                                        commit_date_str.replace("T", " ").split("+")[0]
                                    )
                                except (ValueError, AttributeError) as e:
                                    self.logger.warning(
                                        f"Could not parse commit date for {repo.name}: {e}"
                                    )

                            # Save repository to database after each successful revision
                            if self.repo_db.update_repository(repo):
                                self.logger.info(
                                    f"✅ Successfully processed revision {revision} for {repo.name} and updated last_revision to {revision}"
                                )
                            else:
                                self.logger.error(
                                    f"❌ Failed to update repository {repo.name} in database after processing revision {revision}"
                                )
                        else:
                            self.logger.error(
                                f"❌ Processing failed for revision {revision} in {repo.name} - not updating last_revision"
                            )
                            # Don't update last_revision if processing failed, but continue with next revision
                            # This allows the system to retry this revision on the next monitoring cycle

                    except Exception as e:
                        self.logger.error(
                            f"❌ Exception during processing of revision {revision} for {repo.name}: {e}"
                        )
                        # Don't update last_revision if processing failed
                        break

        except Exception as e:
            self.logger.error(f"Error checking repository {repo.name}: {e}")

    def run_once(self):
        """Run a single check cycle"""
        try:
            self.logger.info("Checking for new commits...")
            self.check_for_new_commits()
        except Exception as e:
            self.logger.error(f"Error during check cycle: {e}")

    def _should_run_ldap_sync(self) -> bool:
        """Check if LDAP sync should run based on interval"""
        if not self.ldap_sync_service or not self.config.ldap_sync_enabled:
            return False

        if self.last_ldap_sync is None:
            return True  # First run

        # Check if enough time has passed since last sync
        try:
            last_sync_time = datetime.fromisoformat(self.last_ldap_sync)
            current_time = datetime.now()
            elapsed_seconds = (current_time - last_sync_time).total_seconds()
            return elapsed_seconds >= self.config.ldap_sync_interval
        except (ValueError, TypeError):
            return True  # If we can't parse the time, run sync

    def _run_ldap_sync(self):
        """Run LDAP user synchronization"""
        if not self.ldap_sync_service:
            self.logger.error("LDAP sync service not available")
            return

        try:
            self.logger.info("Starting LDAP user synchronization")
            stats = self.ldap_sync_service.sync_users_from_ldap()

            self.last_ldap_sync = datetime.now().isoformat()

            # Log sync results
            self.logger.info(
                f"LDAP sync completed: {stats['created']} created, "
                f"{stats['updated']} updated, {stats['errors']} errors, "
                f"{stats['skipped']} skipped from {stats['total_ldap_users']} LDAP users"
            )

            # Emit notification for significant sync events
            if self.notification_manager and (
                stats["created"] > 0 or stats["errors"] > 0
            ):
                severity = (
                    NotificationSeverity.WARNING
                    if stats["errors"] > 0
                    else NotificationSeverity.INFO
                )
                message = f"LDAP sync: {stats['created']} users created, {stats['updated']} updated"
                if stats["errors"] > 0:
                    message += f", {stats['errors']} errors"

                # Create system event for LDAP sync
                event = EventFactory.create_system_health_event(
                    title="LDAP User Synchronization",
                    message=message,
                    severity=severity,
                )
                self.notification_manager.emit_event(event)

        except Exception as e:
            self.logger.error(f"LDAP sync failed: {e}")

            # Emit error notification
            if self.notification_manager:
                event = EventFactory.create_system_health_event(
                    title="LDAP Sync Failed",
                    message=f"LDAP user synchronization failed: {str(e)}",
                    severity=NotificationSeverity.ERROR,
                )
                self.notification_manager.emit_event(event)

    def run_daemon(self):
        """Run continuously as a daemon"""
        self.logger.info(
            f"Starting RepoSense AI daemon (checking every {self.config.check_interval} seconds)"
        )

        while self.running:
            try:
                self.run_once()

                # Check if LDAP sync should run
                if self._should_run_ldap_sync():
                    self._run_ldap_sync()

                self.last_check_time = datetime.now().isoformat()
                time.sleep(self.config.check_interval)
            except Exception as e:
                self.logger.error(f"Unexpected error in daemon loop: {e}")
                time.sleep(60)  # Wait a minute before retrying

        self.logger.info("Daemon stopped")

    def start_monitoring(self):
        """Start the monitoring process in a separate thread"""
        if not self.running:
            self.running = True
            self.monitor_thread = threading.Thread(target=self.run_daemon, daemon=True)
            self.monitor_thread.start()
            self.logger.info("Monitoring started")

            # Emit system startup notification
            self.emit_system_startup_notification()

    def stop_monitoring(self):
        """Stop the monitoring process"""
        if self.running:
            self.running = False
            if self.monitor_thread:
                self.monitor_thread.join(timeout=5)

            # Stop unified processor if it exists
            if self.unified_processor:
                try:
                    self.unified_processor.stop()
                    self.logger.info("✅ Unified document processor stopped")
                except Exception as e:
                    self.logger.error(
                        f"❌ Error stopping unified document processor: {e}"
                    )

            self.logger.info("Monitoring stopped")

            # Emit system shutdown notification
            self.emit_system_shutdown_notification()

    def is_running(self):
        """Check if monitoring is currently running"""
        return self.running and self.monitor_thread and self.monitor_thread.is_alive()

    def get_ollama_connection_status(self):
        """Get Ollama connection status with caching to avoid slow dashboard loads"""
        current_time = time.time()

        # Return cached result if still valid
        if (
            self.ollama_connected_cache is not None
            and self.ollama_cache_time is not None
            and current_time - self.ollama_cache_time < self.ollama_cache_duration
        ):
            return self.ollama_connected_cache

        # Test connection and cache result
        try:
            self.ollama_connected_cache = self.ollama_client.test_connection()
            self.ollama_cache_time = current_time
            return self.ollama_connected_cache
        except Exception as e:
            self.logger.warning(f"Error testing Ollama connection: {e}")
            self.ollama_connected_cache = False
            self.ollama_cache_time = current_time

            # Emit system health notification for Ollama connection failure
            if self.notification_manager:
                try:
                    event = EventFactory.create_system_health_event(
                        "Ollama Connection Failed",
                        f"Unable to connect to Ollama service: {str(e)}",
                        NotificationSeverity.ERROR,
                    )
                    self.notification_manager.emit_event(event)
                except Exception as notification_error:
                    self.logger.error(
                        f"Error emitting Ollama connection notification: {notification_error}"
                    )

            return False

    def get_available_models(self):
        """Get available Ollama models with caching"""
        current_time = time.time()

        # Return cached result if still valid
        if (
            self.ollama_models_cache is not None
            and self.ollama_models_cache_time is not None
            and current_time - self.ollama_models_cache_time
            < self.ollama_models_cache_duration
        ):
            return self.ollama_models_cache

        # Get models and cache result
        try:
            models = self.ollama_client.get_available_models()
            self.ollama_models_cache = models
            self.ollama_models_cache_time = current_time
            return models
        except Exception as e:
            self.logger.warning(f"Error getting Ollama models: {e}")
            # Return cached result if available, otherwise empty list
            return (
                self.ollama_models_cache if self.ollama_models_cache is not None else []
            )

    def clear_ollama_cache(self):
        """Clear Ollama connection and models cache to force refresh"""
        self.ollama_connected_cache = None
        self.ollama_cache_time = None
        self.ollama_models_cache = None
        self.ollama_models_cache_time = None
        self.logger.debug("Cleared Ollama connection and models cache")

    def get_status(self):
        """Get current status information"""
        enabled_repos = self.repo_db.get_enabled_repositories()

        # Repository status information
        repositories_status = []
        for repo in self.repo_db.get_all_repositories():
            repositories_status.append(
                {
                    "id": repo.id,
                    "name": repo.name,
                    "url": repo.url,
                    "enabled": repo.enabled,
                    "last_revision": repo.last_revision,
                    "last_commit_date": repo.last_commit_date.isoformat()
                    if repo.last_commit_date
                    else None,
                    "last_processed_time": repo.last_processed_time.isoformat()
                    if repo.last_processed_time
                    else None,
                }
            )

        # Get LDAP sync status
        ldap_status = None
        if self.ldap_sync_service:
            ldap_status = self.ldap_sync_service.get_sync_status()
            ldap_status["last_sync"] = self.last_ldap_sync

        return {
            "running": self.is_running(),
            "repositories": repositories_status,
            "enabled_repositories_count": len(enabled_repos),
            "total_repositories_count": len(self.repo_db.get_all_repositories()),
            "config_valid": len(enabled_repos) > 0,
            "ollama_connected": self.get_ollama_connection_status(),
            "last_check": self.last_check_time,
            "email_configured": self.email_service.is_configured(),
            "ldap_sync": ldap_status,
        }

    def _handle_commit_notification(self, event):
        """Handle commit notification events"""
        try:
            # Store event in database
            if self.notification_db:
                self.notification_db.store_event(event)

            self.logger.info(f"Processed commit notification: {event.title}")
        except Exception as e:
            self.logger.error(f"Error handling commit notification: {e}")

    def emit_system_startup_notification(self):
        """Emit system startup notification"""
        if self.notification_manager:
            emit_system_startup(self.notification_manager)

    def emit_system_shutdown_notification(self):
        """Emit system shutdown notification"""
        if self.notification_manager:
            emit_system_shutdown(self.notification_manager)

    def trigger_ldap_sync(self) -> dict:
        """Manually trigger LDAP sync and return results"""
        if not self.ldap_sync_service:
            return {"success": False, "message": "LDAP sync service not available"}

        if not self.config.ldap_sync_enabled:
            return {"success": False, "message": "LDAP sync is disabled"}

        try:
            self.logger.info("Manual LDAP sync triggered")
            stats = self.ldap_sync_service.sync_users_from_ldap()
            self.last_ldap_sync = datetime.now().isoformat()

            return {
                "success": True,
                "message": f"LDAP sync completed: {stats['created']} created, {stats['updated']} updated, {stats['errors']} errors",
                "stats": stats,
            }
        except Exception as e:
            self.logger.error(f"Manual LDAP sync failed: {e}")
            return {"success": False, "message": f"LDAP sync failed: {str(e)}"}

    def test_ldap_connection(self, form_data: Optional[dict] = None) -> dict:
        """Test LDAP connection and return results"""
        if not self.ldap_sync_service:
            return {"success": False, "message": "LDAP sync service not available"}

        try:
            success, message = self.ldap_sync_service.test_connection(form_data)
            return {"success": success, "message": message}
        except Exception as e:
            return {
                "success": False,
                "message": f"LDAP connection test failed: {str(e)}",
            }
