#!/usr/bin/env python3
"""
Complete end-to-end test for Change Request Pipeline

This test creates:
1. A real change request in the SQL database
2. A commit that references the change request
3. Tests the complete pipeline: retrieval → correlation → risk voting → documentation
"""

import os
import sqlite3
import sys
import tempfile
from datetime import datetime
from pathlib import Path

# Add the reposense_ai directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), "reposense_ai"))

from change_request_service import ChangeRequestService
from config_manager import ConfigManager
from document_database import DocumentDatabase
from models import CommitInfo, Config, RepositoryConfig, SqlConfig
from unified_document_processor import UnifiedDocumentProcessor


class CompleteChangeRequestPipelineTest:
    """Complete end-to-end change request pipeline test"""

    def __init__(self):
        self.test_db_path = None
        self.change_request_service = None
        self.unified_processor = None
        self.document_db = None

    def setup_test_database(self) -> str:
        """Create a temporary test database with change request data"""
        # Create temporary database
        fd, self.test_db_path = tempfile.mkstemp(suffix="_cr_test.db")
        os.close(fd)

        conn = sqlite3.connect(self.test_db_path)
        cursor = conn.cursor()

        # Create change requests table (matching your SQL schema)
        cursor.execute("""
            CREATE TABLE change_requests (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                number VARCHAR(50) UNIQUE NOT NULL,
                title TEXT NOT NULL,
                description TEXT,
                status VARCHAR(20) DEFAULT 'OPEN',
                priority VARCHAR(20) DEFAULT 'MEDIUM',
                category VARCHAR(50),
                assigned_to VARCHAR(100),
                created_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                risk_level VARCHAR(20),
                business_impact TEXT,
                technical_notes TEXT
            )
        """)

        # Insert test change request (use simple number to match default patterns)
        test_cr = {
            "number": "12345",
            "title": "Implement Camera Auto-Capture with Interval Control",
            "description": """Add automatic camera capture functionality with configurable interval settings.
            
Requirements:
- Add interval control (1-3600 seconds)
- Add start/stop button for user control
- Update camera control UI with new controls
- Ensure proper error handling for camera disconnection
- Add logging for capture events

Business Justification:
This feature will enable automated surveillance capabilities, reducing manual intervention 
and improving monitoring efficiency for security applications.""",
            "status": "APPROVED",
            "priority": "HIGH",
            "category": "FEATURE_ENHANCEMENT",
            "assigned_to": "fvaneijk",
            "created_date": "2016-06-15 09:00:00",
            "updated_date": "2016-06-20 16:30:00",
            "risk_level": "MEDIUM",
            "business_impact": "High - Enables automated surveillance, reduces manual monitoring costs",
            "technical_notes": "Requires UI changes to CameraControl.Designer.cs and MainForm.Designer.cs. Add timer controls and event handlers.",
        }

        cursor.execute(
            """
            INSERT INTO change_requests 
            (number, title, description, status, priority, category, assigned_to, 
             created_date, updated_date, risk_level, business_impact, technical_notes)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (
                test_cr["number"],
                test_cr["title"],
                test_cr["description"],
                test_cr["status"],
                test_cr["priority"],
                test_cr["category"],
                test_cr["assigned_to"],
                test_cr["created_date"],
                test_cr["updated_date"],
                test_cr["risk_level"],
                test_cr["business_impact"],
                test_cr["technical_notes"],
            ),
        )

        conn.commit()
        conn.close()

        print(f"✅ Test database created: {self.test_db_path}")
        print(f"✅ Change request created: {test_cr['number']}")
        return self.test_db_path

    def setup_change_request_service(self):
        """Setup change request service with test database"""
        sql_config = SqlConfig(
            enabled=True,
            driver="sqlite",
            database=self.test_db_path,
            change_request_query="""
                SELECT id, number, title, description, priority, status,
                       created_date, assigned_to, category, risk_level,
                       business_impact, technical_notes
                FROM change_requests
                WHERE number = :change_request_number
            """,
        )

        # Create config with SQL settings
        config = Config(sql_config=sql_config)
        self.change_request_service = ChangeRequestService(config)
        print("✅ Change request service configured")

    def setup_unified_processor(self):
        """Setup unified document processor"""
        try:
            config_manager = ConfigManager()

            # Use the main document database
            self.document_db = DocumentDatabase("/app/data/reposense.db")

            self.unified_processor = UnifiedDocumentProcessor(
                config_manager=config_manager
            )
            print("✅ Unified document processor configured")
            return True
        except Exception as e:
            print(f"⚠️  Could not setup unified processor: {e}")
            return False

    def create_test_commit(self) -> CommitInfo:
        """Create a test commit that references the change request"""
        return CommitInfo(
            revision="7",
            author="fvaneijk",
            date="2016-06-21 01:44:41",
            message="Implement camera interval control and start button - CR-12345",
            changed_paths=[
                "/CaptureCam/AutoKams/Controls/CameraControl.Designer.cs",
                "/CaptureCam/AutoKams/MainForm.Designer.cs",
            ],
            diff="""@@ -15,6 +15,25 @@ namespace AutoKams.Controls
             this.btnStart = new System.Windows.Forms.Button();
             this.lblStatus = new System.Windows.Forms.Label();
             this.pnlCamera = new System.Windows.Forms.Panel();
+            this.lblInterval = new System.Windows.Forms.Label();
+            this.numInterval = new System.Windows.Forms.NumericUpDown();
+            this.tmrCapture = new System.Windows.Forms.Timer(this.components);
+            ((System.ComponentModel.ISupportInitialize)(this.numInterval)).BeginInit();
             this.SuspendLayout();
             // 
+            // lblInterval
+            // 
+            this.lblInterval.AutoSize = true;
+            this.lblInterval.Location = new System.Drawing.Point(12, 50);
+            this.lblInterval.Name = "lblInterval";
+            this.lblInterval.Size = new System.Drawing.Size(45, 13);
+            this.lblInterval.TabIndex = 3;
+            this.lblInterval.Text = "Interval (seconds):";
+            // 
+            // numInterval
+            // 
+            this.numInterval.Location = new System.Drawing.Point(120, 48);
+            this.numInterval.Maximum = new decimal(new int[] { 3600, 0, 0, 0});
+            this.numInterval.Minimum = new decimal(new int[] { 1, 0, 0, 0});
+            this.numInterval.Name = "numInterval";
+            this.numInterval.Size = new System.Drawing.Size(80, 20);
+            this.numInterval.TabIndex = 4;
+            this.numInterval.Value = new decimal(new int[] { 5, 0, 0, 0});
+            // 
+            // tmrCapture - Timer for automatic capture
+            // 
+            this.tmrCapture.Tick += new System.EventHandler(this.tmrCapture_Tick);""",
            repository_id="visionApi",
            repository_name="visionApi",
        )

    def test_change_request_retrieval(self) -> bool:
        """Test 1: Change request retrieval from database"""
        print("\n🔍 Test 1: Change Request Retrieval")
        print("-" * 50)

        try:
            commit = self.create_test_commit()

            # Test retrieval by commit message reference
            cr_numbers = self.change_request_service.extract_change_request_numbers(
                commit.message
            )
            change_requests = self.change_request_service.get_multiple_change_requests(
                cr_numbers
            )

            print(f"📊 Extracted CR numbers: {cr_numbers}")

            print(f"📊 Commit: {commit.message}")
            print(f"📊 Author: {commit.author}")
            print(f"📊 Found {len(change_requests)} change requests")

            for cr in change_requests:
                print(f"   - {cr.number}: {cr.title}")
                print(f"     Priority: {cr.priority}, Risk: {cr.risk_level}")
                print(f"     Status: {cr.status}, Category: {cr.category}")

            if len(change_requests) > 0:
                expected_cr = next(
                    (cr for cr in change_requests if cr.number == "12345"),
                    None,
                )
                if expected_cr:
                    print("✅ Successfully retrieved expected change request")
                    return True
                else:
                    print("❌ Expected change request not found")
                    return False
            else:
                print("❌ No change requests found")
                return False

        except Exception as e:
            print(f"❌ Error in change request retrieval: {e}")
            import traceback

            traceback.print_exc()
            return False

    def test_complete_pipeline(self) -> bool:
        """Test 2: Complete pipeline with change request integration"""
        print("\n🔄 Test 2: Complete Pipeline Processing")
        print("-" * 50)

        if not self.unified_processor:
            print("⚠️  Unified processor not available - skipping pipeline test")
            return True

        try:
            commit = self.create_test_commit()

            # Get change requests for this commit
            cr_numbers = self.change_request_service.extract_change_request_numbers(
                commit.message
            )
            change_requests = self.change_request_service.get_multiple_change_requests(
                cr_numbers
            )

            # Add change requests to commit
            commit.change_requests = change_requests

            print(
                f"📊 Processing commit with {len(commit.change_requests)} change requests"
            )

            # Create a repository config for processing
            repo_config = RepositoryConfig(
                name=commit.repository_name,
                url="file:///test/repo",
                type="svn",
                monitor_all_branches=False,
                last_revision=int(commit.revision) - 1,
            )

            # Process through unified processor
            print("🔄 Processing through unified document processor...")
            # Generate some test documentation
            test_documentation = f"""# Revision {commit.revision} - {commit.repository_name}

## Summary
{commit.message}

## Technical Details
This commit implements camera interval control functionality with the following changes:
- Added interval control UI elements
- Added timer functionality for automatic capture
- Updated camera control designer files

## Files Changed
{chr(10).join(f"- {path}" for path in commit.changed_paths)}

## Change Request Context
{"This commit implements change request requirements for automated camera capture." if commit.change_requests else "No change request associated with this commit."}
"""

            success = self.unified_processor.process_commit(
                commit, repo_config, test_documentation
            )

            if success:
                print("✅ Commit processed successfully")

                # Verify document was created
                doc_id = f"default_{commit.repository_name}_{commit.revision}"
                document = self.document_db.get_document_by_id(doc_id)

                if document:
                    print(f"✅ Document created: {document.id}")
                    print(f"📊 Risk Level: {document.risk_level}")
                    print(f"📊 Code Review: {document.code_review_recommended}")
                    print(f"📊 Doc Impact: {document.documentation_impact}")
                    print(f"📊 AI Model: {document.ai_model_used}")

                    # Check if change request influenced risk assessment
                    if document.risk_level in ["MEDIUM", "HIGH", "CRITICAL"]:
                        print("✅ Risk assessment likely influenced by change request")

                    # Show document preview
                    if document.filepath and os.path.exists(document.filepath):
                        with open(document.filepath, "r", encoding="utf-8") as f:
                            content = f.read()
                            preview = (
                                content[:500] + "..." if len(content) > 500 else content
                            )
                            print(f"\n📄 Document Preview:")
                            print("-" * 40)
                            print(preview)

                    return True
                else:
                    print("❌ Document not found in database")
                    return False
            else:
                print("❌ Commit processing failed")
                return False

        except Exception as e:
            print(f"❌ Error in complete pipeline test: {e}")
            import traceback

            traceback.print_exc()
            return False

    def cleanup(self):
        """Clean up test resources"""
        if self.test_db_path and os.path.exists(self.test_db_path):
            os.unlink(self.test_db_path)
            print(f"🧹 Cleaned up test database: {self.test_db_path}")

    def run_all_tests(self) -> bool:
        """Run complete pipeline test"""
        print("🚀 Complete Change Request Pipeline Test")
        print("=" * 70)

        try:
            # Setup
            self.setup_test_database()
            self.setup_change_request_service()
            processor_available = self.setup_unified_processor()

            # Run tests
            tests = [
                ("Change Request Retrieval", self.test_change_request_retrieval),
                ("Complete Pipeline Processing", self.test_complete_pipeline),
            ]

            results = []
            for test_name, test_func in tests:
                try:
                    result = test_func()
                    results.append((test_name, result))
                except Exception as e:
                    print(f"❌ {test_name} failed with exception: {e}")
                    results.append((test_name, False))

            # Summary
            print("\n📊 Test Results Summary")
            print("=" * 70)

            passed = 0
            total = len(results)

            for test_name, result in results:
                status = "✅ PASS" if result else "❌ FAIL"
                print(f"{status} {test_name}")
                if result:
                    passed += 1

            print(f"\n🎯 Overall Result: {passed}/{total} tests passed")

            if passed == total:
                print(
                    "🎉 Complete pipeline working! Change requests are fully integrated."
                )
                return True
            else:
                print("⚠️  Some tests failed. Check the output above for details.")
                return False

        except Exception as e:
            print(f"❌ Test suite failed: {e}")
            import traceback

            traceback.print_exc()
            return False
        finally:
            self.cleanup()


def main():
    """Run the complete pipeline test"""
    test = CompleteChangeRequestPipelineTest()
    success = test.run_all_tests()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
