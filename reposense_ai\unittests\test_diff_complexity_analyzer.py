"""
Unit tests for the DiffComplexityAnalyzer class.

This module tests the diff complexity analysis functionality including:
- Diff parsing and line analysis
- Complexity metrics calculation
- File type categorization
- Risk assessment with different aggressiveness levels
- Pattern matching for code structures
- Edge cases and error handling
"""

from unittest.mock import Mock, patch

import pytest

# Import test utilities
from .test_utils import mock_config

# Import the module under test
try:
    from diff_complexity_analyzer import DiffComplexityAnalyzer, DiffComplexityMetrics
except ImportError:
    DiffComplexityAnalyzer = None
    DiffComplexityMetrics = None


@pytest.mark.unit
@pytest.mark.analyzer
class TestDiffComplexityMetrics:
    """Test cases for DiffComplexityMetrics dataclass."""

    def test_metrics_import(self):
        """Test that DiffComplexityMetrics can be imported successfully."""
        assert DiffComplexityMetrics is not None, (
            "DiffComplexityMetrics should be importable"
        )

    def test_metrics_initialization(self):
        """Test DiffComplexityMetrics initialization with default values."""
        if DiffComplexityMetrics is None:
            pytest.skip("DiffComplexityMetrics not available")

        metrics = DiffComplexityMetrics()

        # Test default values
        assert metrics.lines_added == 0
        assert metrics.lines_removed == 0
        assert metrics.lines_modified == 0
        assert metrics.files_changed == 0
        assert metrics.characters_added == 0
        assert metrics.characters_removed == 0

        # Complexity indicators
        assert metrics.new_functions == 0
        assert metrics.modified_functions == 0
        assert metrics.new_classes == 0
        assert metrics.modified_classes == 0
        assert metrics.control_flow_changes == 0
        assert metrics.import_changes == 0
        assert metrics.config_changes == 0

        # File type analysis
        assert metrics.code_files == 0
        assert metrics.config_files == 0
        assert metrics.documentation_files == 0
        assert metrics.test_files == 0
        assert metrics.binary_files == 0

        # Risk indicators
        assert metrics.large_file_changes == 0
        assert metrics.critical_file_changes == 0

    def test_metrics_with_custom_values(self):
        """Test DiffComplexityMetrics initialization with custom values."""
        if DiffComplexityMetrics is None:
            pytest.skip("DiffComplexityMetrics not available")

        metrics = DiffComplexityMetrics(
            lines_added=50,
            lines_removed=20,
            files_changed=3,
            new_functions=2,
            new_classes=1,
            code_files=2,
            config_files=1,
        )

        assert metrics.lines_added == 50
        assert metrics.lines_removed == 20
        assert metrics.files_changed == 3
        assert metrics.new_functions == 2
        assert metrics.new_classes == 1
        assert metrics.code_files == 2
        assert metrics.config_files == 1

    def test_total_lines_changed(self):
        """Test total_lines_changed calculation."""
        if DiffComplexityMetrics is None:
            pytest.skip("DiffComplexityMetrics not available")

        metrics = DiffComplexityMetrics(lines_added=30, lines_removed=15)
        assert metrics.total_lines_changed() == 45

        # Test with zero values
        metrics_zero = DiffComplexityMetrics()
        assert metrics_zero.total_lines_changed() == 0

    def test_net_lines_changed(self):
        """Test net_lines_changed calculation."""
        if DiffComplexityMetrics is None:
            pytest.skip("DiffComplexityMetrics not available")

        # More additions than removals
        metrics1 = DiffComplexityMetrics(lines_added=30, lines_removed=15)
        assert metrics1.net_lines_changed() == 15

        # More removals than additions
        metrics2 = DiffComplexityMetrics(lines_added=10, lines_removed=25)
        assert metrics2.net_lines_changed() == -15

        # Equal additions and removals
        metrics3 = DiffComplexityMetrics(lines_added=20, lines_removed=20)
        assert metrics3.net_lines_changed() == 0

    def test_complexity_score_calculation(self):
        """Test complexity score calculation with various scenarios."""
        if DiffComplexityMetrics is None:
            pytest.skip("DiffComplexityMetrics not available")

        # Test minimal complexity
        metrics_low = DiffComplexityMetrics(lines_added=5, files_changed=1)
        score_low = metrics_low.complexity_score()
        assert 0.0 <= score_low <= 1.0
        assert score_low < 0.1  # Should be very low

        # Test moderate complexity
        metrics_medium = DiffComplexityMetrics(
            lines_added=100,
            lines_removed=50,
            files_changed=5,
            new_functions=2,
            new_classes=1,
            control_flow_changes=3,
        )
        score_medium = metrics_medium.complexity_score()
        assert 0.1 <= score_medium <= 0.8

        # Test high complexity
        metrics_high = DiffComplexityMetrics(
            lines_added=500,
            lines_removed=200,
            files_changed=15,
            new_functions=8,
            new_classes=4,
            modified_functions=5,
            modified_classes=2,
            control_flow_changes=20,
            critical_file_changes=3,
        )
        score_high = metrics_high.complexity_score()
        assert score_high >= 0.5
        assert score_high <= 1.0  # Should be capped at 1.0

        # Test maximum complexity (should be capped at 1.0)
        metrics_max = DiffComplexityMetrics(
            lines_added=2000,
            lines_removed=1000,
            files_changed=50,
            new_functions=20,
            new_classes=10,
            modified_functions=15,
            modified_classes=8,
            control_flow_changes=50,
            critical_file_changes=10,
        )
        score_max = metrics_max.complexity_score()
        assert score_max == 1.0

    def test_complexity_score_components(self):
        """Test individual components of complexity score calculation."""
        if DiffComplexityMetrics is None:
            pytest.skip("DiffComplexityMetrics not available")

        # Test lines component (max 0.3)
        metrics_lines = DiffComplexityMetrics(lines_added=1000, lines_removed=0)
        score_lines = metrics_lines.complexity_score()
        assert score_lines >= 0.3  # Should include the max lines score

        # Test files component (max 0.2)
        metrics_files = DiffComplexityMetrics(files_changed=20)
        score_files = metrics_files.complexity_score()
        assert score_files >= 0.2  # Should include the max files score

        # Test structure component (max 0.3)
        metrics_structure = DiffComplexityMetrics(
            new_functions=5, new_classes=3, modified_functions=2
        )
        score_structure = metrics_structure.complexity_score()
        assert score_structure >= 0.3  # Should include the max structure score

        # Test control flow component (max 0.1)
        metrics_control = DiffComplexityMetrics(control_flow_changes=20)
        score_control = metrics_control.complexity_score()
        assert score_control >= 0.1  # Should include the max control score

        # Test critical files component (max 0.1)
        metrics_critical = DiffComplexityMetrics(critical_file_changes=5)
        score_critical = metrics_critical.complexity_score()
        assert score_critical >= 0.1  # Should include the max critical score


@pytest.mark.unit
@pytest.mark.analyzer
class TestDiffComplexityAnalyzer:
    """Test cases for DiffComplexityAnalyzer class."""

    def test_analyzer_import(self):
        """Test that DiffComplexityAnalyzer can be imported successfully."""
        assert DiffComplexityAnalyzer is not None, (
            "DiffComplexityAnalyzer should be importable"
        )

    def test_analyzer_initialization(self):
        """Test DiffComplexityAnalyzer initialization."""
        if DiffComplexityAnalyzer is None:
            pytest.skip("DiffComplexityAnalyzer not available")

        analyzer = DiffComplexityAnalyzer()

        # Test that patterns are initialized
        assert hasattr(analyzer, "code_extensions")
        assert hasattr(analyzer, "config_extensions")
        assert hasattr(analyzer, "doc_extensions")
        assert hasattr(analyzer, "test_patterns")
        assert hasattr(analyzer, "critical_patterns")
        assert hasattr(analyzer, "function_patterns")
        assert hasattr(analyzer, "class_patterns")
        assert hasattr(analyzer, "control_flow_patterns")
        assert hasattr(analyzer, "import_patterns")

        # Test some specific patterns
        assert ".py" in analyzer.code_extensions
        assert ".json" in analyzer.config_extensions
        assert ".md" in analyzer.doc_extensions
        assert "test_" in analyzer.test_patterns
        assert "package.json" in analyzer.critical_patterns

    def test_analyze_diff_empty_input(self):
        """Test analyze_diff with empty input."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Test with empty diff and no file paths
        metrics, confidence = analyzer.analyze_diff("", [])
        assert isinstance(metrics, DiffComplexityMetrics)
        assert metrics.total_lines_changed() == 0
        assert confidence == 0.0

        # Test with None inputs
        metrics, confidence = analyzer.analyze_diff(None, None)
        assert isinstance(metrics, DiffComplexityMetrics)
        assert metrics.total_lines_changed() == 0
        assert confidence == 0.0

    def test_analyze_diff_simple_changes(self):
        """Test analyze_diff with simple line changes."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Simple diff with additions and removals
        diff_content = """--- a/test.py
+++ b/test.py
@@ -1,3 +1,4 @@
 def hello():
-    print("Hello")
+    print("Hello World")
+    return True
"""

        metrics, confidence = analyzer.analyze_diff(diff_content)

        assert metrics.lines_added == 2
        assert metrics.lines_removed == 1
        assert metrics.total_lines_changed() == 3
        assert metrics.files_changed == 1
        assert confidence > 0.0

    def test_analyze_diff_with_file_paths(self):
        """Test analyze_diff with file paths but no diff content."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        file_paths = [
            "src/main.py",
            "config/settings.json",
            "docs/README.md",
            "tests/test_main.py",
        ]

        metrics, confidence = analyzer.analyze_diff("", file_paths)

        assert metrics.files_changed == 4
        assert metrics.code_files == 1  # main.py
        assert metrics.config_files == 1  # settings.json
        assert metrics.documentation_files == 1  # README.md
        assert metrics.test_files == 1  # test_main.py
        assert confidence > 0.0

    def test_analyze_diff_complex_code_changes(self):
        """Test analyze_diff with complex code changes including functions and classes."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Complex diff with function and class definitions
        diff_content = """--- a/complex.py
+++ b/complex.py
@@ -1,10 +1,20 @@
+import os
+from typing import Dict
+
+class DataProcessor:
+    def __init__(self):
+        self.data = {}
+
+    def process_data(self, input_data):
+        if input_data:
+            for item in input_data:
+                try:
+                    self.data[item.id] = item.value
+                except Exception as e:
+                    print(f"Error: {e}")
+        return self.data
+
 def main():
-    print("Simple function")
+    processor = DataProcessor()
+    return processor.process_data([])
"""

        metrics, confidence = analyzer.analyze_diff(diff_content)

        # Should detect structural changes
        assert metrics.lines_added > 15
        assert metrics.new_classes >= 1  # DataProcessor class
        assert metrics.new_functions >= 1  # process_data method
        assert metrics.import_changes >= 2  # import os, from typing
        assert metrics.control_flow_changes >= 3  # if, for, try
        assert confidence > 0.5

    def test_analyze_diff_critical_files(self):
        """Test analyze_diff with critical file changes."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        critical_files = [
            "package.json",
            "requirements.txt",
            "Dockerfile",
            "config.json",
            "settings.py",
        ]

        metrics, confidence = analyzer.analyze_diff("", critical_files)

        assert metrics.files_changed == 5
        assert (
            metrics.critical_file_changes >= 4
        )  # Most files are critical (settings.py might not match pattern)
        assert metrics.config_files >= 2  # package.json, config.json
        assert confidence >= 0.7  # High confidence due to critical files

    def test_analyze_diff_large_file_changes(self):
        """Test analyze_diff with large file changes (>100 lines)."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Create a diff with >100 lines changed in a file
        large_diff_lines = []
        large_diff_lines.append("--- a/large_file.py")
        large_diff_lines.append("+++ b/large_file.py")
        large_diff_lines.append("@@ -1,50 +1,150 @@")

        # Add 120 lines of changes
        for i in range(120):
            large_diff_lines.append(f"+    line_{i} = 'added line {i}'")

        diff_content = "\n".join(large_diff_lines)

        metrics, confidence = analyzer.analyze_diff(diff_content)

        assert metrics.lines_added == 120
        assert metrics.large_file_changes == 1  # Should detect large file change
        assert metrics.files_changed == 1
        assert confidence > 0.5

    def test_file_type_categorization(self):
        """Test file type categorization logic."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        test_files = [
            # Code files
            "src/main.py",
            "lib/utils.js",
            "core/processor.java",
            "engine/core.cpp",
            "api/handler.cs",
            # Config files
            "config/app.json",
            "settings/database.yml",
            "deploy/config.xml",
            "app.properties",
            # Documentation files
            "README.md",
            "docs/guide.txt",
            "manual.rst",
            # Test files
            "tests/test_main.py",
            "spec/utils_spec.js",
            "test/integration.test.js",
            # Binary files
            "bin/app.exe",
            "lib/library.dll",
            "assets/image.bin",
        ]

        metrics, confidence = analyzer.analyze_diff("", test_files)

        assert metrics.files_changed == len(test_files)
        assert metrics.code_files == 5
        assert metrics.config_files == 4
        assert metrics.documentation_files == 3
        assert metrics.test_files == 3
        assert metrics.binary_files == 3

    def test_pattern_matching_functions(self):
        """Test function pattern matching in different languages."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Test different function definition patterns
        diff_content = """--- a/multi_lang.txt
+++ b/multi_lang.txt
@@ -1,5 +1,15 @@
+def python_function(param):
+    return param
+
+function javascript_function(param) {
+    return param;
+}
+
+public int java_method(String param) {
+    return param.length();
+}
"""

        metrics, confidence = analyzer.analyze_diff(diff_content)

        # Should detect multiple function definitions
        assert metrics.new_functions >= 2  # At least Python and JavaScript functions
        assert metrics.lines_added > 8

    def test_pattern_matching_classes(self):
        """Test class pattern matching."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        diff_content = """--- a/classes.txt
+++ b/classes.txt
@@ -1,3 +1,10 @@
+class PythonClass:
+    pass
+
+interface JavaInterface {
+    void method();
+}
+
+struct CppStruct {
+    int value;
+};
"""

        metrics, confidence = analyzer.analyze_diff(diff_content)

        # Should detect class, interface, and struct definitions
        assert metrics.new_classes >= 2  # At least class and interface/struct

    def test_control_flow_pattern_matching(self):
        """Test control flow pattern matching."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        diff_content = """--- a/control_flow.py
+++ b/control_flow.py
@@ -1,5 +1,15 @@
+if condition:
+    do_something()
+elif other_condition:
+    do_other()
+else:
+    default_action()
+
+for item in items:
+    process(item)
+
+while running:
+    try:
+        work()
+    except Exception:
+        handle_error()
+    finally:
+        cleanup()
"""

        metrics, confidence = analyzer.analyze_diff(diff_content)

        # Should detect multiple control flow statements
        assert (
            metrics.control_flow_changes >= 5
        )  # if, elif, else, for, while, try, except, finally

    def test_import_pattern_matching(self):
        """Test import pattern matching."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        diff_content = """--- a/imports.py
+++ b/imports.py
@@ -1,3 +1,8 @@
+import os
+import sys
+from typing import Dict, List
+from collections import defaultdict
+import numpy as np
"""

        metrics, confidence = analyzer.analyze_diff(diff_content)

        # Should detect multiple import statements
        assert (
            metrics.import_changes >= 4
        )  # import os, sys, from typing, from collections, import numpy

    def test_confidence_calculation_scenarios(self):
        """Test confidence calculation with various scenarios."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Test zero confidence for empty metrics
        empty_metrics = DiffComplexityMetrics()
        confidence_empty = analyzer._calculate_confidence(empty_metrics)
        assert confidence_empty == 0.0

        # Test low confidence for file-only changes
        file_only_metrics = DiffComplexityMetrics(files_changed=2)
        confidence_files = analyzer._calculate_confidence(file_only_metrics)
        assert 0.3 <= confidence_files <= 0.5

        # Test higher confidence for line changes
        line_metrics = DiffComplexityMetrics(
            lines_added=50, lines_removed=20, files_changed=3
        )
        confidence_lines = analyzer._calculate_confidence(line_metrics)
        assert confidence_lines >= 0.5

        # Test maximum confidence with all indicators
        max_metrics = DiffComplexityMetrics(
            lines_added=100,
            lines_removed=50,
            files_changed=5,
            code_files=3,
            critical_file_changes=2,
            new_functions=3,
            new_classes=1,
        )
        confidence_max = analyzer._calculate_confidence(max_metrics)
        assert confidence_max >= 0.8
        assert confidence_max <= 1.0

    def test_get_risk_assessment_conservative(self):
        """Test risk assessment with conservative aggressiveness."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Low complexity should be LOW risk even in conservative mode
        low_metrics = DiffComplexityMetrics(lines_added=5, files_changed=1)
        risk_level, confidence = analyzer.get_risk_assessment(
            low_metrics, "CONSERVATIVE"
        )
        assert risk_level == "LOW"
        assert confidence > 0.0

        # Medium complexity should be MEDIUM or higher in conservative mode
        medium_metrics = DiffComplexityMetrics(
            lines_added=50, lines_removed=20, files_changed=3, new_functions=1
        )
        risk_level, confidence = analyzer.get_risk_assessment(
            medium_metrics, "CONSERVATIVE"
        )
        assert risk_level in ["MEDIUM", "HIGH", "CRITICAL"]

        # High complexity should be HIGH or CRITICAL in conservative mode
        high_metrics = DiffComplexityMetrics(
            lines_added=200,
            lines_removed=100,
            files_changed=8,
            new_functions=5,
            new_classes=2,
            critical_file_changes=1,
        )
        risk_level, confidence = analyzer.get_risk_assessment(
            high_metrics, "CONSERVATIVE"
        )
        assert risk_level in ["HIGH", "CRITICAL"]

    def test_get_risk_assessment_balanced(self):
        """Test risk assessment with balanced aggressiveness."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Test various complexity levels with balanced settings
        test_cases = [
            (DiffComplexityMetrics(lines_added=5, files_changed=1), "LOW"),
            (
                DiffComplexityMetrics(lines_added=50, files_changed=3, new_functions=1),
                ["LOW", "MEDIUM"],
            ),
            (
                DiffComplexityMetrics(
                    lines_added=150, files_changed=5, new_functions=3, new_classes=1
                ),
                ["MEDIUM", "HIGH", "CRITICAL"],  # Allow CRITICAL due to high complexity
            ),
            (
                DiffComplexityMetrics(
                    lines_added=500,
                    files_changed=15,
                    new_functions=10,
                    critical_file_changes=2,
                ),
                ["HIGH", "CRITICAL"],
            ),
        ]

        for metrics, expected_risk in test_cases:
            risk_level, confidence = analyzer.get_risk_assessment(metrics, "BALANCED")
            if isinstance(expected_risk, list):
                assert risk_level in expected_risk
            else:
                assert risk_level == expected_risk
            assert confidence > 0.0

    def test_get_risk_assessment_aggressive(self):
        """Test risk assessment with aggressive aggressiveness."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Even high complexity should often be LOW/MEDIUM in aggressive mode
        high_metrics = DiffComplexityMetrics(
            lines_added=200,
            lines_removed=100,
            files_changed=8,
            new_functions=5,
            new_classes=2,
        )
        risk_level, confidence = analyzer.get_risk_assessment(
            high_metrics, "AGGRESSIVE"
        )
        assert risk_level in [
            "LOW",
            "MEDIUM",
            "HIGH",
            "CRITICAL",
        ]  # Allow CRITICAL for very high complexity

        # Very high complexity should be HIGH or CRITICAL
        very_high_metrics = DiffComplexityMetrics(
            lines_added=800,
            lines_removed=400,
            files_changed=20,
            new_functions=15,
            new_classes=8,
            critical_file_changes=5,
        )
        risk_level, confidence = analyzer.get_risk_assessment(
            very_high_metrics, "AGGRESSIVE"
        )
        assert risk_level in ["HIGH", "CRITICAL"]

    def test_get_risk_assessment_very_aggressive(self):
        """Test risk assessment with very aggressive aggressiveness."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Most changes should be LOW in very aggressive mode
        moderate_metrics = DiffComplexityMetrics(
            lines_added=100, lines_removed=50, files_changed=5, new_functions=3
        )
        risk_level, confidence = analyzer.get_risk_assessment(
            moderate_metrics, "VERY_AGGRESSIVE"
        )
        assert risk_level in ["LOW", "MEDIUM"]

        # Only extreme complexity should be HIGH/CRITICAL
        extreme_metrics = DiffComplexityMetrics(
            lines_added=1000,
            lines_removed=500,
            files_changed=30,
            new_functions=20,
            new_classes=10,
            critical_file_changes=8,
        )
        risk_level, confidence = analyzer.get_risk_assessment(
            extreme_metrics, "VERY_AGGRESSIVE"
        )
        # Even extreme changes might be MEDIUM in very aggressive mode
        assert risk_level in ["MEDIUM", "HIGH", "CRITICAL"]

    def test_confidence_boost_for_critical_indicators(self):
        """Test confidence boost for critical indicators."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Base metrics
        base_metrics = DiffComplexityMetrics(lines_added=50, files_changed=2)
        base_risk, base_confidence = analyzer.get_risk_assessment(base_metrics)

        # Metrics with critical file changes
        critical_metrics = DiffComplexityMetrics(
            lines_added=50, files_changed=2, critical_file_changes=1
        )
        critical_risk, critical_confidence = analyzer.get_risk_assessment(
            critical_metrics
        )

        # Metrics with large file changes
        large_metrics = DiffComplexityMetrics(
            lines_added=50, files_changed=2, large_file_changes=1
        )
        large_risk, large_confidence = analyzer.get_risk_assessment(large_metrics)

        # Critical and large file changes should boost confidence
        assert critical_confidence > base_confidence
        assert large_confidence > base_confidence

    def test_error_handling_in_analyze_diff(self):
        """Test error handling in analyze_diff method."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Mock logger to capture error messages
        with patch.object(analyzer, "logger") as mock_logger:
            # Simulate an exception in _analyze_diff_lines
            with patch.object(
                analyzer, "_analyze_diff_lines", side_effect=Exception("Test error")
            ):
                metrics, confidence = analyzer.analyze_diff("test diff content")

                # Should return empty metrics and zero confidence on error
                assert isinstance(metrics, DiffComplexityMetrics)
                assert metrics.total_lines_changed() == 0
                assert confidence == 0.0

                # Should log the error
                mock_logger.error.assert_called_once()

    def test_edge_cases_file_paths(self):
        """Test edge cases in file path analysis."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Test files without extensions
        no_ext_files = ["Makefile", "Dockerfile", "README"]
        metrics, confidence = analyzer.analyze_diff("", no_ext_files)
        assert metrics.files_changed == 3

        # Test files with multiple dots
        multi_dot_files = ["test.spec.js", "config.dev.json", "app.min.css"]
        metrics, confidence = analyzer.analyze_diff("", multi_dot_files)
        assert metrics.files_changed == 3
        assert metrics.test_files >= 1  # test.spec.js
        assert metrics.config_files >= 1  # config.dev.json

        # Test empty file paths
        empty_paths = ["", "   ", "/"]
        metrics, confidence = analyzer.analyze_diff("", empty_paths)
        assert metrics.files_changed == 3

    def test_diff_header_parsing(self):
        """Test proper parsing of diff headers."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Test diff with various header formats
        diff_content = """diff --git a/file1.py b/file1.py
index 1234567..abcdefg 100644
--- a/file1.py
+++ b/file1.py
@@ -1,3 +1,4 @@
 def existing_function():
     pass
+
+def new_function():
+    return True
---
diff --git a/file2.js b/file2.js
new file mode 100644
index 0000000..1234567
--- /dev/null
+++ b/file2.js
@@ -0,0 +1,5 @@
+function newJsFunction() {
+    console.log("Hello");
+    return 42;
+}
"""

        metrics, confidence = analyzer.analyze_diff(diff_content)

        # Should properly count files and ignore headers
        assert metrics.files_changed == 2  # file1.py and file2.js
        assert metrics.lines_added >= 6  # New lines in both files
        assert metrics.new_functions >= 2  # new_function and newJsFunction

    def test_character_counting(self):
        """Test character counting in diff analysis."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        diff_content = """--- a/test.py
+++ b/test.py
@@ -1,2 +1,3 @@
-short
+much longer line with more characters
+another new line
"""

        metrics, confidence = analyzer.analyze_diff(diff_content)

        assert metrics.lines_added == 2
        assert metrics.lines_removed == 1
        assert metrics.characters_added > metrics.characters_removed
        assert metrics.characters_removed == len("short")  # Length of removed line
        # Added characters should be sum of the two added lines
        expected_added = len("much longer line with more characters") + len(
            "another new line"
        )
        assert metrics.characters_added == expected_added

    def test_large_file_detection_edge_cases(self):
        """Test edge cases in large file change detection."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Test exactly 100 lines (should not be considered large)
        diff_lines = ["--- a/file.py", "+++ b/file.py", "@@ -1,50 +1,150 @@"]
        for i in range(100):
            diff_lines.append(f"+line {i}")

        diff_content = "\n".join(diff_lines)
        metrics, confidence = analyzer.analyze_diff(diff_content)

        assert metrics.lines_added == 100
        assert metrics.large_file_changes == 0  # Exactly 100 should not be large

        # Test 101 lines (should be considered large)
        diff_lines.append("+one more line")
        diff_content = "\n".join(diff_lines)
        metrics, confidence = analyzer.analyze_diff(diff_content)

        assert metrics.lines_added == 101
        assert metrics.large_file_changes == 1  # 101 should be large

    def test_multiple_files_large_changes(self):
        """Test detection of large changes across multiple files."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Create diff with multiple files, some with large changes
        diff_content = """--- a/small_file.py
+++ b/small_file.py
@@ -1,2 +1,5 @@
+small change 1
+small change 2
+small change 3
--- a/large_file1.py
+++ b/large_file1.py
@@ -1,50 +1,200 @@
"""

        # Add 120 lines to large_file1.py
        for i in range(120):
            diff_content += f"+large file 1 line {i}\n"

        diff_content += """--- a/large_file2.py
+++ b/large_file2.py
@@ -1,30 +1,150 @@
"""

        # Add 110 lines to large_file2.py
        for i in range(110):
            diff_content += f"+large file 2 line {i}\n"

        metrics, confidence = analyzer.analyze_diff(diff_content)

        assert metrics.files_changed == 3
        # The algorithm counts large files when switching to next file, so all 3 files might be counted
        assert (
            metrics.large_file_changes >= 2
        )  # At least large_file1.py and large_file2.py
        assert metrics.lines_added == 3 + 120 + 110  # small + large1 + large2


@pytest.mark.integration
@pytest.mark.analyzer
class TestDiffComplexityAnalyzerIntegration:
    """Integration tests for DiffComplexityAnalyzer with realistic scenarios."""

    def test_real_world_python_commit(self):
        """Test analysis of a realistic Python commit."""
        if DiffComplexityAnalyzer is None or DiffComplexityMetrics is None:
            pytest.skip("Required classes not available")

        analyzer = DiffComplexityAnalyzer()

        # Simulate a real Python commit with multiple changes
        diff_content = """--- a/src/api/handlers.py
+++ b/src/api/handlers.py
@@ -1,15 +1,35 @@
 import json
+import logging
+from typing import Dict, Any, Optional
 from flask import Flask, request, jsonify
+from .validators import validate_input
+from .exceptions import ValidationError

 app = Flask(__name__)
+logger = logging.getLogger(__name__)

+class RequestHandler:
+    def __init__(self, config: Dict[str, Any]):
+        self.config = config
+        self.logger = logging.getLogger(self.__class__.__name__)
+
+    def validate_request(self, data: Dict[str, Any]) -> bool:
+        try:
+            return validate_input(data, self.config.get('validation_rules'))
+        except ValidationError as e:
+            self.logger.error(f"Validation failed: {e}")
+            return False
+
 @app.route('/api/data', methods=['POST'])
 def handle_data():
+    handler = RequestHandler(app.config)
     data = request.get_json()
-    if not data:
+
+    if not data or not handler.validate_request(data):
         return jsonify({'error': 'Invalid data'}), 400
+
+    try:
+        result = process_data(data)
+        logger.info(f"Successfully processed data for user {data.get('user_id')}")
+        return jsonify(result)
+    except Exception as e:
+        logger.error(f"Processing failed: {e}")
+        return jsonify({'error': 'Processing failed'}), 500
-    return jsonify({'status': 'success'})
--- a/src/api/validators.py
+++ b/src/api/validators.py
@@ -0,0 +1,25 @@
+from typing import Dict, Any, List
+from .exceptions import ValidationError
+
+def validate_input(data: Dict[str, Any], rules: Dict[str, Any]) -> bool:
+    if not isinstance(data, dict):
+        raise ValidationError("Data must be a dictionary")
+
+    required_fields = rules.get('required', [])
+    for field in required_fields:
+        if field not in data:
+            raise ValidationError(f"Missing required field: {field}")
+
+    for field, value in data.items():
+        field_rules = rules.get('fields', {}).get(field, {})
+        if 'type' in field_rules:
+            expected_type = field_rules['type']
+            if not isinstance(value, expected_type):
+                raise ValidationError(f"Field {field} must be of type {expected_type.__name__}")
+
+        if 'max_length' in field_rules and isinstance(value, str):
+            if len(value) > field_rules['max_length']:
+                raise ValidationError(f"Field {field} exceeds maximum length")
+
+    return True
"""

        file_paths = ["src/api/handlers.py", "src/api/validators.py"]

        metrics, confidence = analyzer.analyze_diff(diff_content, file_paths)

        # Verify realistic analysis results
        assert metrics.files_changed == 2
        assert metrics.code_files == 2
        assert metrics.lines_added > 30
        assert metrics.new_classes >= 1  # RequestHandler
        assert metrics.new_functions >= 2  # validate_request, validate_input
        assert metrics.import_changes >= 3  # Multiple import statements
        assert metrics.control_flow_changes >= 4  # if, try, for statements
        assert confidence > 0.7  # High confidence for substantial changes

        # Test risk assessment
        risk_level, risk_confidence = analyzer.get_risk_assessment(metrics, "BALANCED")
        assert risk_level in [
            "MEDIUM",
            "HIGH",
        ]  # Substantial changes should be medium-high risk
        assert risk_confidence > 0.7


if __name__ == "__main__":
    # Allow running this test file directly
    pytest.main([__file__, "-v"])
