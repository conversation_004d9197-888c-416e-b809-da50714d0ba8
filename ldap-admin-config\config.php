<?php
/**
 * Minimal phpLDAPadmin configuration for RepoSense AI
 */

$servers = new Datastore();

$servers->newServer('ldap_pla');
$servers->setValue('server','name','RepoSense AI LDAP');
$servers->setValue('server','host','openldap');
$servers->setValue('server','port',1389);
$servers->setValue('server','base',array('dc=reposense,dc=local'));
$servers->setValue('server','tls',false);
$servers->setValue('login','auth_type','cookie');
$servers->setValue('login','bind_id','cn=admin,dc=reposense,dc=local');

$config->custom->session['blowfish'] = 'reposense-ai-secret-key';

?>
