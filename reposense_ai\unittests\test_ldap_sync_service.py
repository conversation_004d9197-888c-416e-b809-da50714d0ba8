"""
Unit tests for the LDAP Sync Service.

This module tests LDAP authentication, user synchronization, and connection functionality.
Tests are designed to work with or without an actual LDAP server using mocking.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional
from unittest.mock import MagicMock, Mock, patch

import pytest

# Import test utilities
from .test_utils import mock_config, skip_if_no_network

# Import the modules under test
try:
    from ldap_sync_service import LDAP_AVAILABLE, LDAPSyncService
    from models import Config, User, UserRole
    from user_database import UserDatabase

    if LDAP_AVAILABLE:
        from ldap3 import ALL, SUBTREE, Connection, Server
except ImportError:
    LDAPSyncService = None
    LDAP_AVAILABLE = False
    Config = None
    User = None
    UserRole = None
    UserDatabase = None


@pytest.fixture
def mock_ldap_config():
    """Create a mock configuration with LDAP settings."""
    config = Mock(spec=Config)
    config.ldap_sync_enabled = True
    config.ldap_server = "openldap"
    config.ldap_port = 1389
    config.ldap_use_ssl = False
    config.ldap_bind_dn = "cn=admin,dc=reposense,dc=local"
    config.ldap_bind_password = "adminpassword"
    config.ldap_user_base_dn = "ou=users,dc=reposense,dc=local"
    config.ldap_user_filter = "(objectClass=inetOrgPerson)"
    config.ldap_username_attr = "cn"
    config.ldap_email_attr = "mail"
    config.ldap_fullname_attr = "cn"
    config.ldap_phone_attr = "telephoneNumber"
    config.ldap_groups_attr = "memberOf"
    config.ldap_group_role_mapping = {}  # Empty group role mapping
    config.ldap_default_role = "VIEWER"  # Default role for LDAP users
    return config


@pytest.fixture
def mock_user_db():
    """Create a mock user database."""
    user_db = Mock(spec=UserDatabase)
    user_db.get_user_by_username.return_value = None
    user_db.create_user.return_value = True
    user_db.update_user.return_value = True
    return user_db


@pytest.fixture
def mock_ldap_entries():
    """Create mock LDAP entries for testing."""
    # Create mock entries that match the structure expected by the LDAP service
    entry1 = Mock()
    entry1.entry_dn = "cn=Fred Van Eijk,ou=users,dc=reposense,dc=local"

    # Mock the attribute access pattern used by ldap3
    cn_attr = Mock()
    cn_attr.values = ["Fred Van Eijk"]
    entry1.cn = cn_attr

    mail_attr = Mock()
    mail_attr.values = ["<EMAIL>"]
    entry1.mail = mail_attr

    phone_attr = Mock()
    phone_attr.values = ["************"]
    entry1.telephoneNumber = phone_attr

    groups_attr = Mock()
    groups_attr.values = []
    entry1.memberOf = groups_attr

    entry2 = Mock()
    entry2.entry_dn = "cn=Feddo Van Eijk,ou=users,dc=reposense,dc=local"

    cn_attr2 = Mock()
    cn_attr2.values = ["Feddo Van Eijk"]
    entry2.cn = cn_attr2

    mail_attr2 = Mock()
    mail_attr2.values = ["<EMAIL>"]
    entry2.mail = mail_attr2

    phone_attr2 = Mock()
    phone_attr2.values = ["************"]
    entry2.telephoneNumber = phone_attr2

    groups_attr2 = Mock()
    groups_attr2.values = []
    entry2.memberOf = groups_attr2

    return [entry1, entry2]


@pytest.mark.unit
class TestLDAPSyncService:
    """Test cases for LDAP Sync Service."""

    def test_ldap_service_import(self):
        """Test that LDAP service can be imported."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        assert LDAPSyncService is not None, "LDAPSyncService should be importable"
        assert Config is not None, "Config should be importable"
        assert User is not None, "User should be importable"
        assert UserDatabase is not None, "UserDatabase should be importable"

    def test_ldap_service_initialization_success(self, mock_ldap_config, mock_user_db):
        """Test successful LDAP service initialization."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        service = LDAPSyncService(mock_ldap_config, mock_user_db)
        assert service is not None
        assert service.config == mock_ldap_config
        assert service.user_db == mock_user_db
        assert hasattr(service, "logger")

    def test_ldap_service_initialization_disabled(self, mock_user_db):
        """Test LDAP service initialization when disabled."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        config = Mock(spec=Config)
        config.ldap_sync_enabled = False

        service = LDAPSyncService(config, mock_user_db)
        assert service is not None
        assert service.config == config

    def test_ldap_service_initialization_no_library(
        self, mock_ldap_config, mock_user_db
    ):
        """Test LDAP service initialization without ldap3 library."""
        with patch("ldap_sync_service.LDAP_AVAILABLE", False):
            with pytest.raises(ImportError, match="ldap3 library required"):
                LDAPSyncService(mock_ldap_config, mock_user_db)

    def test_validate_config_success(self, mock_ldap_config, mock_user_db):
        """Test successful configuration validation."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        # Should not raise any exception
        service = LDAPSyncService(mock_ldap_config, mock_user_db)
        assert service is not None

    def test_validate_config_missing_fields(self, mock_user_db):
        """Test configuration validation with missing required fields."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        config = Mock(spec=Config)
        config.ldap_sync_enabled = True
        config.ldap_server = None  # Missing required field
        config.ldap_bind_dn = "cn=admin,dc=reposense,dc=local"
        config.ldap_bind_password = "adminpassword"
        config.ldap_user_base_dn = "ou=users,dc=reposense,dc=local"

        with pytest.raises(ValueError, match="Missing required LDAP configuration"):
            LDAPSyncService(config, mock_user_db)

    @patch("ldap_sync_service.Connection")
    @patch("ldap_sync_service.Server")
    def test_connection_test_success(
        self, mock_server, mock_connection, mock_ldap_config, mock_user_db
    ):
        """Test successful LDAP connection test."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        # Setup mocks
        mock_conn_instance = Mock()
        mock_conn_instance.bind.return_value = True
        mock_connection.return_value = mock_conn_instance

        service = LDAPSyncService(mock_ldap_config, mock_user_db)
        success, message = service.test_connection()

        assert success is True
        assert "LDAP connection successful" in message
        mock_conn_instance.search.assert_called_once()
        mock_conn_instance.unbind.assert_called_once()

    @patch("ldap_sync_service.Connection")
    @patch("ldap_sync_service.Server")
    def test_connection_test_failure(
        self, mock_server, mock_connection, mock_ldap_config, mock_user_db
    ):
        """Test LDAP connection test failure."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        # Setup mocks to simulate connection failure
        mock_connection.side_effect = Exception("Connection failed")

        service = LDAPSyncService(mock_ldap_config, mock_user_db)
        success, message = service.test_connection()

        assert success is False
        assert "Connection failed" in message

    @patch("ldap_sync_service.Connection")
    @patch("ldap_sync_service.Server")
    def test_connection_test_with_form_data(
        self, mock_server, mock_connection, mock_ldap_config, mock_user_db
    ):
        """Test LDAP connection test with form data override."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        # Setup mocks
        mock_conn_instance = Mock()
        mock_conn_instance.bind.return_value = True
        mock_connection.return_value = mock_conn_instance

        service = LDAPSyncService(mock_ldap_config, mock_user_db)

        form_data = {
            "ldap_server": "test-server",
            "ldap_port": "389",
            "ldap_bind_dn": "cn=test,dc=test,dc=com",
        }

        success, message = service.test_connection(form_data)

        assert success is True
        assert "test-server" in message

    @patch("ldap_sync_service.Connection")
    @patch("ldap_sync_service.Server")
    def test_sync_users_success(
        self,
        mock_server,
        mock_connection,
        mock_ldap_config,
        mock_user_db,
        mock_ldap_entries,
    ):
        """Test successful user synchronization."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        # Setup mocks
        mock_conn_instance = Mock()
        mock_conn_instance.entries = mock_ldap_entries
        mock_connection.return_value = mock_conn_instance

        service = LDAPSyncService(mock_ldap_config, mock_user_db)
        result = service.sync_users_from_ldap()

        assert result["created"] == 2
        assert result["updated"] == 0
        assert result["errors"] == 0

        # Verify user creation was called
        assert mock_user_db.create_user.call_count == 2

    @patch("ldap_sync_service.Connection")
    @patch("ldap_sync_service.Server")
    def test_sync_users_update_existing(
        self,
        mock_server,
        mock_connection,
        mock_ldap_config,
        mock_user_db,
        mock_ldap_entries,
    ):
        """Test user synchronization with existing users."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        # Setup mocks - simulate existing user
        existing_user = Mock()
        existing_user.username = "Fred Van Eijk"
        mock_user_db.get_user_by_username.return_value = existing_user

        mock_conn_instance = Mock()
        mock_conn_instance.entries = mock_ldap_entries
        mock_connection.return_value = mock_conn_instance

        service = LDAPSyncService(mock_ldap_config, mock_user_db)
        result = service.sync_users_from_ldap()

        assert result["updated"] >= 1  # At least one user should be updated

    @patch("ldap_sync_service.Connection")
    @patch("ldap_sync_service.Server")
    def test_sync_users_connection_failure(
        self, mock_server, mock_connection, mock_ldap_config, mock_user_db
    ):
        """Test user synchronization with connection failure."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        # Setup mocks to simulate connection failure
        mock_connection.side_effect = Exception("LDAP server unavailable")

        service = LDAPSyncService(mock_ldap_config, mock_user_db)
        result = service.sync_users_from_ldap()

        assert result["errors"] == 1

    def test_ldap_sync_service_methods_exist(self, mock_ldap_config, mock_user_db):
        """Test that LDAP sync service has expected methods."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        service = LDAPSyncService(mock_ldap_config, mock_user_db)

        # Test that main methods exist
        assert hasattr(service, "sync_users_from_ldap")
        assert hasattr(service, "test_connection")
        assert hasattr(service, "get_sync_status")
        assert callable(service.sync_users_from_ldap)
        assert callable(service.test_connection)
        assert callable(service.get_sync_status)


@pytest.mark.integration
class TestLDAPIntegration:
    """Integration tests for LDAP functionality."""

    @skip_if_no_network
    @pytest.mark.skipif(not LDAP_AVAILABLE, reason="LDAP library not available")
    def test_real_ldap_connection(self):
        """Test connection to actual LDAP server if available."""
        # This test requires a running LDAP server
        # Skip if not in integration test environment
        pytest.skip("Integration test - requires running LDAP server")

    @skip_if_no_network
    @pytest.mark.skipif(not LDAP_AVAILABLE, reason="LDAP library not available")
    def test_real_ldap_user_sync(self):
        """Test user synchronization with actual LDAP server."""
        # This test requires a running LDAP server with test users
        # Skip if not in integration test environment
        pytest.skip("Integration test - requires running LDAP server with test data")


@pytest.mark.network
class TestLDAPNetworkOperations:
    """Network-dependent LDAP tests."""

    @skip_if_no_network
    @pytest.mark.skipif(not LDAP_AVAILABLE, reason="LDAP library not available")
    def test_ldap_server_connectivity(self):
        """Test basic LDAP server connectivity."""
        # This would test actual network connectivity to LDAP server
        pytest.skip("Network test - requires LDAP server configuration")


# Migrated test functionality from scripts/test_ldap.py
class LDAPTestUtilities:
    """Utility class for LDAP testing - migrated from scripts/test_ldap.py"""

    def __init__(self, host: str = "localhost", port: int = 1389):
        self.host = host
        self.port = port
        self.base_dn = "dc=reposense,dc=local"
        self.admin_dn = "cn=admin,dc=reposense,dc=local"
        self.admin_password = "adminpassword"

    def create_test_server_config(self):
        """Create server configuration for testing."""
        if not LDAP_AVAILABLE:
            return None
        return Server(f"ldap://{self.host}:{self.port}", get_info=ALL)

    def test_basic_connection(self):
        """Test basic LDAP server connectivity - migrated functionality."""
        if not LDAP_AVAILABLE:
            return False, "LDAP library not available"

        try:
            server = self.create_test_server_config()
            conn = Connection(server)
            if conn.bind():
                conn.unbind()
                return True, "Connection successful"
            else:
                return False, "Connection failed"
        except Exception as e:
            return False, f"Connection error: {e}"

    def test_admin_authentication(self):
        """Test admin authentication - migrated functionality."""
        if not LDAP_AVAILABLE:
            return False, "LDAP library not available"

        try:
            server = self.create_test_server_config()
            conn = Connection(server, self.admin_dn, self.admin_password)
            if conn.bind():
                conn.unbind()
                return True, "Admin authentication successful"
            else:
                return False, "Admin authentication failed"
        except Exception as e:
            return False, f"Admin authentication error: {e}"

    def test_user_search(self):
        """Test user search functionality - migrated functionality."""
        if not LDAP_AVAILABLE:
            return False, "LDAP library not available", []

        try:
            server = self.create_test_server_config()
            conn = Connection(server, self.admin_dn, self.admin_password)
            if not conn.bind():
                return False, "Failed to bind as admin", []

            search_base = f"ou=users,{self.base_dn}"
            search_filter = "(objectClass=inetOrgPerson)"

            conn.search(
                search_base,
                search_filter,
                SUBTREE,
                attributes=["cn", "sn", "givenName", "uid", "mail"],
            )

            users = []
            if conn.entries:
                for entry in conn.entries:
                    user_info = {
                        "dn": str(entry.entry_dn),
                        "cn": str(entry.cn)
                        if hasattr(entry, "cn") and entry.cn
                        else "",
                        "uid": str(entry.uid)
                        if hasattr(entry, "uid") and entry.uid
                        else "",
                        "mail": str(entry.mail)
                        if hasattr(entry, "mail") and entry.mail
                        else "",
                    }
                    users.append(user_info)

            conn.unbind()
            return True, f"Found {len(users)} users", users

        except Exception as e:
            return False, f"User search error: {e}", []


@pytest.mark.unit
class TestLDAPUtilities:
    """Test cases for LDAP utility functions."""

    def test_ldap_utilities_initialization(self):
        """Test LDAP utilities initialization."""
        utils = LDAPTestUtilities()
        assert utils.host == "localhost"
        assert utils.port == 1389
        assert utils.base_dn == "dc=reposense,dc=local"
        assert utils.admin_dn == "cn=admin,dc=reposense,dc=local"
        assert utils.admin_password == "adminpassword"

    def test_ldap_utilities_custom_config(self):
        """Test LDAP utilities with custom configuration."""
        utils = LDAPTestUtilities(host="openldap", port=389)
        assert utils.host == "openldap"
        assert utils.port == 389

    @pytest.mark.skipif(not LDAP_AVAILABLE, reason="LDAP library not available")
    def test_create_test_server_config(self):
        """Test server configuration creation."""
        utils = LDAPTestUtilities()
        server = utils.create_test_server_config()
        assert server is not None

    def test_create_test_server_config_no_ldap(self):
        """Test server configuration creation without LDAP library."""
        # Test that when LDAP is not available, server config returns None
        utils = LDAPTestUtilities()
        # This test verifies the logic without actually patching LDAP_AVAILABLE
        # since patching module-level variables in tests can be complex
        if not LDAP_AVAILABLE:
            server = utils.create_test_server_config()
            assert server is None
        else:
            # If LDAP is available, just verify the method exists
            assert hasattr(utils, "create_test_server_config")


@pytest.mark.unit
class TestLDAPConfigurationValidation:
    """Test cases for LDAP configuration validation."""

    def test_required_fields_validation(self):
        """Test validation of required LDAP configuration fields."""
        required_fields = [
            "ldap_server",
            "ldap_bind_dn",
            "ldap_bind_password",
            "ldap_user_base_dn",
        ]

        for field in required_fields:
            config = Mock(spec=Config)
            config.ldap_sync_enabled = True

            # Set all fields to valid values
            config.ldap_server = "openldap"
            config.ldap_bind_dn = "cn=admin,dc=reposense,dc=local"
            config.ldap_bind_password = "adminpassword"
            config.ldap_user_base_dn = "ou=users,dc=reposense,dc=local"

            # Set the current field to None to test validation
            setattr(config, field, None)

            if LDAP_AVAILABLE:
                with pytest.raises(
                    ValueError, match="Missing required LDAP configuration"
                ):
                    LDAPSyncService(config, Mock())

    def test_optional_fields_defaults(self, mock_user_db):
        """Test that optional LDAP fields have reasonable defaults."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        config = Mock(spec=Config)
        config.ldap_sync_enabled = True
        config.ldap_server = "openldap"
        config.ldap_port = 1389
        config.ldap_use_ssl = False
        config.ldap_bind_dn = "cn=admin,dc=reposense,dc=local"
        config.ldap_bind_password = "adminpassword"
        config.ldap_user_base_dn = "ou=users,dc=reposense,dc=local"
        config.ldap_user_filter = "(objectClass=inetOrgPerson)"
        config.ldap_username_attr = "cn"
        config.ldap_email_attr = "mail"
        config.ldap_fullname_attr = "cn"
        config.ldap_phone_attr = "telephoneNumber"
        config.ldap_groups_attr = "memberOf"

        service = LDAPSyncService(config, mock_user_db)
        assert service is not None

    def test_ssl_configuration(self, mock_user_db):
        """Test SSL configuration options."""
        if not LDAP_AVAILABLE:
            pytest.skip("LDAP library not available")

        config = Mock(spec=Config)
        config.ldap_sync_enabled = True
        config.ldap_server = "openldap"
        config.ldap_port = 636  # SSL port
        config.ldap_use_ssl = True
        config.ldap_bind_dn = "cn=admin,dc=reposense,dc=local"
        config.ldap_bind_password = "adminpassword"
        config.ldap_user_base_dn = "ou=users,dc=reposense,dc=local"
        config.ldap_user_filter = "(objectClass=inetOrgPerson)"
        config.ldap_username_attr = "cn"
        config.ldap_email_attr = "mail"
        config.ldap_fullname_attr = "cn"
        config.ldap_phone_attr = "telephoneNumber"
        config.ldap_groups_attr = "memberOf"

        service = LDAPSyncService(config, mock_user_db)
        assert service is not None
