#!/usr/bin/env python3
"""
Test LDAP connection using RepoSense AI configuration
This script mimics the LDAP connection test that RepoSense AI performs
"""

import sys
import os
import json
from pathlib import Path

# Add the reposense_ai directory to the Python path
sys.path.insert(0, str(Path(__file__).parent.parent / "reposense_ai"))

try:
    from ldap3 import Server, Connection, ALL, SUBTREE
    from ldap3.core.exceptions import LDAPException
    LDAP_AVAILABLE = True
except ImportError:
    print("❌ LDAP library (ldap3) not available. Install with: pip install ldap3")
    LDAP_AVAILABLE = False
    sys.exit(1)

def load_config():
    """Load RepoSense AI configuration"""
    config_path = Path(__file__).parent.parent / "reposense_ai" / "data" / "config.json"
    
    if not config_path.exists():
        print(f"❌ Configuration file not found: {config_path}")
        return None
        
    try:
        with open(config_path, 'r') as f:
            config = json.load(f)
        return config
    except Exception as e:
        print(f"❌ Error loading configuration: {e}")
        return None

def test_ldap_connection(config):
    """Test LDAP connection using RepoSense AI configuration"""
    print("🔍 Testing LDAP Connection from RepoSense AI Configuration")
    print("=" * 60)
    
    # Extract LDAP configuration
    ldap_config = {
        'enabled': config.get('ldap_sync_enabled', False),
        'server': config.get('ldap_server', ''),
        'port': config.get('ldap_port', 389),
        'use_ssl': config.get('ldap_use_ssl', False),
        'bind_dn': config.get('ldap_bind_dn', ''),
        'bind_password': config.get('ldap_bind_password', ''),
        'user_base_dn': config.get('ldap_user_base_dn', ''),
        'user_filter': config.get('ldap_user_filter', '(objectClass=person)'),
        'username_attr': config.get('ldap_username_attr', 'sAMAccountName'),
        'email_attr': config.get('ldap_email_attr', 'mail'),
        'fullname_attr': config.get('ldap_fullname_attr', 'displayName'),
    }
    
    # Display configuration
    print("📋 LDAP Configuration:")
    for key, value in ldap_config.items():
        if 'password' in key.lower():
            display_value = '*' * len(str(value)) if value else '(empty)'
        else:
            display_value = value
        print(f"   {key}: {display_value}")
    print()
    
    # Check if LDAP is enabled
    if not ldap_config['enabled']:
        print("⚠️  LDAP sync is disabled in configuration")
        return False
    
    # Validate required fields
    required_fields = ['server', 'bind_dn', 'bind_password', 'user_base_dn']
    missing_fields = [field for field in required_fields if not ldap_config[field]]
    
    if missing_fields:
        print(f"❌ Missing required LDAP configuration fields: {', '.join(missing_fields)}")
        return False
    
    # Test connection
    try:
        print("🔗 Testing LDAP server connection...")
        
        # Build server URI
        protocol = "ldaps" if ldap_config['use_ssl'] else "ldap"
        server_uri = f"{protocol}://{ldap_config['server']}:{ldap_config['port']}"
        print(f"   Server URI: {server_uri}")
        
        # Create server object
        server = Server(
            ldap_config['server'],
            port=ldap_config['port'],
            use_ssl=ldap_config['use_ssl'],
            get_info=ALL
        )
        print("✅ Server object created successfully")
        
        # Test connection and authentication
        print("🔐 Testing authentication...")
        conn = Connection(
            server,
            user=ldap_config['bind_dn'],
            password=ldap_config['bind_password'],
            auto_bind=True
        )
        print("✅ Authentication successful")
        
        # Test user search
        print("👥 Testing user search...")
        search_result = conn.search(
            search_base=ldap_config['user_base_dn'],
            search_filter=ldap_config['user_filter'],
            search_scope=SUBTREE,
            attributes=[
                ldap_config['username_attr'],
                ldap_config['email_attr'],
                ldap_config['fullname_attr']
            ],
            size_limit=10
        )
        
        if search_result:
            print(f"✅ User search successful - Found {len(conn.entries)} users")
            
            # Display found users
            if conn.entries:
                print("\n📋 Found Users:")
                for entry in conn.entries[:5]:  # Show first 5 users
                    dn = str(entry.entry_dn)
                    print(f"   👤 {dn}")
                    
                    # Show attributes if available
                    for attr in [ldap_config['username_attr'], ldap_config['email_attr'], ldap_config['fullname_attr']]:
                        if hasattr(entry, attr) and getattr(entry, attr).value:
                            print(f"      {attr}: {getattr(entry, attr).value}")
                
                if len(conn.entries) > 5:
                    print(f"   ... and {len(conn.entries) - 5} more users")
            else:
                print("⚠️  No users found matching the filter")
        else:
            print("❌ User search failed")
            conn.unbind()
            return False
        
        # Show server info
        if server.info:
            print(f"\n🖥️  Server Information:")
            print(f"   Vendor: {server.info.vendor_name or 'Unknown'}")
            print(f"   Version: {server.info.vendor_version or 'Unknown'}")
            print(f"   Naming Contexts: {server.info.naming_contexts}")
        
        conn.unbind()
        print("\n🎉 LDAP connection test completed successfully!")
        return True
        
    except LDAPException as e:
        print(f"❌ LDAP connection failed: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def main():
    print("🧪 RepoSense AI LDAP Connection Test")
    print("=" * 40)
    
    # Load configuration
    config = load_config()
    if not config:
        sys.exit(1)
    
    # Test LDAP connection
    success = test_ldap_connection(config)
    
    print("\n" + "=" * 60)
    if success:
        print("✅ LDAP connection test PASSED!")
        print("   Your RepoSense AI LDAP configuration is working correctly.")
        print("   You can now use the 'Test LDAP Connection' feature in the web interface.")
    else:
        print("❌ LDAP connection test FAILED!")
        print("   Please check your LDAP configuration and server connectivity.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())
