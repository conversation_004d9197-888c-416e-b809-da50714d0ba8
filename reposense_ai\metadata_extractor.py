#!/usr/bin/env python3
"""
Metadata Extractor Service
Centralized service for extracting metadata from documentation content.
Consolidates logic previously duplicated across HistoricalScanner, DocumentProcessor, and DocumentService.
"""

import json
import logging
import re
from datetime import datetime
from typing import TYPE_CHECKING, Any, Dict, List, Optional, Tuple, Union

from diff_complexity_analyzer import DiffComplexityAnalyzer

if TYPE_CHECKING:
    from document_database import DocumentRecord
    from models import RepositoryConfig


class MetadataExtractor:
    """Centralized metadata extraction service with hybrid heuristic + LLM approach"""

    def __init__(self, ollama_client=None, config_manager=None):
        """
        Initialize metadata extractor

        Args:
            ollama_client: Optional Ollama client for LLM fallback
            config_manager: Optional config manager for enhanced prompts
        """
        self.logger = logging.getLogger(__name__)
        self.ollama_client = ollama_client
        self.config_manager = config_manager
        self.diff_analyzer = DiffComplexityAnalyzer()

    def extract_all_metadata(
        self,
        documentation: str,
        document_record: Optional["DocumentRecord"] = None,
        override_model: Optional[str] = None,
        override_aggressiveness: Optional[str] = None,
        change_requests: Optional[List] = None,
    ) -> Dict[str, Any]:
        """Extract all metadata using heuristics to prime LLM for better decisions

        Args:
            documentation: The documentation content to analyze
            document_record: Optional document record for enhanced context
            override_model: Optional AI model to use instead of configured model
            override_aggressiveness: Optional risk aggressiveness level to use instead of configured level
        """
        metadata: Dict[str, Any] = {}

        try:
            # Always run heuristic analysis first to gather context indicators
            self.logger.debug(
                "Running heuristic analysis to gather context indicators..."
            )
            heuristic_context = self._gather_heuristic_context(
                documentation, document_record, override_aggressiveness, change_requests
            )

            # Use LLM with heuristic context when available (most accurate)
            if self.ollama_client and document_record:
                if override_model:
                    self.logger.info(
                        f"🎯 Using LLM with heuristic context for document {document_record.id} with override model: {override_model}"
                    )
                else:
                    self.logger.info(
                        f"🎯 Using LLM with heuristic context for document {document_record.id}"
                    )
                llm_metadata, model_used = self._extract_metadata_with_llm_enhanced(
                    documentation,
                    document_record,
                    heuristic_context,
                    override_model,
                    override_aggressiveness,
                )

                # Store the model used for this analysis
                if model_used and document_record:
                    document_record.ai_model_used = model_used
                    self.logger.info(
                        f"🎯 Stored AI model used in document record: {model_used}"
                    )

                # Use LLM results as primary source if successful, but prefer heuristic risk assessment
                if llm_metadata:
                    for field, value in llm_metadata.items():
                        if value is not None:
                            # For risk_level, prefer heuristic assessment over LLM (more conservative)
                            if (
                                field == "risk_level"
                                and heuristic_context
                                and "decisions" in heuristic_context
                            ):
                                heuristic_risk = heuristic_context["decisions"].get(
                                    "risk_level"
                                )
                                if heuristic_risk:
                                    metadata[field] = heuristic_risk
                                    self.logger.debug(
                                        f"Using heuristic risk assessment: {heuristic_risk} (LLM suggested: {value})"
                                    )
                                else:
                                    metadata[field] = value
                                    self.logger.debug(
                                        f"LLM (with context) provided {field}: {value}"
                                    )
                            else:
                                metadata[field] = value
                                self.logger.debug(
                                    f"LLM (with context) provided {field}: {value}"
                                )

                    # Extract priority if review is recommended and not provided by LLM
                    if (
                        metadata.get("code_review_recommended")
                        and "code_review_priority" not in metadata
                    ):
                        priority = self.extract_code_review_priority(
                            documentation, document_record
                        )
                        if priority:
                            metadata["code_review_priority"] = priority
                else:
                    # LLM failed, use heuristic results as fallback
                    self.logger.warning(
                        "LLM extraction failed, falling back to heuristic results"
                    )
                    if heuristic_context and "decisions" in heuristic_context:
                        metadata = heuristic_context["decisions"].copy()
                        self.logger.info(
                            f"Using heuristic fallback decisions: {metadata}"
                        )

            else:
                # No LLM available - use heuristic results directly
                self.logger.debug("No LLM available, using heuristic results directly")
                if heuristic_context and "decisions" in heuristic_context:
                    metadata = heuristic_context["decisions"].copy()
                    self.logger.info(f"Using heuristic fallback decisions: {metadata}")
                else:
                    self.logger.warning("No heuristic context available for fallback")

        except Exception as e:
            self.logger.error(f"Error extracting document metadata: {e}")

        # Store heuristic context for UI display and transparency
        if "heuristic_context" not in locals():
            heuristic_context = {}
        metadata["heuristic_context"] = heuristic_context

        # Store heuristic context in document record for database persistence
        if document_record:
            document_record.heuristic_context = heuristic_context

        return metadata

    def _gather_heuristic_context(
        self,
        documentation: str,
        document_record: Optional["DocumentRecord"] = None,
        override_aggressiveness: Optional[str] = None,
        change_requests: Optional[List] = None,
    ) -> Dict[str, Any]:
        """Gather heuristic context indicators to prime the LLM for better decisions

        Args:
            documentation: The documentation content to analyze
            document_record: Optional document record for enhanced context
            override_aggressiveness: Optional risk aggressiveness level to use instead of configured level
        """
        context: Dict[str, Any] = {"indicators": {}, "decisions": {}, "reasoning": []}

        try:
            # Extract heuristic decisions
            code_review_rec = self.extract_code_review_recommendation(
                documentation, document_record
            )
            doc_impact = self.extract_documentation_impact(
                documentation, document_record, override_aggressiveness
            )
            risk_level = self.extract_risk_level(
                documentation, document_record, override_aggressiveness, change_requests
            )

            # Store decisions for fallback
            if code_review_rec is not None:
                context["decisions"]["code_review_recommended"] = code_review_rec
                if code_review_rec:
                    priority = self.extract_code_review_priority(
                        documentation, document_record
                    )
                    if priority:
                        context["decisions"]["code_review_priority"] = priority

            if doc_impact is not None:
                context["decisions"]["documentation_impact"] = doc_impact

            if risk_level:
                context["decisions"]["risk_level"] = risk_level

            # Gather context indicators for LLM
            indicators: Dict[str, Union[str, List[str]]] = {}

            # Complexity indicators - enhanced with more patterns
            complexity_high_keywords = [
                "new",
                "function",
                "class",
                "algorithm",
                "complex",
                "refactor",
                "restructure",
                "rewrite",
                # Protocol-specific high complexity
                "state machine",
                "parser",
                "encoder",
                "decoder",
                "serializer",
                "deserializer",
                "protocol stack",
                "message queue",
                "buffer management",
                "flow control",
                # Multi-threading high complexity
                "thread pool",
                "concurrent",
                "parallel",
                "synchronization",
                "race condition",
                "deadlock",
                "atomic operation",
                "lock-free",
                "wait-free",
                "producer consumer",
                # Memory management high complexity
                "memory pool",
                "garbage collector",
                "reference counting",
                "memory allocator",
                "buffer overflow",
                "memory leak",
                "smart pointer",
                "raii",
            ]
            complexity_low_keywords = [
                "simple",
                "minimal",
                "minor",
                "fix",
                "typo",
                "style",
                "format",
                "comment",
                "test",
                # Protocol-specific low complexity
                "constant",
                "enum",
                "flag",
                "status",
                "error code",
                "message id",
                # Threading/Memory low complexity
                "thread local",
                "immutable",
                "const",
                "readonly",
                "static",
            ]

            doc_lower = documentation.lower()
            high_complexity_matches = sum(
                1 for kw in complexity_high_keywords if kw in doc_lower
            )
            low_complexity_matches = sum(
                1 for kw in complexity_low_keywords if kw in doc_lower
            )

            if (
                high_complexity_matches > low_complexity_matches
                and high_complexity_matches >= 2
            ):
                indicators["complexity"] = "HIGH - significant structural changes"
            elif (
                low_complexity_matches > high_complexity_matches
                and low_complexity_matches >= 2
            ):
                indicators["complexity"] = "LOW - simple maintenance changes"
            elif high_complexity_matches > 0:
                indicators["complexity"] = (
                    "MEDIUM - moderate changes with some complexity"
                )
            else:
                indicators["complexity"] = "MEDIUM - standard changes"

            # Risk keywords with probabilistic confidence ratings (keyword: confidence_score)
            # Confidence: 0.0-1.0 where 1.0 = highest confidence this indicates risk
            # ADJUSTED: More conservative confidence scores to reduce false CRITICAL assessments
            risk_keywords_with_confidence = {
                # CRITICAL Risk Keywords (0.85-1.0 confidence) - Only truly critical security/safety issues
                "buffer overflow": 1.0,
                "segfault": 1.0,
                "memory corruption": 1.0,
                "use after free": 1.0,
                "race condition": 0.90,
                "deadlock": 0.90,
                "memory leak": 0.85,
                "critical": 0.85,
                "hotfix": 0.85,
                "urgent": 0.85,
                "vulnerability": 0.90,
                "exploit": 0.95,
                # HIGH Risk Keywords (0.65-0.84 confidence) - Important but not always critical
                "security": 0.75,
                "password": 0.70,
                "crypto": 0.75,
                "authentication": 0.70,
                "database": 0.65,
                "migration": 0.75,
                "production": 0.70,
                "breaking": 0.80,
                "auth": 0.65,
                "login": 0.65,
                "token": 0.65,
                "ssl": 0.70,
                "tls": 0.70,
                "schema": 0.70,
                "sql": 0.60,
                "major": 0.65,
                "api": 0.50,
                "endpoint": 0.60,
                "mutex": 0.75,
                "lock": 0.65,
                "semaphore": 0.75,
                "atomic": 0.70,
                "volatile": 0.75,
                "critical section": 0.75,
                "thread safe": 0.65,
                "malloc": 0.70,
                "free": 0.70,
                "alloc": 0.65,
                "dealloc": 0.70,
                "leak": 0.75,
                "memory pool": 0.65,
                "smart pointer": 0.60,
                "shared_ptr": 0.60,
                "unique_ptr": 0.60,
                "raii": 0.65,
                # MEDIUM Risk Keywords (0.4-0.64 confidence) - Common development terms
                "data": 0.45,
                "deploy": 0.55,
                "server": 0.50,
                "environment": 0.45,
                "config": 0.50,
                "settings": 0.45,
                "integration": 0.50,
                "external": 0.45,
                "protocol": 0.60,
                "packet": 0.60,
                "message": 0.45,
                "frame": 0.55,
                "header": 0.50,
                "payload": 0.55,
                "encoding": 0.50,
                "decoding": 0.50,
                "serialization": 0.55,
                "parsing": 0.50,
                "buffer": 0.60,
                "timeout": 0.50,
                "handshake": 0.60,
                "connection": 0.45,
                "socket": 0.55,
                "network": 0.45,
                "communication": 0.40,
                "transmission": 0.45,
                "synchronization": 0.70,
                "state machine": 0.75,
                "thread": 0.70,
                "threading": 0.75,
                "multithread": 0.80,
                "concurrent": 0.70,
                "concurrency": 0.75,
                "parallel": 0.65,
                "async": 0.60,
                "synchronize": 0.65,
                "thread pool": 0.75,
                "queue": 0.55,
                "producer": 0.60,
                "consumer": 0.60,
                "barrier": 0.65,
                "condition variable": 0.70,
                "spinlock": 0.75,
                "rwlock": 0.75,
                "memory": 0.65,
                "garbage": 0.60,
                "heap": 0.65,
                "stack": 0.55,
                "pointer": 0.60,
                "reference": 0.55,
                "delete": 0.60,
                "new": 0.50,
                "weak_ptr": 0.70,
            }

            # Low-risk exclusion keywords - these reduce risk assessment when present
            low_risk_keywords = [
                "test",
                "spec",
                "readme",
                "doc",
                "comment",
                "style",
                "format",
                "lint",
                "typo",
                "whitespace",
                "import",
                "example",
                "sample",
                "demo",
                "minor",
                "trivial",
            ]

            # Find risk keywords and calculate weighted confidence scores
            doc_lower = documentation.lower()
            found_risk_keywords = []
            risk_confidence_scores = []

            for keyword, confidence in risk_keywords_with_confidence.items():
                if keyword in doc_lower:
                    found_risk_keywords.append(keyword)
                    risk_confidence_scores.append(confidence)

            found_low_risk_keywords = [
                kw for kw in low_risk_keywords if kw in doc_lower
            ]

            # Calculate probabilistic risk assessment using confidence-weighted voting
            total_risk_confidence = (
                sum(risk_confidence_scores) if risk_confidence_scores else 0
            )
            avg_risk_confidence = (
                total_risk_confidence / len(risk_confidence_scores)
                if risk_confidence_scores
                else 0
            )
            low_risk_score = len(found_low_risk_keywords)

            # Get risk level thresholds based on repository aggressiveness setting
            aggressiveness = self._get_repository_aggressiveness(
                document_record, override_aggressiveness
            )
            critical_threshold, high_threshold, medium_threshold = (
                self._get_risk_thresholds(aggressiveness)
            )

            # Determine risk level using probabilistic voting
            if (
                total_risk_confidence >= critical_threshold
                and avg_risk_confidence >= 0.80
            ):
                risk_level = "CRITICAL"
                critical_keywords = [
                    kw
                    for kw, conf in zip(found_risk_keywords, risk_confidence_scores)
                    if conf >= 0.85
                ]
                indicators["risk_keywords"] = found_risk_keywords
                indicators["risk_confidence"] = f"{avg_risk_confidence:.2f}"
                indicators["risk_assessment"] = (
                    f"CRITICAL - high-confidence critical areas: {', '.join(critical_keywords[:3])}"
                )
            elif (
                total_risk_confidence >= high_threshold and avg_risk_confidence >= 0.65
            ):
                risk_level = "HIGH"
                indicators["risk_keywords"] = found_risk_keywords
                indicators["risk_confidence"] = f"{avg_risk_confidence:.2f}"
                indicators["risk_assessment"] = (
                    f"HIGH - confidence {avg_risk_confidence:.2f}: {', '.join(found_risk_keywords[:4])}"
                )
            elif low_risk_score >= 3 and (
                total_risk_confidence < medium_threshold
                or low_risk_score > len(found_risk_keywords)
            ):
                risk_level = "LOW"
                indicators["low_risk_keywords"] = found_low_risk_keywords
                indicators["risk_assessment"] = (
                    f"LOW - primarily {', '.join(found_low_risk_keywords[:3])} changes"
                )
            elif total_risk_confidence >= medium_threshold:
                risk_level = "MEDIUM"
                indicators["risk_keywords"] = found_risk_keywords
                indicators["risk_confidence"] = f"{avg_risk_confidence:.2f}"
                indicators["risk_assessment"] = (
                    f"MEDIUM - confidence {avg_risk_confidence:.2f}: {', '.join(found_risk_keywords[:3])}"
                )
            else:
                risk_level = "MEDIUM"
                indicators["risk_assessment"] = (
                    "MEDIUM - no clear risk indicators detected"
                )

            # Documentation impact keywords with confidence ratings
            doc_keywords_with_confidence = {
                # HIGH Documentation Impact (0.8-1.0 confidence)
                "api": 0.95,
                "interface": 0.90,
                "endpoint": 0.90,
                "public": 0.85,
                "breaking": 0.95,
                "major": 0.80,
                "specification": 0.90,
                "spec": 0.85,
                "rfc": 0.90,
                "compatibility": 0.85,
                "thread safe": 0.85,
                "thread safety": 0.85,
                # MEDIUM-HIGH Documentation Impact (0.6-0.79 confidence)
                "client": 0.75,
                "user": 0.70,
                "ui": 0.75,
                "ux": 0.75,
                "frontend": 0.70,
                "gui": 0.70,
                "configuration": 0.70,
                "config": 0.65,
                "setup": 0.75,
                "install": 0.80,
                "deploy": 0.70,
                "environment": 0.60,
                "feature": 0.65,
                "webhook": 0.75,
                "callback": 0.70,
                "protocol": 0.75,
                "message": 0.60,
                "format": 0.70,
                "schema": 0.75,
                "command": 0.65,
                "response": 0.60,
                "request": 0.60,
                "field": 0.60,
                "parameter": 0.65,
                "version": 0.70,
                "standard": 0.65,
                "implementation": 0.60,
                "concurrency": 0.65,
                "synchronization": 0.65,
                "locking": 0.60,
                "memory management": 0.65,
                "allocation": 0.60,
                # MEDIUM Documentation Impact (0.4-0.59 confidence)
                "new": 0.50,
                "add": 0.45,
                "remove": 0.55,
                "integration": 0.55,
                "external": 0.50,
                "performance": 0.45,
                "scalability": 0.50,
                "resource": 0.40,
            }

            # No documentation impact keywords - these suggest internal-only changes
            no_doc_keywords = [
                "internal",
                "private",
                "refactor",
                "cleanup",
                "test",
                "spec",
                "style",
                "format",
                "lint",
                "debug",
                "logging",
                "performance",
            ]

            # Find documentation keywords and calculate weighted confidence scores
            found_doc_keywords = []
            doc_confidence_scores = []

            for keyword, confidence in doc_keywords_with_confidence.items():
                if keyword in doc_lower:
                    found_doc_keywords.append(keyword)
                    doc_confidence_scores.append(confidence)

            found_no_doc_keywords = [kw for kw in no_doc_keywords if kw in doc_lower]

            # Calculate probabilistic documentation impact using confidence-weighted voting
            total_doc_confidence = (
                sum(doc_confidence_scores) if doc_confidence_scores else 0
            )
            avg_doc_confidence = (
                total_doc_confidence / len(doc_confidence_scores)
                if doc_confidence_scores
                else 0
            )
            no_doc_score = len(found_no_doc_keywords)

            # Documentation impact thresholds based on confidence-weighted scores
            likely_threshold = 1.5  # High confidence needed for LIKELY
            possible_threshold = 0.8  # Medium confidence for POSSIBLE

            # Determine documentation impact using probabilistic voting
            if total_doc_confidence >= likely_threshold and avg_doc_confidence >= 0.7:
                indicators["doc_keywords"] = found_doc_keywords
                indicators["doc_confidence"] = f"{avg_doc_confidence:.2f}"
                high_conf_keywords = [
                    kw
                    for kw, conf in zip(found_doc_keywords, doc_confidence_scores)
                    if conf >= 0.8
                ]
                if high_conf_keywords:
                    indicators["doc_assessment"] = (
                        f"LIKELY - high-confidence user-facing: {', '.join(high_conf_keywords[:3])}"
                    )
                else:
                    indicators["doc_assessment"] = (
                        f"LIKELY - confidence {avg_doc_confidence:.2f}: {', '.join(found_doc_keywords[:3])}"
                    )
            elif no_doc_score >= 3 and (
                total_doc_confidence < possible_threshold
                or no_doc_score > len(found_doc_keywords)
            ):
                indicators["no_doc_keywords"] = found_no_doc_keywords
                indicators["doc_assessment"] = (
                    f"UNLIKELY - primarily {', '.join(found_no_doc_keywords[:3])} changes"
                )
            elif total_doc_confidence >= possible_threshold:
                indicators["doc_keywords"] = found_doc_keywords
                indicators["doc_confidence"] = f"{avg_doc_confidence:.2f}"
                indicators["doc_assessment"] = (
                    f"POSSIBLE - confidence {avg_doc_confidence:.2f}: {', '.join(found_doc_keywords[:2])}"
                )
            else:
                indicators["doc_assessment"] = (
                    "UNCERTAIN - no clear documentation indicators"
                )

            # File type indicators (if available from document_record)
            if document_record and hasattr(document_record, "filename"):
                filename = document_record.filename.lower()
                if any(ext in filename for ext in [".py", ".js", ".java", ".cpp"]):
                    indicators["file_type"] = "CODE - source code changes"
                elif any(ext in filename for ext in [".md", ".txt", ".rst"]):
                    indicators["file_type"] = "DOCS - documentation changes"
                elif any(
                    ext in filename for ext in [".json", ".yaml", ".xml", ".conf"]
                ):
                    indicators["file_type"] = "CONFIG - configuration changes"

            # Protocol communications detection - specialized for protocol development
            protocol_keywords = [
                "protocol",
                "packet",
                "message",
                "frame",
                "header",
                "payload",
                "command",
                "response",
                "handshake",
                "acknowledgment",
                "ack",
                "nack",
                "checksum",
                "crc",
                "sequence",
                "communication",
                "transmission",
                "network",
                "socket",
                "connection",
                "session",
            ]
            found_protocol_keywords = [
                kw for kw in protocol_keywords if kw in documentation.lower()
            ]

            if found_protocol_keywords and len(found_protocol_keywords) >= 2:
                indicators["protocol_keywords"] = found_protocol_keywords
                indicators["protocol_assessment"] = (
                    f"PROTOCOL - involves {', '.join(found_protocol_keywords[:3])} components"
                )
                # Protocol changes typically require higher scrutiny
                if (
                    "risk_assessment" in indicators
                    and "LOW" in indicators["risk_assessment"]
                ):
                    indicators["risk_assessment"] = (
                        "MEDIUM - protocol changes require careful review"
                    )

            # Multi-threading detection - high-risk concurrency patterns
            threading_keywords = [
                "thread",
                "threading",
                "concurrent",
                "parallel",
                "async",
                "mutex",
                "lock",
                "semaphore",
                "atomic",
                "volatile",
                "race condition",
                "deadlock",
                "synchronize",
                "critical section",
                "thread safe",
                "producer",
                "consumer",
                "barrier",
            ]
            # Critical threading keywords that indicate real concurrency work
            critical_threading = [
                "race condition",
                "deadlock",
                "mutex",
                "semaphore",
                "atomic",
                "thread safe",
                "critical section",
            ]

            found_threading_keywords = [
                kw for kw in threading_keywords if kw in documentation.lower()
            ]
            critical_threading_found = [
                kw for kw in critical_threading if kw in found_threading_keywords
            ]

            # Require either critical threading keywords OR multiple threading indicators
            if critical_threading_found or len(found_threading_keywords) >= 3:
                indicators["threading_keywords"] = found_threading_keywords
                indicators["threading_assessment"] = (
                    f"THREADING - involves {', '.join(found_threading_keywords[:3])} patterns"
                )
                # Note threading work but don't automatically elevate risk level
                # Let the base risk assessment handle threading keywords through normal scoring
                if critical_threading_found and len(critical_threading_found) >= 2:
                    # Just note the threading complexity, don't override risk level
                    indicators["threading_note"] = (
                        f"Complex threading: {', '.join(critical_threading_found[:3])}"
                    )

            # Memory management detection - critical for stability
            memory_keywords = [
                "memory",
                "malloc",
                "free",
                "alloc",
                "dealloc",
                "new",
                "delete",
                "heap",
                "stack",
                "pointer",
                "reference",
                "leak",
                "buffer overflow",
                "segfault",
                "memory pool",
                "smart pointer",
                "shared_ptr",
                "unique_ptr",
                "weak_ptr",
                "raii",
                "garbage",
            ]
            # Critical memory keywords that indicate real memory management work
            critical_memory = [
                "malloc",
                "free",
                "alloc",
                "dealloc",
                "leak",
                "buffer overflow",
                "segfault",
                "memory pool",
                "smart pointer",
                "shared_ptr",
                "unique_ptr",
                "raii",
            ]
            # Common but less critical keywords
            common_memory = ["new", "delete", "memory", "pointer", "reference"]

            found_memory_keywords = [
                kw for kw in memory_keywords if kw in documentation.lower()
            ]
            critical_memory_found = [
                kw for kw in critical_memory if kw in found_memory_keywords
            ]
            common_memory_found = [
                kw for kw in common_memory if kw in found_memory_keywords
            ]

            # Require critical keywords OR multiple common keywords for detection
            if critical_memory_found or len(common_memory_found) >= 2:
                indicators["memory_keywords"] = found_memory_keywords
                indicators["memory_assessment"] = (
                    f"MEMORY - involves {', '.join(found_memory_keywords[:3])} management"
                )
                # Note memory management work but don't automatically elevate risk level
                # Let the base risk assessment handle memory keywords through normal scoring
                if critical_memory_found and len(critical_memory_found) >= 2:
                    # Just note the memory complexity, don't override risk level
                    indicators["memory_note"] = (
                        f"Complex memory management: {', '.join(critical_memory_found[:3])}"
                    )

            context["indicators"] = indicators

            # Build reasoning summary
            reasoning = []
            if code_review_rec is True:
                reasoning.append(
                    "Heuristic suggests code review needed based on content analysis"
                )
            elif code_review_rec is False:
                reasoning.append(
                    "Heuristic suggests no code review needed based on content analysis"
                )

            if doc_impact is True:
                reasoning.append("Heuristic suggests documentation updates needed")
            elif doc_impact is False:
                reasoning.append("Heuristic suggests no documentation updates needed")

            if risk_level:
                reasoning.append(f"Heuristic assessed risk level as {risk_level}")

            context["reasoning"] = reasoning

        except Exception as e:
            self.logger.warning(f"Error gathering heuristic context: {e}")

        return context

    def _extract_metadata_with_llm_enhanced(
        self,
        documentation: str,
        document_record: Optional["DocumentRecord"] = None,
        heuristic_context: Optional[Dict[str, Any]] = None,
        override_model: Optional[str] = None,
        override_aggressiveness: Optional[str] = None,
    ) -> tuple[Dict[str, Any], Optional[str]]:
        """Extract metadata using LLM enhanced with heuristic context for better decisions

        Args:
            documentation: The documentation content to analyze
            document_record: Optional document record for enhanced context
            heuristic_context: Optional heuristic analysis context
            override_model: Optional AI model to use instead of configured model
            override_aggressiveness: Optional risk aggressiveness level to use instead of configured level
        """
        if not self.ollama_client:
            self.logger.warning(
                "No Ollama client available for enhanced LLM metadata extraction"
            )
            return {}, None

        try:
            # Build enhanced prompt with heuristic context
            context_prompt = (
                self._build_context_prompt(heuristic_context)
                if heuristic_context
                else ""
            )

            # Try to use enhanced prompts if available
            config = self.config_manager.load_config() if self.config_manager else None
            use_enhanced = config and getattr(config, "use_enhanced_prompts", True)

            if document_record and use_enhanced:
                try:
                    from prompt_templates import ContextAnalyzer, PromptTemplateManager

                    if not config:
                        raise Exception("No config available")

                    # Initialize enhanced prompt system
                    prompt_manager = PromptTemplateManager(config)
                    context_analyzer = ContextAnalyzer(config)

                    # Analyze the change context
                    context = context_analyzer.analyze_change_context(
                        document=document_record,
                        commit_message=getattr(document_record, "commit_message", None),
                    )

                    # Get enhanced prompts (note: enhanced prompts don't support heuristic context yet)
                    system_prompt, user_prompt = (
                        prompt_manager.get_metadata_extraction_prompt(
                            documentation, context
                        )
                    )

                    # Enhance the system prompt with heuristic context
                    if heuristic_context:
                        context_prompt = self._build_context_prompt(heuristic_context)
                        system_prompt = context_prompt + "\n\n" + system_prompt

                    self.logger.debug(
                        f"Using enhanced prompts with heuristic context for document {document_record.id}"
                    )

                except Exception as e:
                    self.logger.warning(
                        f"Failed to use enhanced prompts, falling back to basic with context: {e}"
                    )
                    # Fall back to basic prompts with context
                    system_prompt, user_prompt = (
                        self._get_basic_metadata_prompts_with_context(
                            documentation, context_prompt
                        )
                    )
            else:
                # Use basic prompts with context when no document context available or enhanced prompts disabled
                system_prompt, user_prompt = (
                    self._get_basic_metadata_prompts_with_context(
                        documentation, context_prompt
                    )
                )

            # Determine which model to use - override takes precedence
            specialized_model = None
            actual_model_used = None

            if override_model:
                # Use the override model directly
                specialized_model = override_model
                actual_model_used = override_model
                self.logger.info(
                    f"🎯 MetadataExtractor using override model with heuristic context: {override_model}"
                )
            elif (
                config
                and hasattr(self.ollama_client, "config")
                and self.ollama_client.config
            ):
                # Prefer risk assessment model, fall back to code review model
                risk_model = getattr(
                    self.ollama_client.config, "ollama_model_risk_assessment", None
                )
                code_review_model = getattr(
                    self.ollama_client.config, "ollama_model_code_review", None
                )
                specialized_model = risk_model or code_review_model

                if specialized_model:
                    self.logger.info(
                        f"🎯 MetadataExtractor using specialized model with heuristic context: {specialized_model}"
                    )
                    actual_model_used = specialized_model
                else:
                    actual_model_used = (
                        self.ollama_client.config.ollama_model
                        if self.ollama_client.config
                        else None
                    )

            response = self.ollama_client.call_ollama(
                user_prompt, system_prompt, model=specialized_model
            )

            if response:
                try:
                    # Clean up response and parse JSON
                    cleaned_response = response.strip()
                    if cleaned_response.startswith("```json"):
                        cleaned_response = cleaned_response[7:]
                    if cleaned_response.endswith("```"):
                        cleaned_response = cleaned_response[:-3]
                    cleaned_response = cleaned_response.strip()

                    return json.loads(cleaned_response), actual_model_used
                except json.JSONDecodeError as e:
                    self.logger.warning(
                        f"Failed to parse enhanced LLM metadata response as JSON: {e}"
                    )
                    return {}, actual_model_used

            return {}, actual_model_used
        except Exception as e:
            self.logger.error(f"Error extracting metadata with enhanced LLM: {e}")
            return {}, None

    def _build_context_prompt(self, heuristic_context: Dict[str, Any]) -> str:
        """Build context prompt from heuristic analysis"""
        if not heuristic_context:
            return ""

        context_parts = ["HEURISTIC ANALYSIS CONTEXT:"]

        # Add indicators
        indicators = heuristic_context.get("indicators", {})
        if indicators:
            context_parts.append("\nKey Indicators:")
            for key, value in indicators.items():
                context_parts.append(f"- {key.replace('_', ' ').title()}: {value}")

        # Add reasoning
        reasoning = heuristic_context.get("reasoning", [])
        if reasoning:
            context_parts.append("\nHeuristic Assessment:")
            for reason in reasoning:
                context_parts.append(f"- {reason}")

        # Add preliminary decisions
        decisions = heuristic_context.get("decisions", {})
        if decisions:
            context_parts.append("\nPreliminary Heuristic Decisions:")
            for field, value in decisions.items():
                context_parts.append(f"- {field.replace('_', ' ').title()}: {value}")

        context_parts.append(
            "\nPlease consider this heuristic analysis when making your final decisions, but feel free to override if the full context suggests different conclusions.\n"
        )

        return "\n".join(context_parts)

    def _get_basic_metadata_prompts_with_context(
        self, documentation: str, context_prompt: str
    ) -> Tuple[str, str]:
        """Get basic metadata extraction prompts enhanced with heuristic context"""

        system_prompt = f"""You are a metadata extraction assistant with access to heuristic analysis context.

{context_prompt}

Analyze the provided technical documentation and extract specific metadata fields.

Return ONLY a JSON object with these exact fields:
- code_review_recommended: true/false (whether code review is recommended)
- code_review_priority: "CRITICAL"/"HIGH"/"MEDIUM"/"LOW" (priority level if review is recommended, null if not recommended)
- code_review_confidence: 0.0-1.0 (confidence in code review recommendation)
- risk_level: "CRITICAL"/"HIGH"/"MEDIUM"/"LOW" (risk level of the changes)
- risk_confidence: 0.0-1.0 (confidence in risk assessment, where 1.0 = completely certain)
- documentation_impact: true/false (whether documentation needs updates)
- documentation_confidence: 0.0-1.0 (confidence in documentation impact assessment)
- reasoning: "Brief explanation of key factors that influenced the risk and priority decisions"

CRITICAL risk level should be used for:
- Security vulnerabilities, buffer overflows, memory corruption
- Race conditions, deadlocks, critical threading issues
- Production system failures, data loss risks
- Multiple high-risk factors combined

Consider both the heuristic analysis context and the full documentation content. The heuristic analysis provides helpful indicators, but make your final decision based on the complete picture.

Be precise and consistent. If you cannot determine a value, use null."""

        user_prompt = f"""Analyze this technical documentation and extract the metadata:

{documentation}

Return only the JSON object with the metadata fields."""

        return system_prompt, user_prompt

    def extract_ai_summary(self, content: str) -> Optional[str]:
        """Extract AI-generated summary from document content to use as commit message"""
        try:
            # Look for the Commit Summary section in the documentation
            summary_content = self.extract_section(content, "Commit Summary")

            if summary_content:
                # Clean up the summary - take first sentence or first line if it's concise
                lines = summary_content.split("\n")
                first_line = lines[0].strip()

                # If first line is a complete sentence and not too long, use it
                if (
                    first_line
                    and len(first_line) <= 100
                    and (first_line.endswith(".") or len(lines) == 1)
                ):
                    return first_line.rstrip(".")

                # Otherwise, try to find a concise summary sentence
                for line in lines:
                    line = line.strip()
                    if (
                        line
                        and len(line) <= 100
                        and not line.startswith("*")
                        and not line.startswith("-")
                    ):
                        return line.rstrip(".")

                # Fallback: use first 80 characters of summary
                if len(first_line) > 0:
                    return (
                        (first_line[:80] + "...")
                        if len(first_line) > 80
                        else first_line.rstrip(".")
                    )

            return None
        except Exception as e:
            self.logger.debug(f"Error extracting AI summary: {e}")
            return None

    def extract_section(self, content: str, section_name: str) -> Optional[str]:
        """Extract a specific section from the document content"""
        try:
            pattern = rf"##\s*{re.escape(section_name)}\s*\n(.*?)(?=\n##|\Z)"
            match = re.search(pattern, content, re.DOTALL | re.IGNORECASE)
            return match.group(1).strip() if match else None
        except Exception:
            return None

    def extract_field(self, content: str, field_name: str) -> Optional[str]:
        """Extract field value from document content (for markdown metadata)"""
        pattern = rf"\*\*{re.escape(field_name)}:\*\*\s*(.+?)(?:\n|$)"
        match = re.search(pattern, content)
        return match.group(1).strip() if match else None

    def parse_date_with_fallbacks(
        self, date_input: Any, filename_date: Optional[str] = None
    ) -> datetime:
        """Parse date with multiple fallback strategies"""
        # Try repository date first
        if date_input:
            try:
                if isinstance(date_input, str):
                    return datetime.fromisoformat(date_input.replace("Z", "+00:00"))
                elif isinstance(date_input, datetime):
                    return date_input
            except (ValueError, TypeError):
                pass

        # Try filename date
        if filename_date:
            try:
                return datetime.strptime(filename_date, "%Y-%m-%d")
            except ValueError:
                pass

        # Final fallback
        return datetime.now()

    def generate_document_id(
        self, server_name: str, repository_id: str, revision: int
    ) -> str:
        """Generate consistent document ID with server identifier"""
        return f"{server_name}_{repository_id}_{revision}"

    def extract_code_review_recommendation(
        self, content: str, document_record: Optional["DocumentRecord"] = None
    ) -> Optional[bool]:
        """Extract code review recommendation using voting mechanism between heuristic and LLM analysis"""
        try:
            votes = {}
            confidence_scores = {}

            # Method 1: Heuristic analysis
            heuristic_result = self._extract_code_review_rec_heuristic_simple(content)
            if heuristic_result:
                recommendation, confidence = heuristic_result
                votes["heuristic"] = recommendation
                confidence_scores["heuristic"] = confidence
                self.logger.debug(
                    f"Code review heuristic vote: {recommendation} (confidence: {confidence:.2f})"
                )

            # Method 2: LLM analysis (if available)
            if self.ollama_client:
                llm_result, _ = self._extract_metadata_with_llm(
                    content, document_record
                )
                llm_recommendation = llm_result.get("code_review_recommended")
                if llm_recommendation is not None:
                    votes["llm"] = llm_recommendation
                    confidence_scores["llm"] = 0.7  # Default LLM confidence
                    self.logger.debug(
                        f"Code review LLM vote: {llm_recommendation} (confidence: 0.70)"
                    )

            # Apply simple voting mechanism
            final_recommendation = self._vote_on_code_review_simple(
                votes, confidence_scores
            )

            if final_recommendation is not None:
                self.logger.debug(
                    f"Final code review decision: {final_recommendation} (from {len(votes)} votes)"
                )

            return final_recommendation

        except Exception as e:
            self.logger.debug(f"Error extracting code review recommendation: {e}")
            return None

    def _extract_code_review_rec_heuristic_simple(
        self, content: str
    ) -> Optional[tuple[bool, float]]:
        """Simple heuristic code review recommendation extraction with confidence scoring"""
        try:
            review_section = self.extract_section(content, "Code Review Recommendation")
            if not review_section:
                return None

            review_lower = review_section.lower()

            # Look for clear decisions at the start of the section
            first_sentence = (
                review_lower.split(".")[0]
                if "." in review_lower
                else review_lower[:100]
            )

            # Check for explicit "No" decisions first (high confidence)
            if (
                "no, this commit does not require" in first_sentence
                or "no, this commit should not" in first_sentence
                or "not required" in first_sentence
                or "no review" in first_sentence
                or "skip review" in first_sentence
                or "does not require" in first_sentence
            ):
                return (False, 0.9)

            # Check for explicit "Yes" decisions (high confidence)
            elif (
                "yes, this commit should" in first_sentence
                or "yes, this commit requires" in first_sentence
                or "recommended" in first_sentence
                or "should be reviewed" in first_sentence
                or "requires review" in first_sentence
            ):
                return (True, 0.9)

            # Fallback to broader patterns in the full section (medium confidence)
            elif (
                "not required" in review_lower
                or "no review" in review_lower
                or "skip review" in review_lower
                or "does not require" in review_lower
                or "should not require" in review_lower
                or "not be subject to" in review_lower
                or "not necessary" in review_lower
            ):
                return (False, 0.6)
            elif (
                "recommended" in review_lower
                or "should be reviewed" in review_lower
                or "should be code reviewed" in review_lower
                or "requires review" in review_lower
                or "should be considered" in review_lower
                or "consider" in review_lower
                or "priority" in review_lower
            ):
                return (True, 0.6)

            return (True, 0.3)  # Default to review recommended with low confidence

        except Exception as e:
            self.logger.debug(f"Error in simple heuristic code review extraction: {e}")
            return None

    def _vote_on_code_review_simple(
        self, votes: dict, confidence_scores: dict
    ) -> Optional[bool]:
        """Simple voting mechanism for code review recommendation determination"""
        try:
            if not votes:
                return None

            if len(votes) == 1:
                # Single vote - return it if confidence is reasonable
                method, recommendation = next(iter(votes.items()))
                confidence = confidence_scores.get(method, 0.5)
                return recommendation if confidence >= 0.3 else True

            # Multiple votes - weighted by confidence
            rec_scores = {"True": 0.0, "False": 0.0}
            total_weight = 0.0

            for method, recommendation in votes.items():
                confidence = confidence_scores.get(method, 0.5)
                rec_key = "True" if recommendation else "False"
                rec_scores[rec_key] += confidence
                total_weight += confidence

            if total_weight > 0:
                for rec in rec_scores:
                    rec_scores[rec] /= total_weight

            # Find winning recommendation
            winning_rec = max(rec_scores.keys(), key=lambda k: rec_scores[k])
            winning_score = rec_scores[winning_rec]

            # Require minimum confidence
            if winning_score < 0.4:
                return True  # Safe fallback to review recommended

            return winning_rec == "True"

        except Exception as e:
            self.logger.debug(f"Error in simple code review voting: {e}")
            return True

    def extract_code_review_priority(
        self, content: str, document_record: Optional["DocumentRecord"] = None
    ) -> Optional[str]:
        """Extract code review priority using voting mechanism between heuristic and LLM analysis"""
        try:
            votes = {}
            confidence_scores = {}

            # Method 1: Heuristic analysis
            heuristic_result = self._extract_priority_heuristic_simple(content)
            if heuristic_result:
                priority, confidence = heuristic_result
                votes["heuristic"] = priority
                confidence_scores["heuristic"] = confidence
                self.logger.debug(
                    f"Heuristic vote: {priority} (confidence: {confidence:.2f})"
                )

            # Method 2: LLM analysis (if available)
            if self.ollama_client:
                llm_result, _ = self._extract_metadata_with_llm(
                    content, document_record
                )
                llm_priority = llm_result.get("code_review_priority")
                if llm_priority:
                    votes["llm"] = llm_priority
                    confidence_scores["llm"] = 0.7  # Default LLM confidence
                    self.logger.debug(f"LLM vote: {llm_priority} (confidence: 0.70)")

            # Apply simple voting mechanism
            final_priority = self._vote_on_priority_simple(votes, confidence_scores)

            if final_priority:
                self.logger.debug(
                    f"Final priority decision: {final_priority} (from {len(votes)} votes)"
                )

            return final_priority

        except Exception as e:
            self.logger.debug(f"Error extracting code review priority: {e}")
            return None

    def _extract_priority_heuristic_simple(
        self, content: str
    ) -> Optional[tuple[str, float]]:
        """Simple heuristic priority extraction with confidence scoring"""
        try:
            review_section = self.extract_section(content, "Code Review Recommendation")
            if not review_section:
                return None

            review_lower = review_section.lower()

            # Explicit priority mentions (high confidence)
            if (
                "high priority" in review_lower
                or "critical" in review_lower
                or "urgent" in review_lower
            ):
                confidence = 0.9 if "high priority" in review_lower else 0.8
                return ("HIGH", confidence)
            elif "medium priority" in review_lower or "moderate" in review_lower:
                confidence = 0.9 if "medium priority" in review_lower else 0.7
                return ("MEDIUM", confidence)
            elif "low priority" in review_lower or "minor" in review_lower:
                confidence = 0.9 if "low priority" in review_lower else 0.8
                return ("LOW", confidence)

            # Contextual inference (medium confidence)
            if any(
                indicator in review_lower
                for indicator in ["simple", "straightforward", "minimal"]
            ):
                return ("LOW", 0.6)
            elif any(
                indicator in review_lower
                for indicator in ["extensive", "thorough", "careful"]
            ):
                return ("HIGH", 0.6)
            else:
                return ("MEDIUM", 0.3)  # Default with low confidence

        except Exception as e:
            self.logger.debug(f"Error in simple heuristic priority extraction: {e}")
            return None

    def _vote_on_priority_simple(
        self, votes: dict, confidence_scores: dict
    ) -> Optional[str]:
        """Simple voting mechanism for priority determination"""
        try:
            if not votes:
                return None

            if len(votes) == 1:
                # Single vote - return it if confidence is reasonable
                method, priority = next(iter(votes.items()))
                confidence = confidence_scores.get(method, 0.5)
                return priority if confidence >= 0.3 else "MEDIUM"

            # Multiple votes - weighted by confidence
            priority_scores = {"HIGH": 0.0, "MEDIUM": 0.0, "LOW": 0.0}
            total_weight = 0.0

            for method, priority in votes.items():
                confidence = confidence_scores.get(method, 0.5)
                priority_scores[priority] += confidence
                total_weight += confidence

            if total_weight > 0:
                for priority in priority_scores:
                    priority_scores[priority] /= total_weight

            # Find winning priority
            winning_priority = max(
                priority_scores.keys(), key=lambda k: priority_scores[k]
            )
            winning_score = priority_scores[winning_priority]

            # Require minimum confidence
            if winning_score < 0.4:
                return "MEDIUM"  # Safe fallback

            return winning_priority

        except Exception as e:
            self.logger.debug(f"Error in simple priority voting: {e}")
            return "MEDIUM"

    def extract_documentation_impact(
        self,
        content: str,
        document_record: Optional["DocumentRecord"] = None,
        override_aggressiveness: Optional[str] = None,
    ) -> Optional[bool]:
        """Extract documentation impact using voting mechanism between heuristic and LLM analysis"""
        try:
            votes = {}
            confidence_scores = {}

            # Get repository aggressiveness setting
            aggressiveness = self._get_repository_aggressiveness(
                document_record, override_aggressiveness
            )

            # Method 1: Heuristic analysis (now aggressiveness-aware)
            heuristic_result = self._extract_doc_impact_heuristic_simple(
                content, aggressiveness
            )
            if heuristic_result:
                impact, confidence = heuristic_result
                votes["heuristic"] = impact
                confidence_scores["heuristic"] = confidence
                self.logger.debug(
                    f"Doc impact heuristic vote: {impact} (confidence: {confidence:.2f})"
                )

            # Method 2: LLM analysis (if available)
            if self.ollama_client:
                llm_result, _ = self._extract_metadata_with_llm(
                    content, document_record
                )
                llm_impact = llm_result.get("documentation_impact")
                llm_doc_confidence = llm_result.get(
                    "documentation_confidence", 0.7
                )  # Get LLM's confidence or default
                if llm_impact is not None:
                    # Adjust LLM confidence based on aggressiveness for positive impact assessments
                    adjusted_confidence = float(llm_doc_confidence)
                    if aggressiveness == "AGGRESSIVE" and llm_impact:
                        adjusted_confidence *= (
                            0.8  # Reduce confidence in positive LLM assessments
                        )
                    elif aggressiveness == "VERY_AGGRESSIVE" and llm_impact:
                        adjusted_confidence *= 0.6  # Significantly reduce confidence in positive LLM assessments

                    votes["llm"] = llm_impact
                    confidence_scores["llm"] = adjusted_confidence
                    self.logger.debug(
                        f"Doc impact LLM vote: {llm_impact} (confidence: {adjusted_confidence:.2f})"
                    )

            # Apply aggressiveness-aware voting mechanism
            final_impact = self._vote_on_doc_impact_simple(
                votes, confidence_scores, aggressiveness
            )

            if final_impact is not None:
                self.logger.debug(
                    f"Final doc impact decision: {final_impact} (from {len(votes)} votes, aggressiveness: {aggressiveness})"
                )

            return final_impact

        except Exception as e:
            self.logger.debug(f"Error extracting documentation impact: {e}")
            return None

    def _extract_doc_impact_heuristic_simple(
        self, content: str, aggressiveness: str = "BALANCED"
    ) -> Optional[tuple[bool, float]]:
        """Simple heuristic documentation impact extraction with confidence scoring and aggressiveness awareness"""
        try:
            doc_section = self.extract_section(content, "Documentation Impact")
            if not doc_section:
                return None

            doc_lower = doc_section.lower()

            # Look for clear decisions at the start of the section
            first_sentence = (
                doc_lower.split(".")[0] if "." in doc_lower else doc_lower[:100]
            )

            # Check for explicit "No" decisions first (high confidence)
            if (
                "no, documentation updates are not required" in first_sentence
                or "no, this commit does not affect" in first_sentence
                or "not required" in first_sentence
                or "no updates" in first_sentence
                or "no impact" in first_sentence
                or "no documentation updates" in first_sentence
            ):
                return (False, 0.9)

            # Check for explicit "Yes" decisions (high confidence) - adjust confidence based on aggressiveness
            elif (
                "yes, documentation updates are needed" in first_sentence
                or "yes, this commit affects" in first_sentence
                or "required" in first_sentence
                or "should be updated" in first_sentence
                or "needs update" in first_sentence
            ):
                base_confidence = 0.9
                if aggressiveness == "AGGRESSIVE":
                    base_confidence *= 0.8
                elif aggressiveness == "VERY_AGGRESSIVE":
                    base_confidence *= 0.6
                return (True, base_confidence)

            # Fallback to broader patterns in the full section (medium confidence)
            elif (
                "not required" in doc_lower
                or "no updates" in doc_lower
                or "no impact" in doc_lower
                or "no documentation updates" in doc_lower
            ):
                return (False, 0.6)
            elif (
                "required" in doc_lower
                or "should be updated" in doc_lower
                or "needs update" in doc_lower
            ):
                # Apply aggressiveness adjustment to broader pattern matches
                base_confidence = 0.6
                if aggressiveness == "AGGRESSIVE":
                    base_confidence *= 0.7
                elif aggressiveness == "VERY_AGGRESSIVE":
                    base_confidence *= 0.5
                return (True, base_confidence)

            # Contextual analysis based on aggressiveness
            impact_indicators = [
                "api",
                "interface",
                "public",
                "user",
                "configuration",
                "setup",
                "install",
                "breaking",
            ]
            no_impact_indicators = [
                "internal",
                "private",
                "refactor",
                "cleanup",
                "test",
                "style",
                "fix",
                "bug",
            ]

            # Add more no-impact indicators for aggressive modes
            if aggressiveness in ["AGGRESSIVE", "VERY_AGGRESSIVE"]:
                no_impact_indicators.extend(
                    [
                        "minor",
                        "small",
                        "trivial",
                        "cosmetic",
                        "format",
                        "whitespace",
                        "initial",
                    ]
                )

            impact_count = sum(
                1 for indicator in impact_indicators if indicator in doc_lower
            )
            no_impact_count = sum(
                1 for indicator in no_impact_indicators if indicator in doc_lower
            )

            # Adjust decision thresholds based on aggressiveness
            if aggressiveness == "CONSERVATIVE":
                # Conservative: lower threshold for requiring documentation
                if impact_count >= no_impact_count:  # Equal counts favor documentation
                    confidence = min(0.5, 0.3 + (impact_count * 0.05))
                    return (True, confidence)
            elif aggressiveness == "BALANCED":
                # Balanced: standard logic
                if impact_count > no_impact_count:
                    confidence = min(0.4, 0.2 + (impact_count * 0.05))
                    return (True, confidence)
            else:  # AGGRESSIVE or VERY_AGGRESSIVE
                # Aggressive: much higher threshold for requiring documentation
                threshold_multiplier = 3 if aggressiveness == "VERY_AGGRESSIVE" else 2
                if impact_count > (no_impact_count * threshold_multiplier):
                    confidence = min(0.3, 0.1 + (impact_count * 0.05))
                    return (True, confidence)

            return (False, 0.3)  # Default to no impact with low confidence

        except Exception as e:
            self.logger.debug(f"Error in simple heuristic doc impact extraction: {e}")
            return None

    def _vote_on_doc_impact_simple(
        self, votes: dict, confidence_scores: dict, aggressiveness: str = "BALANCED"
    ) -> Optional[bool]:
        """Simple voting mechanism for documentation impact determination with aggressiveness awareness"""
        try:
            if not votes:
                return None

            # Adjust confidence thresholds based on aggressiveness
            if aggressiveness == "CONSERVATIVE":
                min_single_confidence = 0.2  # Lower threshold for single votes
                min_winning_score = 0.3  # Lower threshold for winning score
            elif aggressiveness == "BALANCED":
                min_single_confidence = 0.3
                min_winning_score = 0.4
            elif aggressiveness == "AGGRESSIVE":
                min_single_confidence = 0.4  # Higher threshold for single votes
                min_winning_score = 0.5  # Higher threshold for winning score
            else:  # VERY_AGGRESSIVE
                min_single_confidence = 0.5  # Much higher threshold for single votes
                min_winning_score = 0.6  # Much higher threshold for winning score

            if len(votes) == 1:
                # Single vote - return it if confidence meets aggressiveness threshold
                method, impact = next(iter(votes.items()))
                confidence = confidence_scores.get(method, 0.5)

                # For positive impact votes, apply stricter thresholds in aggressive modes
                if impact and aggressiveness in ["AGGRESSIVE", "VERY_AGGRESSIVE"]:
                    return impact if confidence >= min_single_confidence else False
                else:
                    return impact if confidence >= min_single_confidence else False

            # Multiple votes - weighted by confidence with aggressiveness adjustments
            impact_scores = {"True": 0.0, "False": 0.0}
            total_weight = 0.0

            for method, impact in votes.items():
                confidence = confidence_scores.get(method, 0.5)

                # Apply aggressiveness penalty to positive impact votes
                if impact and aggressiveness == "AGGRESSIVE":
                    confidence *= 0.8
                elif impact and aggressiveness == "VERY_AGGRESSIVE":
                    confidence *= 0.6

                impact_key = "True" if impact else "False"
                impact_scores[impact_key] += confidence
                total_weight += confidence

            if total_weight > 0:
                for impact in impact_scores:
                    impact_scores[impact] /= total_weight

            # Find winning impact
            winning_impact = max(impact_scores.keys(), key=lambda k: impact_scores[k])
            winning_score = impact_scores[winning_impact]

            # Apply aggressiveness-adjusted minimum confidence threshold
            if winning_score < min_winning_score:
                return False  # Safe fallback

            result = winning_impact == "True"

            # Log voting details for debugging
            vote_details = ", ".join(
                [
                    f"{method}:{impact}({confidence_scores.get(method, 0.5):.2f})"
                    for method, impact in votes.items()
                ]
            )
            self.logger.debug(
                f"Doc impact voting ({aggressiveness}): {vote_details} -> {result} ({winning_score:.2f})"
            )

            return result

        except Exception as e:
            self.logger.debug(f"Error in simple doc impact voting: {e}")
            return False

    def extract_risk_level(
        self,
        content: str,
        document_record: Optional["DocumentRecord"] = None,
        override_aggressiveness: Optional[str] = None,
        change_requests: Optional[List] = None,
    ) -> Optional[str]:
        """Extract risk level using voting mechanism between heuristic and LLM analysis"""
        try:
            votes = {}
            confidence_scores = {}

            # Get repository aggressiveness setting
            aggressiveness = self._get_repository_aggressiveness(
                document_record, override_aggressiveness
            )

            # Method 1: Heuristic analysis
            heuristic_result = self._extract_risk_heuristic_simple(
                content, aggressiveness
            )
            if heuristic_result:
                risk, confidence = heuristic_result
                votes["heuristic"] = risk
                confidence_scores["heuristic"] = confidence
                self.logger.debug(
                    f"Risk heuristic vote: {risk} (confidence: {confidence:.2f})"
                )

            # Method 2: LLM analysis (if available)
            if self.ollama_client:
                llm_result, _ = self._extract_metadata_with_llm(
                    content, document_record
                )
                llm_risk = llm_result.get("risk_level")
                llm_risk_confidence = llm_result.get(
                    "risk_confidence", 0.7
                )  # Get LLM's confidence or default
                if llm_risk:
                    # Adjust LLM confidence based on aggressiveness for high-risk assessments
                    adjusted_confidence = float(llm_risk_confidence)
                    if aggressiveness == "AGGRESSIVE" and llm_risk in [
                        "CRITICAL",
                        "HIGH",
                    ]:
                        adjusted_confidence *= (
                            0.8  # Reduce confidence in high-risk LLM assessments
                        )
                    elif aggressiveness == "VERY_AGGRESSIVE" and llm_risk in [
                        "CRITICAL",
                        "HIGH",
                    ]:
                        adjusted_confidence *= 0.5  # Significantly reduce confidence in high-risk LLM assessments
                    elif aggressiveness == "VERY_AGGRESSIVE" and llm_risk == "MEDIUM":
                        adjusted_confidence *= (
                            0.7  # Also reduce confidence in medium-risk LLM assessments
                        )

                    votes["llm"] = llm_risk
                    confidence_scores["llm"] = adjusted_confidence
                    self.logger.debug(
                        f"Risk LLM vote: {llm_risk} (confidence: {adjusted_confidence:.2f}, original: {llm_risk_confidence:.2f})"
                    )

            # Method 3: Change request risk analysis (if available)
            if change_requests:
                cr_risk, cr_confidence = self._extract_risk_from_change_requests(
                    change_requests
                )
                if cr_risk:
                    votes["change_request"] = cr_risk
                    confidence_scores["change_request"] = cr_confidence
                    self.logger.debug(
                        f"Risk change request vote: {cr_risk} (confidence: {cr_confidence:.2f})"
                    )

            # Method 4: Diff complexity analysis (if document record available)
            if document_record and hasattr(document_record, "changed_paths"):
                diff_risk, diff_confidence = self._extract_risk_from_diff_complexity(
                    document_record, aggressiveness
                )
                if diff_risk:
                    votes["diff_complexity"] = diff_risk
                    confidence_scores["diff_complexity"] = diff_confidence
                    self.logger.debug(
                        f"Risk diff complexity vote: {diff_risk} (confidence: {diff_confidence:.2f})"
                    )

            # Apply aggressiveness-aware voting mechanism
            final_risk = self._vote_on_risk_simple(
                votes, confidence_scores, aggressiveness
            )

            if final_risk:
                self.logger.debug(
                    f"Final risk decision: {final_risk} (from {len(votes)} votes, aggressiveness: {aggressiveness})"
                )

            return final_risk

        except Exception as e:
            self.logger.debug(f"Error extracting risk level: {e}")
            return None

    def _extract_risk_from_change_requests(
        self, change_requests
    ) -> tuple[Optional[str], float]:
        """Extract risk level from change request data using configurable field mappings"""
        try:
            if not change_requests:
                return None, 0.0

            # Get configuration from config manager
            config = self.config_manager.load_config() if self.config_manager else None
            if not config or not config.sql_config.enabled:
                return None, 0.0

            # Extract configuration values
            field_mappings = config.sql_config.field_mappings
            value_mappings = config.sql_config.value_mappings
            risk_config = config.sql_config.risk_analysis_config

            # Initialize risk scores with configured risk levels
            supported_risks = risk_config.get(
                "supported_risk_levels", ["CRITICAL", "HIGH", "MEDIUM", "LOW"]
            )
            risk_scores = {risk: 0.0 for risk in supported_risks}
            total_weight = 0.0

            for cr in change_requests:
                weight = 1.0

                # Get field names from configuration
                risk_field = field_mappings.get("risk_level_field", "risk_level")
                priority_field = field_mappings.get("priority_field", "priority")
                category_field = field_mappings.get("category_field", "category")

                # Map change request risk_level directly if available
                if hasattr(cr, risk_field):
                    risk_value = getattr(cr, risk_field)
                    if risk_value:
                        risk_level = str(risk_value).upper()
                        if risk_level in risk_scores:
                            risk_scores[risk_level] += weight
                            total_weight += weight
                            continue

                # Map priority to risk level using configured mappings
                if hasattr(cr, priority_field):
                    priority_value = getattr(cr, priority_field)
                    if priority_value:
                        priority = str(priority_value).upper()
                        priority_mappings = value_mappings.get("priority_to_risk", {})
                        mapped_risk = priority_mappings.get(priority)

                        if mapped_risk and mapped_risk in risk_scores:
                            risk_scores[mapped_risk] += weight
                        else:
                            # Use default risk level for unknown priorities
                            default_risk = risk_config.get(
                                "default_risk_level", "MEDIUM"
                            )
                            if default_risk in risk_scores:
                                risk_scores[default_risk] += weight
                        total_weight += weight

                # Consider category for additional risk weighting using configured mappings
                if hasattr(cr, category_field):
                    category_value = getattr(cr, category_field)
                    if category_value:
                        category = str(category_value).upper()
                        category_mappings = value_mappings.get(
                            "category_risk_boost", {}
                        )
                        category_risk = category_mappings.get(category)

                        if category_risk and category_risk in risk_scores:
                            category_weight = risk_config.get("category_weight", 0.3)
                            risk_scores[category_risk] += category_weight
                            total_weight += category_weight

            if total_weight == 0:
                return None, 0.0

            # Normalize scores
            for risk in risk_scores:
                risk_scores[risk] /= total_weight

            # Find highest scoring risk level
            max_risk = max(risk_scores.keys(), key=lambda k: risk_scores[k])
            max_score = risk_scores[max_risk]

            # Only return if we have reasonable confidence
            confidence_threshold = risk_config.get("confidence_threshold", 0.3)
            if max_score >= confidence_threshold:
                # Calculate confidence using configured values
                base_confidence = risk_config.get("base_confidence", 0.6)
                max_confidence = risk_config.get("max_confidence", 0.9)
                confidence = min(max_confidence, base_confidence + max_score)

                self.logger.debug(
                    f"Change request risk analysis: {max_risk} from {len(change_requests)} CRs (confidence: {confidence:.2f})"
                )
                return max_risk, confidence

            return None, 0.0

        except Exception as e:
            self.logger.debug(f"Error extracting risk from change requests: {e}")
            return None, 0.0

    def _extract_risk_heuristic_simple(
        self, content: str, aggressiveness: str = "BALANCED"
    ) -> Optional[tuple[str, float]]:
        """Simple heuristic risk level extraction with confidence scoring adjusted for aggressiveness"""
        try:
            sections = [
                "Code Review Recommendation",
                "Impact Assessment",
                "Summary",
                "Recommendations",
            ]

            for section_name in sections:
                section_content = self.extract_section(content, section_name)
                if section_content:
                    section_lower = section_content.lower()

                    # Detect risk indicators
                    has_critical_indicators = (
                        "critical risk" in section_lower
                        or "security vulnerability" in section_lower
                        or "buffer overflow" in section_lower
                        or "memory corruption" in section_lower
                        or "race condition" in section_lower
                        or "deadlock" in section_lower
                    )

                    has_high_indicators = (
                        "high risk" in section_lower
                        or "critical" in section_lower
                        or "breaking change" in section_lower
                        or "major impact" in section_lower
                    )

                    has_medium_indicators = (
                        "medium risk" in section_lower
                        or "moderate" in section_lower
                        or "some impact" in section_lower
                    )

                    has_low_indicators = (
                        "low risk" in section_lower
                        or "minor" in section_lower
                        or "minimal impact" in section_lower
                        or "safe" in section_lower
                    )

                    # Adjust risk assessment based on aggressiveness
                    if has_critical_indicators:
                        if aggressiveness == "CONSERVATIVE":
                            return ("CRITICAL", 0.9)
                        elif aggressiveness == "BALANCED":
                            return ("CRITICAL", 0.8)
                        elif aggressiveness == "AGGRESSIVE":
                            return ("HIGH", 0.7)  # Downgrade CRITICAL to HIGH
                        else:  # VERY_AGGRESSIVE
                            return (
                                "HIGH",
                                0.6,
                            )  # Downgrade CRITICAL to HIGH (not MEDIUM)

                    elif has_high_indicators:
                        if aggressiveness == "CONSERVATIVE":
                            return ("HIGH", 0.8)
                        elif aggressiveness == "BALANCED":
                            return ("HIGH", 0.7)
                        elif aggressiveness == "AGGRESSIVE":
                            return ("MEDIUM", 0.6)  # Downgrade HIGH to MEDIUM
                        else:  # VERY_AGGRESSIVE
                            return ("MEDIUM", 0.5)  # Downgrade HIGH to MEDIUM (not LOW)

                    elif has_medium_indicators:
                        if aggressiveness == "CONSERVATIVE":
                            return ("MEDIUM", 0.8)
                        elif aggressiveness == "BALANCED":
                            return ("MEDIUM", 0.7)
                        elif aggressiveness == "AGGRESSIVE":
                            return ("LOW", 0.6)  # Downgrade MEDIUM to LOW
                        else:  # VERY_AGGRESSIVE
                            return ("LOW", 0.4)  # Downgrade MEDIUM to LOW

                    elif has_low_indicators:
                        return (
                            "LOW",
                            0.8,
                        )  # LOW stays LOW regardless of aggressiveness

            # Default fallback adjusted for aggressiveness
            if aggressiveness == "CONSERVATIVE":
                return ("MEDIUM", 0.4)
            elif aggressiveness == "BALANCED":
                return ("MEDIUM", 0.3)
            elif aggressiveness == "AGGRESSIVE":
                return ("LOW", 0.3)
            else:  # VERY_AGGRESSIVE
                return ("LOW", 0.2)

        except Exception as e:
            self.logger.debug(f"Error in simple heuristic risk extraction: {e}")
            return None

    def _vote_on_risk_simple(
        self, votes: dict, confidence_scores: dict, aggressiveness: str = "BALANCED"
    ) -> Optional[str]:
        """Aggressiveness-aware voting mechanism for risk level determination"""
        try:
            if not votes:
                return None

            if len(votes) == 1:
                # Single vote - return it if confidence is reasonable, but adjust for aggressiveness
                method, risk = next(iter(votes.items()))
                confidence = confidence_scores.get(method, 0.5)

                # Adjust confidence threshold based on aggressiveness
                min_confidence = (
                    0.3 if aggressiveness in ["CONSERVATIVE", "BALANCED"] else 0.2
                )

                if confidence >= min_confidence:
                    return self._adjust_risk_for_aggressiveness(risk, aggressiveness)
                else:
                    return self._get_default_risk_for_aggressiveness(aggressiveness)

            # Multiple votes - weighted by confidence with aggressiveness adjustment
            risk_scores = {"CRITICAL": 0.0, "HIGH": 0.0, "MEDIUM": 0.0, "LOW": 0.0}
            total_weight = 0.0

            for method, risk in votes.items():
                confidence = confidence_scores.get(method, 0.5)

                # Adjust confidence based on aggressiveness - more aggressive = lower confidence in high risks
                if aggressiveness == "AGGRESSIVE" and risk in ["CRITICAL", "HIGH"]:
                    confidence *= 0.7  # Reduce confidence in high-risk assessments
                elif aggressiveness == "VERY_AGGRESSIVE" and risk in [
                    "CRITICAL",
                    "HIGH",
                ]:
                    confidence *= (
                        0.4  # Significantly reduce confidence in high-risk assessments
                    )
                elif aggressiveness == "VERY_AGGRESSIVE" and risk == "MEDIUM":
                    confidence *= (
                        0.7  # Also reduce confidence in medium-risk assessments
                    )

                if risk in risk_scores:  # Only count valid risk levels
                    risk_scores[risk] += confidence
                    total_weight += confidence

            if total_weight > 0:
                for risk in risk_scores:
                    risk_scores[risk] /= total_weight

            # Find winning risk level
            winning_risk = max(risk_scores.keys(), key=lambda k: risk_scores[k])
            winning_score = risk_scores[winning_risk]

            # Adjust minimum confidence threshold based on aggressiveness
            min_confidence_threshold = {
                "CONSERVATIVE": 0.4,
                "BALANCED": 0.35,
                "AGGRESSIVE": 0.25,
                "VERY_AGGRESSIVE": 0.15,
            }.get(aggressiveness, 0.35)

            if winning_score < min_confidence_threshold:
                return self._get_default_risk_for_aggressiveness(aggressiveness)

            return self._adjust_risk_for_aggressiveness(winning_risk, aggressiveness)

        except Exception as e:
            self.logger.debug(f"Error in aggressiveness-aware risk voting: {e}")
            return self._get_default_risk_for_aggressiveness(aggressiveness)

    def _adjust_risk_for_aggressiveness(self, risk: str, aggressiveness: str) -> str:
        """Adjust risk level based on aggressiveness setting"""
        if aggressiveness == "CONSERVATIVE":
            return risk  # No adjustment for conservative
        elif aggressiveness == "BALANCED":
            return risk  # No adjustment for balanced
        elif aggressiveness == "AGGRESSIVE":
            # Downgrade high risks
            if risk == "CRITICAL":
                return "HIGH"
            elif risk == "HIGH":
                return "MEDIUM"
            return risk
        else:  # VERY_AGGRESSIVE
            # Moderately downgrade risks - still allow some variety
            if risk == "CRITICAL":
                return "HIGH"  # CRITICAL -> HIGH (not MEDIUM)
            elif risk == "HIGH":
                return "MEDIUM"  # HIGH -> MEDIUM (not LOW)
            # MEDIUM and LOW stay the same
            return risk

    def _get_default_risk_for_aggressiveness(self, aggressiveness: str) -> str:
        """Get default risk level based on aggressiveness when confidence is too low"""
        if aggressiveness == "CONSERVATIVE":
            return "MEDIUM"
        elif aggressiveness == "BALANCED":
            return "MEDIUM"
        elif aggressiveness == "AGGRESSIVE":
            return "LOW"
        else:  # VERY_AGGRESSIVE
            return "LOW"

    def _extract_risk_from_diff_complexity(
        self, document_record: "DocumentRecord", aggressiveness: str = "BALANCED"
    ) -> Tuple[Optional[str], float]:
        """Extract risk assessment from diff complexity analysis"""
        try:
            # Use provided aggressiveness or get from repository configuration
            repo_config = None
            if self.config_manager and hasattr(document_record, "repository_name"):
                try:
                    config = self.config_manager.load_config()
                    repo_config = config.get_repository_by_name(
                        document_record.repository_name
                    )
                    # Only override if not explicitly provided and repo has setting
                    if (
                        aggressiveness == "BALANCED"
                        and repo_config
                        and hasattr(repo_config, "risk_aggressiveness")
                    ):
                        aggressiveness = repo_config.risk_aggressiveness
                except Exception as e:
                    self.logger.debug(
                        f"Could not get repository aggressiveness setting: {e}"
                    )

            # Analyze diff complexity if we have changed paths
            if (
                hasattr(document_record, "changed_paths")
                and document_record.changed_paths
            ):
                diff_content = ""

                # Try to get actual diff content from repository
                if repo_config and hasattr(document_record, "revision"):
                    retrieved_diff = self._get_diff_from_repository(
                        repo_config, str(document_record.revision)
                    )
                    if retrieved_diff:
                        diff_content = retrieved_diff
                        self.logger.debug(
                            f"Retrieved diff content ({len(diff_content)} chars) for revision {document_record.revision}"
                        )
                    else:
                        self.logger.debug(
                            f"Could not retrieve diff content for revision {document_record.revision}, using file paths only"
                        )

                # Analyze with diff content (if available) and file paths
                metrics, confidence = self.diff_analyzer.analyze_diff(
                    diff_content or "", document_record.changed_paths
                )

                if confidence > 0.3:  # Only use if we have reasonable confidence
                    risk_level, risk_confidence = (
                        self.diff_analyzer.get_risk_assessment(metrics, aggressiveness)
                    )
                    return risk_level, risk_confidence

            return None, 0.0

        except Exception as e:
            self.logger.debug(f"Error in diff complexity risk extraction: {e}")
            return None, 0.0

    def _get_diff_from_repository(
        self, repo_config: "RepositoryConfig", revision: str
    ) -> Optional[str]:
        """Get diff content from repository backend"""
        try:
            # Import repository backends
            from repository_backends.base import RepositoryBackend
            from repository_backends.git_backend import GitBackend
            from repository_backends.svn_backend import SVNBackend

            # Select appropriate backend
            backend: RepositoryBackend
            if repo_config.type.lower() == "svn":
                backend = SVNBackend(
                    self.config_manager.load_config() if self.config_manager else None
                )
            elif repo_config.type.lower() == "git":
                backend = GitBackend(
                    self.config_manager.load_config() if self.config_manager else None
                )
            else:
                self.logger.debug(f"Unsupported repository type: {repo_config.type}")
                return None

            # Get diff content
            diff_content = backend.get_diff(repo_config, revision)
            return diff_content

        except Exception as e:
            self.logger.debug(f"Error retrieving diff from repository: {e}")
            return None

    def _get_repository_aggressiveness(
        self,
        document_record: Optional["DocumentRecord"] = None,
        override_aggressiveness: Optional[str] = None,
    ) -> str:
        """Get the risk aggressiveness setting for the repository

        Args:
            document_record: Optional document record for repository context
            override_aggressiveness: Optional aggressiveness level to use instead of configured level
        """
        # Use override if provided
        if override_aggressiveness:
            self.logger.debug(
                f"Using override aggressiveness: {override_aggressiveness}"
            )
            return override_aggressiveness

        if not document_record or not self.config_manager:
            return "BALANCED"

        try:
            # Repository lookup from config is deprecated - repositories are now in database
            # This should be updated to use the repository database instead
            self.logger.debug(
                "Repository lookup from config is deprecated - use repository database instead"
            )
        except Exception as e:
            self.logger.debug(f"Could not get repository aggressiveness: {e}")

        return "BALANCED"

    def _get_risk_thresholds(self, aggressiveness: str) -> Tuple[float, float, float]:
        """Get risk assessment thresholds based on aggressiveness level"""
        if aggressiveness == "CONSERVATIVE":
            # Very conservative - fewer CRITICAL assessments
            return 8.0, 5.0, 2.0  # critical, high, medium
        elif aggressiveness == "BALANCED":
            # Balanced approach - current settings
            return 6.0, 3.5, 1.5
        elif aggressiveness == "AGGRESSIVE":
            # More aggressive - more CRITICAL assessments for important codebases
            return 4.0, 2.5, 1.0
        else:  # VERY_AGGRESSIVE
            # Very aggressive - catch more potential issues
            return 2.5, 1.8, 0.8

    def _extract_metadata_with_llm(
        self, documentation: str, document_record: Optional["DocumentRecord"] = None
    ) -> tuple[Dict[str, Any], Optional[str]]:
        """Extract metadata using LLM with enhanced contextual prompts when available

        Returns:
            tuple: (metadata_dict, model_used)
        """
        if not self.ollama_client:
            self.logger.warning(
                "No Ollama client available for LLM metadata extraction"
            )
            return {}, None

        try:
            # Try to use enhanced prompts if available
            config = self.config_manager.load_config() if self.config_manager else None
            use_enhanced = config and getattr(config, "use_enhanced_prompts", True)

            if document_record and use_enhanced:
                try:
                    from prompt_templates import ContextAnalyzer, PromptTemplateManager

                    if not config:
                        raise Exception("No config available")

                    # Initialize enhanced prompt system
                    prompt_manager = PromptTemplateManager(config)
                    context_analyzer = ContextAnalyzer(config)

                    # Analyze the change context
                    context = context_analyzer.analyze_change_context(
                        document=document_record,
                        commit_message=getattr(document_record, "commit_message", None),
                    )

                    # Get enhanced prompts
                    system_prompt, user_prompt = (
                        prompt_manager.get_metadata_extraction_prompt(
                            documentation, context
                        )
                    )

                    self.logger.debug(
                        f"Using enhanced prompts for document {document_record.id}"
                    )

                except Exception as e:
                    self.logger.warning(
                        f"Failed to use enhanced prompts, falling back to basic: {e}"
                    )
                    # Fall back to basic prompts
                    system_prompt, user_prompt = self._get_basic_metadata_prompts(
                        documentation
                    )
            else:
                # Use basic prompts when no document context available or enhanced prompts disabled
                system_prompt, user_prompt = self._get_basic_metadata_prompts(
                    documentation
                )

            # Determine which specialized model to use - always use fresh config
            specialized_model = None
            actual_model_used = None

            # Always reload config to get the latest model settings
            if config:
                # Prefer risk assessment model, fall back to code review model
                risk_model = getattr(config, "ollama_model_risk_assessment", None)
                code_review_model = getattr(config, "ollama_model_code_review", None)
                default_model = getattr(config, "ollama_model", None)
                specialized_model = risk_model or code_review_model

                if specialized_model:
                    self.logger.info(
                        f"🎯 MetadataExtractor using specialized model: {specialized_model}"
                    )
                    actual_model_used = specialized_model
                else:
                    actual_model_used = default_model
                    self.logger.info(
                        f"🎯 MetadataExtractor using default model: {default_model}"
                    )
            elif hasattr(self.ollama_client, "config") and self.ollama_client.config:
                # Fallback to ollama client config if no fresh config available
                risk_model = getattr(
                    self.ollama_client.config, "ollama_model_risk_assessment", None
                )
                code_review_model = getattr(
                    self.ollama_client.config, "ollama_model_code_review", None
                )
                specialized_model = risk_model or code_review_model

                if specialized_model:
                    self.logger.info(
                        f"🎯 MetadataExtractor using specialized model (fallback): {specialized_model}"
                    )
                    actual_model_used = specialized_model
                else:
                    actual_model_used = (
                        self.ollama_client.config.ollama_model
                        if self.ollama_client.config
                        else None
                    )

            response = self.ollama_client.call_ollama(
                user_prompt, system_prompt, model=specialized_model
            )

            if response:
                try:
                    # Clean up response and parse JSON
                    cleaned_response = response.strip()
                    if cleaned_response.startswith("```json"):
                        cleaned_response = cleaned_response[7:]
                    if cleaned_response.endswith("```"):
                        cleaned_response = cleaned_response[:-3]
                    cleaned_response = cleaned_response.strip()

                    return json.loads(cleaned_response), actual_model_used
                except json.JSONDecodeError as e:
                    self.logger.warning(
                        f"Failed to parse LLM metadata response as JSON: {e}"
                    )
                    return {}, actual_model_used

            return {}, actual_model_used
        except Exception as e:
            self.logger.error(f"Error extracting metadata with LLM: {e}")
            return {}, None

    def _get_basic_metadata_prompts(self, documentation: str) -> Tuple[str, str]:
        """Get basic metadata extraction prompts (fallback when enhanced prompts fail)"""

        system_prompt = """You are a metadata extraction assistant. Analyze the provided technical documentation and extract specific metadata fields.

Return ONLY a JSON object with these exact fields:
- code_review_recommended: true/false (whether code review is recommended)
- code_review_priority: "CRITICAL"/"HIGH"/"MEDIUM"/"LOW" (priority level if review is recommended, null if not recommended)
- code_review_confidence: 0.0-1.0 (confidence in code review recommendation)
- risk_level: "CRITICAL"/"HIGH"/"MEDIUM"/"LOW" (risk level of the changes)
- risk_confidence: 0.0-1.0 (confidence in risk assessment, where 1.0 = completely certain)
- documentation_impact: true/false (whether documentation needs updates)
- documentation_confidence: 0.0-1.0 (confidence in documentation impact assessment)
- reasoning: "Brief explanation of key factors that influenced the risk and priority decisions"

DOCUMENTATION IMPACT GUIDELINES - Be CONSERVATIVE:
- documentation_impact should be FALSE (no updates needed) for:
  * Internal refactoring, code cleanup, performance optimizations
  * Bug fixes that don't change behavior or APIs
  * Test additions, code formatting, style improvements
  * Internal implementation changes without user-facing impact
  * Private/internal method changes
- documentation_impact should be TRUE (updates needed) ONLY for:
  * New public APIs, endpoints, or user-facing features
  * Breaking changes that affect external users
  * New configuration options or environment variables
  * Changes to command-line interfaces or user workflows
  * Security changes that affect user procedures

CRITICAL risk level should be used SPARINGLY for:
- Confirmed security vulnerabilities, buffer overflows, memory corruption
- Confirmed race conditions, deadlocks in production systems
- Emergency production hotfixes for critical failures
- Data corruption or loss scenarios

HIGH risk level should be used for:
- Authentication/authorization system changes
- Database schema changes, data migrations
- Major API changes or breaking changes
- Performance-critical algorithm changes

MEDIUM risk level should be used for:
- Standard feature development and enhancements
- Security improvements (not fixing vulnerabilities)
- Bug fixes in non-critical functionality
- Configuration changes

LOW risk level should be used for:
- Documentation-only changes
- Code formatting, style improvements
- Test additions without production code changes

Be conservative with CRITICAL - it should represent <10% of all changes. Be precise and consistent. If you cannot determine a value, use null."""

        user_prompt = f"""Analyze this technical documentation and extract the metadata:

{documentation}

Return only the JSON object with the metadata fields."""

        return system_prompt, user_prompt
