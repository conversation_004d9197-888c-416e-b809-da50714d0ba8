#!/usr/bin/env python3
"""
Enhanced Notification System for RepoSense AI
Provides centralized event management and notification handling
"""

import logging
import time
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from models import Config, NotificationCategory, RepositoryConfig, User


class NotificationSeverity(Enum):
    """Severity levels for notifications"""

    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class NotificationEvent:
    """Represents a system event that can trigger notifications"""

    # Core event information
    category: NotificationCategory
    severity: NotificationSeverity
    title: str
    message: str

    # Optional context
    repository_id: Optional[str] = None
    user_id: Optional[str] = None  # User who triggered the event
    commit_info: Optional[Dict[str, Any]] = None  # Commit-related data

    # Event metadata
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: datetime = field(default_factory=datetime.now)
    event_id: str = field(default_factory=lambda: f"evt_{int(time.time() * 1000)}")

    # Processing flags
    processed: bool = False
    email_sent: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary for storage/serialization"""
        return {
            "event_id": self.event_id,
            "category": self.category.value,
            "severity": self.severity.value,
            "title": self.title,
            "message": self.message,
            "repository_id": self.repository_id,
            "user_id": self.user_id,
            "commit_info": self.commit_info,
            "metadata": self.metadata,
            "timestamp": self.timestamp.isoformat(),
            "processed": self.processed,
            "email_sent": self.email_sent,
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "NotificationEvent":
        """Create event from dictionary"""
        return cls(
            event_id=data.get("event_id", ""),
            category=NotificationCategory(data["category"]),
            severity=NotificationSeverity(data["severity"]),
            title=data["title"],
            message=data["message"],
            repository_id=data.get("repository_id"),
            user_id=data.get("user_id"),
            commit_info=data.get("commit_info"),
            metadata=data.get("metadata", {}),
            timestamp=datetime.fromisoformat(data["timestamp"]),
            processed=data.get("processed", False),
            email_sent=data.get("email_sent", False),
        )


class NotificationManager:
    """Centralized service for managing and emitting notification events"""

    def __init__(self, config: Config, email_service=None):
        self.config = config
        self.email_service = email_service
        self.logger = logging.getLogger(__name__)

        # Event handlers
        self.event_handlers: Dict[NotificationCategory, List[Callable]] = {}

        # Event storage (in-memory for now, could be extended to database)
        self.event_history: List[NotificationEvent] = []
        self.max_history_size = 1000

        # Statistics
        self.stats = {"events_emitted": 0, "emails_sent": 0, "errors": 0}

    def register_handler(
        self,
        category: NotificationCategory,
        handler: Callable[[NotificationEvent], None],
    ):
        """Register an event handler for a specific category"""
        if category not in self.event_handlers:
            self.event_handlers[category] = []
        self.event_handlers[category].append(handler)
        self.logger.debug(f"Registered handler for {category.value}")

    def emit_event(self, event: NotificationEvent) -> bool:
        """Emit a notification event and process it"""
        try:
            self.logger.info(f"Emitting event: {event.category.value} - {event.title}")

            # Add to history
            self._add_to_history(event)

            # Update statistics
            self.stats["events_emitted"] += 1

            # Process the event
            success = self._process_event(event)

            # Mark as processed
            event.processed = True

            return success

        except Exception as e:
            self.logger.error(f"Error emitting event {event.event_id}: {e}")
            self.stats["errors"] += 1
            return False

    def _process_event(self, event: NotificationEvent) -> bool:
        """Process an event by calling handlers and sending notifications"""
        success = True

        # Call registered handlers
        if event.category in self.event_handlers:
            for handler in self.event_handlers[event.category]:
                try:
                    handler(event)
                except Exception as e:
                    self.logger.error(f"Error in event handler: {e}")
                    success = False

        # Send email notifications if email service is available
        if self.email_service:
            try:
                email_success = self._send_email_notifications(event)
                if email_success:
                    event.email_sent = True
                    self.stats["emails_sent"] += 1
                else:
                    success = False
            except Exception as e:
                self.logger.error(f"Error sending email notifications: {e}")
                success = False

        return success

    def _send_email_notifications(self, event: NotificationEvent) -> bool:
        """Send email notifications for an event"""
        try:
            # Check if email service is available and configured
            if not self.email_service:
                self.logger.debug(
                    f"No email service available for event: {event.title}"
                )
                return True  # Not an error if no email service

            # Use the email service to send the notification
            success = self.email_service.send_notification(event)

            if success:
                self.logger.info(f"Email notification sent for event: {event.title}")
            else:
                self.logger.warning(
                    f"Failed to send email notification for event: {event.title}"
                )

            return success

        except Exception as e:
            self.logger.error(
                f"Error sending email notification for event {event.title}: {e}"
            )
            return False

    def _add_to_history(self, event: NotificationEvent):
        """Add event to history with size management"""
        self.event_history.append(event)

        # Trim history if it gets too large
        if len(self.event_history) > self.max_history_size:
            self.event_history = self.event_history[-self.max_history_size :]

    def get_recent_events(
        self, limit: int = 50, category: Optional[NotificationCategory] = None
    ) -> List[NotificationEvent]:
        """Get recent events, optionally filtered by category"""
        events = self.event_history

        if category:
            events = [e for e in events if e.category == category]

        return list(reversed(events[-limit:]))

    def get_statistics(self) -> Dict[str, Any]:
        """Get notification system statistics"""
        return {
            **self.stats,
            "history_size": len(self.event_history),
            "handlers_registered": sum(
                len(handlers) for handlers in self.event_handlers.values()
            ),
        }


# Event Creation Utilities
class EventFactory:
    """Factory class for creating common notification events"""

    @staticmethod
    def create_commit_event(
        commit_info: Dict[str, Any], repository_id: str, risk_level: str = "LOW"
    ) -> NotificationEvent:
        """Create a commit notification event"""
        severity = NotificationSeverity.INFO
        if risk_level == "HIGH":
            severity = NotificationSeverity.WARNING
        elif risk_level == "CRITICAL":
            severity = NotificationSeverity.ERROR

        return NotificationEvent(
            category=NotificationCategory.COMMITS,
            severity=severity,
            title=f"New commit in {commit_info.get('repository_name', 'repository')}",
            message=f"Commit {commit_info.get('revision', 'unknown')} by {commit_info.get('author', 'unknown')}",
            repository_id=repository_id,
            user_id=commit_info.get("author"),
            commit_info=commit_info,
            metadata={
                "risk_level": risk_level,
                "changed_paths": commit_info.get("changed_paths", []),
                "commit_message": commit_info.get("message", ""),
            },
        )

    @staticmethod
    def create_system_health_event(
        title: str,
        message: str,
        severity: NotificationSeverity = NotificationSeverity.WARNING,
    ) -> NotificationEvent:
        """Create a system health event"""
        return NotificationEvent(
            category=NotificationCategory.SYSTEM_HEALTH,
            severity=severity,
            title=title,
            message=message,
            metadata={"component": "system"},
        )

    @staticmethod
    def create_repository_event(
        title: str,
        message: str,
        repository_id: str,
        severity: NotificationSeverity = NotificationSeverity.INFO,
    ) -> NotificationEvent:
        """Create a repository management event"""
        return NotificationEvent(
            category=NotificationCategory.REPOSITORY_MGMT,
            severity=severity,
            title=title,
            message=message,
            repository_id=repository_id,
        )

    @staticmethod
    def create_security_alert(
        title: str,
        message: str,
        repository_id: Optional[str] = None,
        commit_info: Optional[Dict[str, Any]] = None,
    ) -> NotificationEvent:
        """Create a security alert event"""
        return NotificationEvent(
            category=NotificationCategory.SECURITY_ALERTS,
            severity=NotificationSeverity.ERROR,
            title=title,
            message=message,
            repository_id=repository_id,
            commit_info=commit_info,
            metadata={"security_alert": True},
        )

    @staticmethod
    def create_processing_event(
        title: str,
        message: str,
        repository_id: Optional[str] = None,
        success: bool = True,
    ) -> NotificationEvent:
        """Create a document processing event"""
        severity = NotificationSeverity.INFO if success else NotificationSeverity.ERROR

        return NotificationEvent(
            category=NotificationCategory.PROCESSING_STATUS,
            severity=severity,
            title=title,
            message=message,
            repository_id=repository_id,
            metadata={"processing_success": success},
        )

    @staticmethod
    def create_maintenance_event(
        title: str,
        message: str,
        severity: NotificationSeverity = NotificationSeverity.WARNING,
    ) -> NotificationEvent:
        """Create a maintenance/performance event"""
        return NotificationEvent(
            category=NotificationCategory.MAINTENANCE,
            severity=severity,
            title=title,
            message=message,
            metadata={"maintenance": True},
        )


# Convenience functions for common events
def emit_commit_notification(
    manager: NotificationManager,
    commit_info: Dict[str, Any],
    repository_id: str,
    risk_level: str = "LOW",
) -> bool:
    """Convenience function to emit a commit notification"""
    event = EventFactory.create_commit_event(commit_info, repository_id, risk_level)
    return manager.emit_event(event)


def emit_system_startup(manager: NotificationManager) -> bool:
    """Emit system startup notification"""
    event = EventFactory.create_system_health_event(
        "RepoSense AI Started",
        "RepoSense AI monitoring service has started successfully",
        NotificationSeverity.INFO,
    )
    return manager.emit_event(event)


def emit_system_shutdown(manager: NotificationManager) -> bool:
    """Emit system shutdown notification"""
    event = EventFactory.create_system_health_event(
        "RepoSense AI Stopped",
        "RepoSense AI monitoring service has been stopped",
        NotificationSeverity.WARNING,
    )
    return manager.emit_event(event)


def emit_database_error(manager: NotificationManager, error_message: str) -> bool:
    """Emit database connectivity error"""
    event = EventFactory.create_system_health_event(
        "Database Connection Error",
        f"Database connectivity issue: {error_message}",
        NotificationSeverity.CRITICAL,
    )
    return manager.emit_event(event)


def emit_repository_scan_complete(
    manager: NotificationManager,
    repository_id: str,
    revisions_processed: int,
    success: bool = True,
) -> bool:
    """Emit repository scan completion notification"""
    title = "Repository Scan Complete" if success else "Repository Scan Failed"
    message = f"Processed {revisions_processed} revisions"

    event = EventFactory.create_processing_event(title, message, repository_id, success)
    return manager.emit_event(event)
