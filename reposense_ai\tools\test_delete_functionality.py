#!/usr/bin/env python3
"""
Test script to verify delete functionality and diagnose caching issues.
This script helps identify if the duplicate documents issue is in the database or caching layer.
"""

import sys
import os
import sqlite3
from pathlib import Path

# Add the parent directory to the path so we can import modules
sys.path.insert(0, str(Path(__file__).parent.parent))

def test_database_directly():
    """Test the database directly using SQLite"""
    print("🔍 Testing database directly with SQLite...")
    
    db_path = "./data/reposense.db"
    if not os.path.exists(db_path):
        print(f"❌ Database file not found: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # Check if documents table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='documents'")
        table_exists = cursor.fetchone()
        
        if not table_exists:
            print("❌ Documents table does not exist")
            return False
        
        print("✅ Documents table exists")
        
        # Count total documents
        cursor.execute("SELECT COUNT(*) FROM documents")
        total_count = cursor.fetchone()[0]
        print(f"📊 Total documents in database: {total_count}")
        
        # Check for duplicates by repository_id + revision
        cursor.execute("""
            SELECT repository_id, revision, COUNT(*) as count 
            FROM documents 
            GROUP BY repository_id, revision 
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        duplicates = cursor.fetchall()
        
        if duplicates:
            print(f"❌ Found {len(duplicates)} duplicate repository+revision combinations:")
            for repo_id, revision, count in duplicates[:5]:  # Show first 5
                print(f"   {repo_id} revision {revision}: {count} entries")
                
                # Show details of the duplicates
                cursor.execute("""
                    SELECT id, processed_time, size, ai_model_used 
                    FROM documents 
                    WHERE repository_id = ? AND revision = ?
                    ORDER BY processed_time
                """, (repo_id, revision))
                details = cursor.fetchall()
                
                for i, (doc_id, processed_time, size, ai_model) in enumerate(details):
                    print(f"     [{i+1}] ID: {doc_id}, Processed: {processed_time}, Size: {size}, Model: {ai_model}")
        else:
            print("✅ No duplicate repository+revision combinations found")
        
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error testing database: {e}")
        return False

def test_document_service():
    """Test using the DocumentService class"""
    print("\n🔍 Testing using DocumentService...")
    
    try:
        from document_service import DocumentService
        from config_manager import ConfigManager
        
        # Initialize services
        config_manager = ConfigManager()
        doc_service = DocumentService(
            output_dir="/app/data/output",
            db_path="./data/reposense.db",
            config_manager=config_manager
        )
        
        # Get document count
        total_count = doc_service.get_document_count()
        print(f"📊 Total documents via DocumentService: {total_count}")
        
        # Get some documents
        documents = doc_service.get_documents(limit=10)
        print(f"📄 Retrieved {len(documents)} documents via DocumentService")
        
        if documents:
            print("📋 Sample documents:")
            for i, doc in enumerate(documents[:3]):
                print(f"   [{i+1}] {doc.repository_id} rev {doc.revision} - {doc.processed_time}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing DocumentService: {e}")
        return False

def test_clear_all_documents():
    """Test the clear all documents functionality"""
    print("\n🧹 Testing clear all documents functionality...")
    
    try:
        from document_service import DocumentService
        from config_manager import ConfigManager
        
        # Initialize services
        config_manager = ConfigManager()
        doc_service = DocumentService(
            output_dir="/app/data/output",
            db_path="./data/reposense.db",
            config_manager=config_manager
        )
        
        # Get count before
        count_before = doc_service.get_document_count()
        print(f"📊 Documents before clear: {count_before}")
        
        if count_before > 0:
            # Clear all documents
            cleared_count = doc_service.clear_all_documents()
            print(f"🗑️ Cleared {cleared_count} documents")
            
            # Get count after
            count_after = doc_service.get_document_count()
            print(f"📊 Documents after clear: {count_after}")
            
            if count_after == 0:
                print("✅ Clear all documents functionality is working correctly")
                return True
            else:
                print(f"❌ Clear all documents failed - {count_after} documents remain")
                return False
        else:
            print("ℹ️ No documents to clear")
            return True
        
    except Exception as e:
        print(f"❌ Error testing clear all documents: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 RepoSense AI Delete Functionality Test")
    print("=" * 50)
    
    # Change to the correct directory
    script_dir = Path(__file__).parent
    os.chdir(script_dir.parent)
    print(f"📁 Working directory: {os.getcwd()}")
    
    # Run tests
    tests_passed = 0
    total_tests = 3
    
    if test_database_directly():
        tests_passed += 1
    
    if test_document_service():
        tests_passed += 1
    
    if test_clear_all_documents():
        tests_passed += 1
    
    print("\n" + "=" * 50)
    print(f"🎯 Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ All tests passed - Delete functionality is working correctly")
        print("💡 If you're still seeing duplicates in the web interface, try:")
        print("   1. Click 'Force Hard Refresh' button")
        print("   2. Clear browser cache (Ctrl+Shift+Delete)")
        print("   3. Restart the web application")
    else:
        print("❌ Some tests failed - There may be issues with the delete functionality")
    
    return tests_passed == total_tests

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
